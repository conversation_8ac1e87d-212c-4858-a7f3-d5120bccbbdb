import { Component, DestroyRef, Inject, inject, signal } from '@angular/core';
import {
  KnowledgeSource,
  KnowledgeSourceConfigWebsiteScrapeConfigScrapedPage,
  ScrapeWebsiteRequestOptions,
} from '@vendasta/embeddings';
import { AiKnowledgeService } from '../../../ai-knowledge.service';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, UntypedFormArray } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { AiKnowledgeI18nModule } from '../../../assets/i18n/ai-knowledge-i18n.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'ai-knowledge-rescrape-pages',
  imports: [
    CommonModule,
    TranslateModule,
    AiKnowledgeI18nModule,
    MatCheckboxModule,
    ReactiveFormsModule,
    FormsModule,
    GalaxyFormFieldModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
    MatExpansionModule,
    MatDialogModule,
    MatButtonModule,
  ],
  standalone: true,
  providers: [AiKnowledgeService],
  templateUrl: './rescrape-pages.component.html',
  styleUrl: './rescrape-pages.component.scss',
})
export class RescrapePagesComponent {
  private readonly knowledgeService = inject(AiKnowledgeService);
  destroyRef = inject(DestroyRef);
  form: FormGroup;
  scrapedPages: KnowledgeSourceConfigWebsiteScrapeConfigScrapedPage[] = [];
  readonly isLoading = signal(true);
  scrapeAllControl = new FormControl(false);
  searchControl = new FormControl('');
  filteredItems: KnowledgeSourceConfigWebsiteScrapeConfigScrapedPage[] = [];

  constructor(
    public dialogRef: MatDialogRef<RescrapePagesComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { knowledgeSource: KnowledgeSource },
  ) {}

  ngOnInit(): void {
    if (!this.data?.knowledgeSource) {
      return;
    }

    this.scrapedPages = this.data?.knowledgeSource?.config?.websiteScrapeConfig?.scrapedPages || [];
    if (!this.data?.knowledgeSource || !this.scrapedPages) return;

    this.form = new FormGroup({
      scrapedPages: new FormArray([]),
    });
    const controls = this.scrapedPages?.map((_) => new FormControl(false));
    this.form.controls.scrapedPages = new FormArray(controls);
    this.form.controls.scrapedPages.valueChanges
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        map(() => this.form.markAsDirty()),
      )
      .subscribe();

    this.searchControl.valueChanges.subscribe((searchTerm) => {
      this.updateFilteredItems(searchTerm || '');
    });
    this.updateFilteredItems('');

    this.isLoading.set(false);
  }

  updateFilteredItems(searchTerm: string): void {
    if (searchTerm === '') {
      this.filteredItems = this.scrapedPages;
    }

    const lower = searchTerm.toLowerCase();
    this.filteredItems = this.scrapedPages.filter(
      (item) => item.title?.toLowerCase().includes(lower) || item.url?.toLowerCase().includes(lower),
    );
  }

  get scrapedPagesArray(): UntypedFormArray {
    return this.form.controls['scrapedPages'] as UntypedFormArray;
  }

  setAllScrapedPages(value: boolean): void {
    this.form.controls.scrapedPages = new FormArray(
      this.scrapedPages?.map((p) => {
        const training = p?.errorMessage ? false : value;
        return new FormControl(training);
      }),
    );
    this.form.markAsDirty();
  }

  getCheckboxIndex(item: KnowledgeSourceConfigWebsiteScrapeConfigScrapedPage): number {
    return this.scrapedPages.findIndex((i) => i && item && i.url === item.url);
  }

  getScrapedPagesCount(): number {
    return this.scrapedPages?.length || 0;
  }

  getSelectedPagesCount(): number {
    return this.scrapedPagesArray?.controls?.filter((control) => control.value)?.length || 0;
  }

  async submit(): Promise<string> {
    const urlsToRefresh: string[] = [];
    this.scrapedPagesArray.controls.forEach((pageControl, index) => {
      if (pageControl.value) {
        urlsToRefresh.push(this.scrapedPages[index].url);
      }
    });
    if (!this.scrapeAllControl.value && urlsToRefresh.length === 0) {
      throw new Error('no urls selected to refresh');
    }
    let options: ScrapeWebsiteRequestOptions = null;
    if (!this.scrapeAllControl.value && urlsToRefresh.length > 0 && urlsToRefresh.length < this.scrapedPages.length) {
      options = new ScrapeWebsiteRequestOptions({
        specificUrls: urlsToRefresh,
      });
    }

    if (this.data?.knowledgeSource) {
      const source = this.data?.knowledgeSource;
      await this.knowledgeService.rescrapeWebsite(source, options);
      this.form.markAsPristine();
      this.dialogRef.close(true);
      return;
    }
    throw new Error('No knowledge source');
  }

  close(): void {
    this.dialogRef.close(false);
  }

  protected readonly Math = Math;
  protected readonly parent = parent;
}
