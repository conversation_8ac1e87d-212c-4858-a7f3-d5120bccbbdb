@use 'design-tokens' as dt;

.clickable:hover {
  cursor: pointer;
}

.knowledge-icon {
  margin-right: dt.$spacing-3 !important;
}

.knowledge-source-title {
  glxy-badge {
    margin-left: dt.$spacing-2;
  }
}

.knowledge-source-title-link {
  color: dt.$blue;
}

.preview {
  max-width: 100%;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

.last-scraped {
  color: dt.$tertiary-font-color;
  font-size: dt.$font-preset-5-size;
}

.knowledge-source-list {
  height: auto !important;
  padding-bottom: dt.$spacing-2;
}
