import { animate, style, transition, trigger } from '@angular/animations';
import { BreakpointObserver } from '@angular/cdk/layout';
import { Overlay, OverlayConfig, OverlayRef } from '@angular/cdk/overlay';
import { CdkPortal } from '@angular/cdk/portal';
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Input,
  input,
  OnChanges,
  OnInit,
  signal,
  Signal,
  SimpleChanges,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { Capacitor } from '@capacitor/core';
import { CustomUserDropdownItem, IdentifiableCustomUserDropdownItem } from '@galaxy/atlas';
import { TranslateService } from '@ngx-translate/core';
import { DropdownItem, PinnedItem, SideNavigationItem } from '@vendasta/atlas';
import { GalaxyNavControlService } from '@vendasta/galaxy/nav';
import { combineLatest, Observable, ReplaySubject } from 'rxjs';
import { map, shareReplay, startWith, tap } from 'rxjs/operators';
import { BusinessNavConfigService } from '../config.service';
import { SidebarService } from '../core-components/sidebar/sidebar.service';
import { BusinessNavDataService } from '../data.service';
import { SalesInfoModalComponent } from '../modals/sales-info/sales-info.modal.component';
import { PinsService } from '../pins.service';
import { GalaxyTheme, StyleService } from '@vendasta/galaxy/style-service';
import { AUTO_HIDE_NAV_MAX_WIDTH_DEFAULT } from '@vendasta/galaxy/nav/src/constants';
import { signOutLabel, stopImpersonatingLabel } from '../globals';

export const BUSINESS_APP_SERVICE_PROVIDER_ID = 'VBC';

type themingValues = {
  logoUrl: string;
  isDarkMode: boolean;
};

@Component({
  selector: 'bc-business-navbar',
  templateUrl: './navigation.component.html',
  styleUrls: ['./navigation.component.scss'],
  animations: [
    trigger('animateNav', [
      transition(':enter', [
        style({ transform: 'translateX(-100%)' }),
        animate('0.15s ease-in-out', style({ transform: 'translateX(0%)' })),
      ]),
      transition(':leave', [
        style({ transform: 'translateX(0%)' }),
        animate('0.15s ease-in-out', style({ transform: 'translateX(-100%)' })),
      ]),
    ]),
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class NavigationComponent implements OnInit, OnChanges {
  @Input({ transform: booleanAttribute }) hideCenters: boolean;
  @Input({ transform: booleanAttribute }) hideNotifications: boolean;
  disableNavigation = input(false, { transform: booleanAttribute });
  @Input({ transform: booleanAttribute }) alwaysShowMenuToggle: boolean;
  @Input({ transform: booleanAttribute }) showLanguageSelector = true;
  protected readonly portal = viewChild(CdkPortal);

  protected readonly partnerId = input<string>();
  protected readonly accountId = input<string>();
  protected readonly marketId = input<string>();
  protected readonly brandPath = input<string>();
  protected readonly serviceProviderId = input<string>();
  protected readonly defaultURL = input<string>();
  protected readonly isPrimaryNav = input(false, { transform: booleanAttribute });
  protected readonly overrideSignOutText = input<string>();

  private readonly sidebarService = inject(SidebarService);
  private readonly cfgService = inject(BusinessNavConfigService);
  private readonly apiService = inject(BusinessNavDataService);
  private readonly translate = inject(TranslateService);
  private readonly matIconRegistry = inject(MatIconRegistry);
  private readonly domSanitizer = inject(DomSanitizer);
  private readonly dialog = inject(MatDialog);
  private readonly pinsService = inject(PinsService);
  private readonly overlay = inject(Overlay);
  private readonly navControlService = inject(GalaxyNavControlService);
  private readonly styleService = inject(StyleService);

  protected readonly overlayDisabled = signal(false);
  protected readonly showLegacyButton: Signal<boolean>;
  protected readonly showNavToggle: Signal<boolean>;
  protected dropdownItems$: Observable<CustomUserDropdownItem[]>;

  protected readonly businessAppBranding$ = this.apiService.businessAppBranding$;
  protected readonly coBrandingUrl$ = this.apiService.cobrandingLogoURL$.pipe(map((url) => url || null));
  protected readonly branding$: Observable<themingValues>;
  protected readonly businessCenterName$ = this.apiService.businessCenterName$;
  protected readonly salespersonPictureUrl$ = this.apiService.salespersonPictureUrl$;
  protected readonly partnerName$ = this.apiService.partnerName$;
  protected readonly salespersonName$ = this.apiService.salespersonName$;
  protected readonly exitLinkConfiguration$ = this.apiService.exitLinkConfiguration$;
  protected readonly disableProductSwitcher$ = this.apiService.disableProductSwitcher$;

  protected readonly navigationItems$ = combineLatest([this.apiService.navigationItems$, this.pinsService.pins$]).pipe(
    map(([items, pins]) => removeUnpinnedItems(items, pins)),
  );

  protected readonly isMobile = toSignal(
    inject(BreakpointObserver)
      .observe('(max-width: ' + AUTO_HIDE_NAV_MAX_WIDTH_DEFAULT + 'px)')
      .pipe(map((result) => result.matches)),
  );

  private readonly logoHeight$$ = new ReplaySubject<number>(1);
  protected readonly logoHeight$: Observable<number>;

  private readonly overlayRef: Signal<OverlayRef>;
  private showMenuToggle = toSignal(this.sidebarService.showMenuToggle$);

  protected readonly navIsOpen = this.navControlService.isOpen;

  constructor() {
    effect(() => {
      if (this.serviceProviderId() === BUSINESS_APP_SERVICE_PROVIDER_ID) {
        document.body.classList.add('new-nav');
      }
    });

    this.logoHeight$ = this.logoHeight$$.asObservable().pipe(
      tap((height: number) => {
        this.rememberLastLogoHeight(height);
      }),
      startWith(this.getLastLogoHeight()),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.showNavToggle = computed(() => {
      if (this.disableNavigation()) return false;
      return (this.showMenuToggle() && this.isMobile()) || !this.navIsOpen();
    });

    // Init the nav
    this.calcAlwaysShowMenuToggle();
    this.matIconRegistry.addSvgIconLiteral(
      'store_outlined',
      this.domSanitizer.bypassSecurityTrustHtml(
        `<svg><path d="M18.36 9l.6 3H5.04l.6-3h12.72M20 4H4v2h16V4zm0 3H4l-1 5v2h1v6h10v-6h4v6h2v-6h1v-2l-1-5zM6 18v-4h6v4H6z"/></svg>`,
      ),
    );

    effect(() => {
      this.refreshConfig(
        this.partnerId(),
        this.brandPath(),
        this.marketId(),
        this.accountId(),
        this.serviceProviderId(),
        this.defaultURL(),
      );
    });

    effect(() => {
      this.sidebarService.setDisabled(this.disableNavigation() === true);
    });

    this.showLegacyButton = computed(() => this.overlayDisabled() === false);

    combineLatest([this.cfgService.serviceProviderId$, this.apiService.disableBusinessNav$])
      .pipe(takeUntilDestroyed())
      .subscribe(([serviceProviderId, disableBusNav]) => {
        this.overlayDisabled.set(
          serviceProviderId === BUSINESS_APP_SERVICE_PROVIDER_ID ||
            (serviceProviderId !== BUSINESS_APP_SERVICE_PROVIDER_ID && disableBusNav),
        );
      });

    this.overlayRef = computed(() => {
      const isPortal = !!this.portal();
      const isPrimaryNav = this.isPrimaryNav();

      if (isPortal === true && isPrimaryNav === false) {
        const ovrRef = this.createOverlay();
        return ovrRef;
      }
    });

    this.branding$ = combineLatest([
      this.apiService.marketLogoUrl$,
      this.apiService.darkMarketLogoUrl$,
      this.styleService.activeTheme$,
    ]).pipe(
      map(([lightLogoUrl, darkLogoUrl, theme]) => {
        const displayDarkLogo = theme === GalaxyTheme.Dark && !!darkLogoUrl;
        if (!this.isPrimaryNav()) {
          return {
            isDarkMode: false,
            logoUrl: lightLogoUrl,
          };
        }
        return {
          isDarkMode: displayDarkLogo,
          logoUrl: displayDarkLogo ? darkLogoUrl : lightLogoUrl || '',
        };
      }),
      startWith({ isDarkMode: false, logoUrl: '' }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private createOverlay(): OverlayRef {
    const config: OverlayConfig = new OverlayConfig({
      positionStrategy: this.overlay.position().global().bottom().left().start(),
      direction: 'ltr',
      hasBackdrop: true,
      height: `100%`,
      width: '276px',
    });
    const overlayRef = this.overlay.create(config);
    overlayRef.backdropClick().subscribe(() => {
      overlayRef.detach();
    });
    return overlayRef;
  }

  ngOnInit(): void {
    this.dropdownItems$ = combineLatest([
      this.apiService.dropdownItems$,
      this.translate.onTranslationChange.pipe(startWith(null)),
    ]).pipe(
      map(([dropdownItems]) =>
        dropdownItems.map((item: DropdownItem) => {
          let label: string = this.translate.instant(item.translationId || item.label);

          if (!label && item.translationId == 'NAVIGATION.MENU.EDIT_PROFILE') {
            label = item.label || 'Edit Profile';
          }

          const i: IdentifiableCustomUserDropdownItem = {
            id: item.label,
            label: label,
          };

          // Certain routes in VBC do not use the full URL
          const routesUsingFullURL =
            item.label == signOutLabel ||
            item.label == stopImpersonatingLabel ||
            (item.translationId == 'NAVIGATION.MENU.EDIT_PROFILE' && !item.path);
          if (this.serviceProviderId().toUpperCase() === BUSINESS_APP_SERVICE_PROVIDER_ID && !routesUsingFullURL) {
            i.route = item.path;
          } else {
            i.url = item.url;
          }

          // If we are on a native platform, we need to use the oauth/logout route to logout
          if (item.label == signOutLabel && Capacitor.isNativePlatform()) {
            i.url = '/oauth/logout';
          }

          return i;
        }),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.alwaysShowMenuToggle) {
      this.calcAlwaysShowMenuToggle();
    }
  }

  protected logoLoaded(logo: { height: number }): void {
    window.setTimeout(() => {
      this.logoHeight$$.next(Math.min(logo.height, 120));
    }, 0);
  }

  protected openAssistanceModal(): void {
    this.dialog.open(SalesInfoModalComponent, {
      maxWidth: '100vw',
      maxHeight: '100vh',
      autoFocus: false,
    });
  }

  private rememberLastLogoHeight(height: number): void {
    localStorage.setItem('business-sidenav-logo-height', '' + height);
  }

  private getLastLogoHeight(): number {
    const t = localStorage.getItem('business-sidenav-logo-height');
    if (!t) {
      return 80;
    }
    return +t;
  }

  protected toggleMenu(): void {
    if (this.isPrimaryNav()) {
      this.navControlService.toggle();
    } else {
      if (!this.overlayRef() || !this.portal() || this.overlayDisabled()) return;

      if (!this.overlayRef().hasAttached()) {
        this.overlayRef().attach(this.portal());
      } else if (this.overlayRef().hasAttached()) {
        this.overlayRef().detach();
      }
    }
  }

  private refreshConfig(
    partnerId?: string,
    brandPath?: string,
    marketId?: string,
    accountId?: string,
    serviceProviderId?: string,
    defaultURL?: string,
  ): void {
    this.cfgService.initialize(partnerId, marketId, accountId, brandPath, serviceProviderId, defaultURL);
  }

  private calcAlwaysShowMenuToggle(): void {
    this.sidebarService.setShowMenuToggle(this.alwaysShowMenuToggle === true);
  }

  protected trackBy(i: SideNavigationItem): string {
    const key =
      i.sideNavigationLink?.navigationId ||
      i.sideNavigationLink?.translationId ||
      i.sideNavigationContainer?.navigationId ||
      i.sideNavigationContainer?.translationId ||
      i.sideNavigationSection?.translationId;

    return key;
  }
}

export function removeUnpinnedItems(items: SideNavigationItem[], pins: PinnedItem[]): SideNavigationItem[] {
  return items.filter((item) => {
    if (!(item.sideNavigationLink || item.sideNavigationContainer)) return true;
    const relevantItem = item.sideNavigationLink || item.sideNavigationContainer;
    return !relevantItem.pinnable || pins?.find((pin) => pin.navigationId === relevantItem.navigationId);
  });
}
