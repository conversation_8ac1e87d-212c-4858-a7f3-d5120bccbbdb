{"PAYMENT_FORM": {"PAY_INVOICE": "Pay invoice", "LATEST_PROCESSING_ERROR": {"NO_ACCOUNT": "The bank account provided is closed", "INSUFFICIENT_FUNDS": "Insufficient funds in the bank account provided", "DEBIT_NOT_AUTHORIZED": "Debit payment was unauthorized for the back account provided", "UNKNOWN": "An error occurred processing payment for the back account provided"}}, "PAYMENTS_ONBOARDING": {"CTA": {"TITLE": "Receive payments from your customers", "SUBTITLE": "Set up Vendasta Payments", "DESCRIPTION": {"PAYOUTS_PAGE": "Once you've started collecting payments using Vendasta Payments, your balance is paid out regularly. Use this page to view your current balance and the amount, status, and expected arrival date of payouts."}}, "UNSUPPORTED_REGION": {"TITLE": "Accept payments through the platform", "DESCRIPTION": "Contact us to determine if Vendasta Payments is available in your area and start accepting payments from your customers", "CONTACT_US": "Contact us"}}, "INVOICE": {"INVOICE_FROM": "Invoice from", "BILLED_TO": "Billed to", "INVOICE": "Invoice", "INVOICE_PAID": "INVOICE PAID", "INVOICE_PROCESSING": "Processing payment", "INVOICE_PROCESSING_DETAILS": "You will be notified once the payment is confirmed, which may take up to 5 days. If the payment is confirmed after the due date it will still be considered as on time.", "PAYMENT_FAILED": "Payment attempt failed", "VOIDED": "VOIDED", "DOWNLOAD_AS": "Download as", "DOWNLOAD_RECEIPT": "Download receipt", "DOWNLOAD_INVOICE": "Download invoice", "DESCRIPTION": "Description", "QUANTITY": "Quantity", "PRICE": "Price", "TAX": "Tax", "TOTAL": "Total", "SUBTOTAL": "Subtotal", "AMOUNT_PAID": "Amount paid", "AMOUNT_DUE": "Amount due", "DISCOUNT": "Discount", "DISCOUNTS": "Discounts", "CREDITS_APPLIED": "Credits applied", "ADJUSTED_INVOICE_AMOUNT": "Adjusted invoice amount", "REFUND": "Refund", "QUESTIONS": "If you have any questions, contact", "POWERED_BY": "Powered by", "PARTNERS_WITH": "partners with Stripe to provide secure invoicing and payment processing.", "SCHEDULED_TO_SEND": "Scheduled to send on {{date}}", "ON": "on", "TABLE": {"EXPORT": "Export as CSV", "TOTAL": "Total", "INVOICE_STATUS": "Invoice Status", "NUMBER": "Number", "PAYMENT_STATUS": "Payment Status", "ACCOUNT": "Account", "DUE": "Due", "ISSUED": "Issued", "COLLECTION_METHOD": "Collection Method", "ACCOUNT_ACTIONS": {"VIEW_ACCOUNT": "View account", "EDIT_SETTINGS": "Edit billing settings"}, "ACTIONS": {"EDIT": "Edit invoice", "DOWNLOAD_RECEIPT": "Download receipt", "DOWNLOAD_INVOICE": "Download invoice", "DELETE_DRAFT": "Delete draft", "CHANGE_STATUS": "Change invoice status", "DUPLICATE": "Duplicate invoice", "COPY_PAYMENT_LINK": "Copy payment link"}, "STATUS_CHANGE_SUCCESS": "Successfully changed invoice status", "ERRORS": {"STATUS_CHANGE_FAILED": "Failed to change invoice status", "INVALID_STATUS": "Can only void invoices with due status", "PAYMENT_PENDING": "Cannot void invoice with pending payment"}, "COLUMNS": {"TOTAL": "Total", "INVOICE_STATUS": "Invoice status", "NUMBER": "Number", "PAYMENT_STATUS": "Payment status", "DUE": "Due", "ISSUED": "Issued", "COLLECTION_METHOD": "Collection method", "ACTIONS": "Actions", "CUSTOMER": "Customer"}, "FILTERS": {"ALL_INVOICES": "All invoices", "DRAFT": "Draft", "DUE": "Due", "PAST_DUE": "Past due", "PAID": "Paid", "VOID": "Void", "ISSUED_START": "Issued start: {{date}}", "ISSUED_END": "Issued end: {{date}}", "CREATED_START": "Created start: {{date}}", "CREATED_END": "Created end: {{date}}", "NUMBER": "Number", "ISSUED_DATE": "Issued date", "CREATED_DATE": "Created date", "PAYMENT_STATUS": "Payment status", "STATUS": "Status", "SUBSCRIPTION_RENEWALS": "Subscription renewals", "RECURRING_INVOICE": "Recurring invoice", "MANUAL": "Manual", "COLLECTION_METHOD": "Collection method", "AUTOMATICALLY_CHARGE": "Automatically charge", "SEND_EMAIL": "Send email", "MANUALLY_COLLECT": "Manually collect", "SUCCESS": "Success", "FAILED": "Failed", "PENDING": "Pending", "INVOICE_NUMBER_HASH": "Invoice #"}, "SNACK_BAR": {"PAYMENT_LINK_COPIED": "Payment link copied to clipboard", "FAILED_TO_GENERATE": "Failed to generate payment link"}, "TOOLTIP": {"INVOICE_FROM_SUBSCRIPTION": "Invoice generated from subscription renewal on the account", "INVOICE_FROM_RECURRING": "Invoice generated from a recurring invoice"}}, "DELETE_INVOICE_MODAL": {"TITLE": "Delete invoice {{invoiceNumber}}?", "SUBTITLE": "This action cannot be undone.", "DELETE_INVOICE": "Delete invoice"}, "PAYMENT_STATUSES": {"SUCCEEDED": "Succeeded", "FAILED": "Failed", "PENDING": "Pending"}}, "PAYMENT_METHOD_SELECTOR": {"ADD_PAYMENT_METHOD": "+ Add payment method", "EDIT": "Edit", "USE_AS_DEFAULT": "Use as default", "EXPIRES": "expires", "DEFAULT": "<PERSON><PERSON><PERSON>", "EXPIRED": "Expired", "CARD_TITLE": "Debit or credit card", "ACH_TITLE": "ACH direct debit transfer", "ACSS_TITLE": "Canadian pre-authorized debit", "PAYMENT_METHODS": "Payment methods", "NO_PAYMENT_METHODS": "No payment methods on file", "NEW_PAYMENT_METHOD": "New payment method", "ADD_CARD": "Add new card", "CANCEL": "Cancel", "SAVE": "Save", "ERROR_INCORRECT_NUMBER": "You have entered an incorrect card number", "ERROR_CARD_DECLINED": "Your card was declined", "ERROR_ADDING_PAYMENT_METHOD": "Error adding a new payment method", "ERROR_REMOVING_PAYMENT_METHOD": "Failed to remove payment method"}, "BANK_ACCOUNT": {"TITLE": "Receiving payouts", "SUBTITLE": "Connect your bank account to receive payouts", "REMOVE": "Remove", "REMOVED": "Removed account", "REMOVED_TOOLTIP": "The bank account that received this payout is no longer connected", "VIEW_PAYOUTS": "View payouts", "ADD_BANK_ACCOUNT": "Add bank account", "DEFAULT": "<PERSON><PERSON><PERSON>", "SET_DEFAULT": "Set default", "CANCEL_BUTTON": "Cancel", "ADD": {"TITLE": "Receiving bank account", "PAYOUT_DESCRIPTION": "Connect a bank account to receive payouts", "BANK_ACCOUNT": "Country of bank account", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "ACCOUNT_DETAILS": "Account details", "REFERENCE_CHEQUE": "You can reference a check for these numbers", "TRANSIT_NUMBER": "Transit number", "TRANSIT_NUMBER_REQUIRED": "Transit number is required", "TRANSIT_NUMBER_INVALID": "Transit number must be 5 digits", "INSTITUITON_NUMBER": "Institution number", "INSTITUITON_NUMBER_REQUIRED": "Institution number is required", "INSTITUITON_NUMBER_INVALID": "Institution number must be 3 digits", "ACCOUNT_NUMBER": "Account number", "ACCOUNT_NUMBER_REQUIRED": "Account number is required", "ACCOUNT_NUMBER_INVALID": "Account number must be {{required_digits}} digits", "ROUTING_NUMBER": "Routing number", "ROUTING_NUMBER_REQUIRED": "Routing number is required", "ROUTING_NUMBER_INVALID": "Routing number must be 9 digits", "IBAN": "IBAN", "IBAN_REQUIRED": "IBAN is required", "IBAN_INVALID": "IBAN must be {{required_length}} characters", "BSB": "BSB", "BSB_REQUIRED": "BSB is required", "BSB_INVALID": "BSB must be 6 characters", "SORT_CODE": "Sort code", "SORT_CODE_REQUIRED": "Sort code is required", "SORT_CODE_INVALID": "Sort code must be in the format '12-34-56' or '123456'", "ADD_BANK_ACCOUNT_BUTTON": "Add bank account", "SAVE_BANK_ACCOUNT_BUTTON": "Save bank account", "CANCEL_BUTTON": "Cancel", "SUCCESS_SNACK": "Successfully added bank account", "ERROR_SNACK": "Failed to add bank account", "COUNTRY": {"UNITED_STATES": "United States", "CANADA": "Canada", "CZECH_REPUBLIC": "Czech Republic", "AUSTRALIA": "Australia", "NEW_ZEALAND": "New Zealand", "UNITED_KINGDOM": "United Kingdom", "UNITED_ARAB_EMIRATES": "United Arab Emirates", "GERMANY": "Germany"}, "CURRENCY_OPTION": {"CAD": "CAD - Canadian dollar", "USD": "USD - U.S. dollar"}}}, "REFUND_DIALOG": {"TITLE": "Refund payment", "TO": "To", "DESCRIPTION": "Refund will appear on the account's statement within 5-10 business days. Fees from initial payment will not be refunded.", "ISSUE_CREDIT_NOTE": "Refunds can also be issued on a credit note for a paid invoice. If you wish to create a credit note instead, you can do so on the invoice.", "ISSUE_CREDIT_NOTE_CTA": "View invoice {{invoiceNumber}}", "WARNING_ACH_TITLE": "Refunding now may result in refunding twice", "DESCRIPTION_ACH": "ACH Direct Debit payments can be disputed for 60 days after payment. If the dispute is lost, the account will be refunded again. <a target=\"_blank\" href=\"https://docs.stripe.com/payments/ach-debit#disputed-payments\">Learn more about ACH Direct Debit refunds with Stripe.</a>", "WARNING_ACSS_TITLE": "Refunding now may result in refunding twice", "DESCRIPTION_ACSS": "Pre-authorized debit payments can be disputed 90 days after payment. If the dispute is lost, the account will be refunded again. <a target=\"_blank\" href=\"https://docs.stripe.com/payments/acss-debit#disputed-payments\">Learn more about PADs refunds with <PERSON><PERSON>.</a>", "BUTTON": "Refund", "AMOUNT": "Refund amount", "DATE": "Date", "BALANCE": "Vendasta Payments balance", "REASON": "Reason", "SELECT_REASON": "Select a reason", "PAYMENT_REFUNDED": "Payment refunded", "FAILED_TO_REFUND": "Failed to refund payment: {{err}}", "REFUND_CANNOT_EXCEED_AMOUNT": "Refund cannot be more than", "NEGATIVE_BALANCE_TITLE": "Negative balance", "NEGATIVE_BALANCE_DESCRIPTION": "The following refund will result in a negative balance in Vendasta payments. Future payments from your customer accounts will cover the negative balance.", "CONTACT_SUPPORT": "Contact support", "REFUND_MUST_BE_GREATER_THAN_ZERO": "Refund must be more than", "DUPLICATE": "Duplicate", "FRAUDULENT": "Fraudulent", "REQUESTED_BY_CUSTOMER": "Requested by customer"}, "REFUND_REASON": {"UNSET": "Unset", "DUPLICATE": "Duplicate", "FRAUDULENT": "Fraudulent", "REQUESTED_BY_CUSTOMER": "Requested by customer", "UNKNOWN": "Unknown"}, "PAYMENTS_PAGE": {"PAGE_TITLE": "Payments", "DISPUTE_ALERT": "You have open payment disputes that require action.", "DISPUTE_ACTION": "View open disputes", "MANAGE_DISPUTES_IN_STRIPE": "Manage disputes in Stripe", "AMOUNT": "Amount", "FEE": "Fee", "NET": "Net", "STATUS": "Status", "PAYMENT_METHOD": "Payment method", "DESCRIPTION": "Description", "CUSTOMER": "Account", "ORDER": "Order", "ORDER_REFUND": "Refund - Order", "INVOICE": "Invoice", "INVOICE_REFUND": "Refund - Invoice", "DISPUTE": "Dispute adjustment", "REFUND_FAILURE": "Refund failure adjustment", "PAYOUT_FAILURE": "Payout failure", "NO_PAYMENTS_TITLE": "You haven't received any payments yet", "NO_PAYMENTS_BODY": "You can receive payments from your customers via invoices you've sent, as well as the purchases they make via the Shopping Cart in your Store", "NO_PAYMENTS_PRIMARY_CTA": "Manage invoices", "NO_PAYMENTS_SECONDARY_CTA": "Manage store", "REFUND": "Refund", "ISSUE_CREDIT_NOTE": "Issue credit note", "CREATED": "Created", "DATE": "Date"}, "PAYMENTS_EXPORT": {"EXPORT_BUTTON": "Export as CSV", "DIALOG": {"TITLE": "Export payment data", "ERROR_MESSAGE": "Failed to build report", "PATIENCE_MESSAGE": "It may take a few minutes for the download to be ready after the export starts", "DATE_RANGE": "Date range", "DATE_RANGE_OPTIONS": {"LAST_30_DAYS": "Last 30 days", "LAST_60_DAYS": "Last 60 days", "LAST_90_DAYS": "Last 90 days", "LAST_MONTH": "Last month", "LAST_QUARTER": "Last quarter", "CUSTOM": "Custom"}, "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "ALL_CURRENCIES": "All currencies", "CURRENCY_HINT": "Only payments with the selected currency will be exported", "REPORT_DATA_HINT": "This export will also include information on payouts and Stripe account balance", "DATE_HINT": "Dates are based on Coordinated Universal Time (UTC)", "CANCEL": "Cancel", "EXPORT": "Export"}}, "PAYOUTS_PAGE": {"PAGE_TITLE": "Payouts", "CURRENT_BALANCE": {"TITLE": "Upcoming payouts", "TOOLTIP": "The total pending and available balances on the account.", "PAYOUT_SCHEDULE_DAILY": "Payouts scheduled daily ({{ delay }}-day rolling basis)", "PAYOUT_SCHEDULE_WEEKLY": "Payouts scheduled weekly ({{ delay }}-day rolling basis)", "PAYOUT_SCHEDULE_MONTHLY": "Payouts scheduled monthly ({{ delay }}-day rolling basis)", "PAYOUT_SCHEDULE_MANUAL": "Payouts processed manually"}, "IN_TRANSIT": {"TITLE": "In transit", "TOOLTIP": "The amount that has been paid and is in transit to your bank account", "NEXT_PAYOUT": "Next arrival:"}, "RETAIL_STATUS_ALERT": {"ACTION_TITLE": "View Vendasta Payments"}, "DEFER_TO_STRIPE": {"TITLE": "Manage payouts in Stripe", "SUBTITLE": "View and manage your payout information in your connected Stripe account", "CTA": "Go to my Stripe account", "TABLE": "Payouts received will appear in your Stripe account connected to Vendasta Payments"}, "COLUMNS": {"DATE": "Date", "STATUS": "Status", "BANK": "Bank", "AMOUNT": "Amount", "VIEW": "View"}}, "PAYMENT_PAGE": {"PAGE_TITLE": "Payment", "PAYMENT_DETAILS": "Payment details", "ACCEPT_DISPUTE": "Accept dispute", "PROVIDE_EVIDENCE": "Provide evidence", "VIEW_EVIDENCE": "View evidence", "REFUND": "Refund", "NEEDS_RESPONSE": "Needs response", "NEEDS_RESPONSE_DESCRIPTION": "Deal with dispute by", "GENERAL_ERROR": "Sorry, something went wrong", "BUSINESS_NOT_FOUND": "Business does not exist", "ERROR_TITLE": "Failed to get payment", "NOT_FOUND": "Payment does not exist", "DATE": "Date", "AMOUNT": "Amount", "FEE": "Fee", "APPLICATION_FEE": "Application fee", "STRIPE_FEE": "Stripe fee", "NET": "Net", "STATUS": "Status", "SETTLED_DATE": "Settled date", "DESCRIPTION": "Description", "REFUNDED": "Refunded", "PAYMENT_METHOD": "Payment method", "FAILURE_REASON": "Failure reason", "RETRY_PAYMENT_DESCRIPTION": "To retry a failed payment, try charging the original invoice again.", "GO_TO_INVOICE": "Go to invoice"}, "PAYOUT_DETAILS_PAGE": {"PAGE_TITLE": "{{ payoutDate }} Payout", "PAYOUT_DETAILS_CARD_TITLE": "Payout details", "PAYMENT_RECORDS_CARD_TITLE": "Payment records", "BACK_BUTTON_TEXT": "Back to payouts", "FIELD_NAMES": {"STATUS": "Status", "BANK_ACCOUNT": "Bank", "AMOUNT": "Amount", "ESTIMATED": "Estimated", "COMPLETED": "Completed"}}, "PAYMENT_STATUS_BADGE": {"SUCCEEDED": "Succeeded", "FAILED": "Failed", "PENDING": "Pending", "DISPUTED": "Disputed", "REFUNDED": "Refunded", "PARTIALLY_REFUNDED": "Partially refunded", "DISPUTE_WARNING_NEEDS_RESPONSE": "Inquiry needs response", "DISPUTE_WARNING_UNDER_REVIEW": "Inquiry under review", "DISPUTE_WARNING_CLOSED": "Inquiry closed", "DISPUTE_NEEDS_RESPONSE": "Dispute needs response", "DISPUTE_UNDER_REVIEW": "Dispute under review", "DISPUTE_CHARGE_REFUNDED": "Charge refunded", "DISPUTE_LOST": "Di<PERSON><PERSON> lost", "DISPUTE_WON": "Dispute won", "UNKNOWN": "Unknown", "DISPUTE_DEADLINE_MISSED": "Dispute deadline missed", "DISPUTE_DUE_IN_HOURS": "{{hours}} hours to respond", "DISPUTE_DUE_IN_HOUR": "{{hours}} hour to respond", "DISPUTE_DUE_IN_DAYS": "{{days}} days to respond", "DISPUTE_DUE_IN_DAY": "{{days}} day to respond", "TOOLTIP": {"DISPUTE_WARNING_NEEDS_RESPONSE": "Some card networks initiate a preliminary inquiry phase before creating a formal dispute. Inquiries can be resolved without incurring a dispute fee by either providing satisfactory evidence that answers the dispute type for the inquiry or by issuing a full refund.", "DISPUTE_WARNING_UNDER_REVIEW": "Some card networks initiate a preliminary inquiry phase before creating a formal dispute. Evidence for this dispute was submitted during its inquiry phase.", "DISPUTE_WARNING_CLOSED": "Some card networks initiate a preliminary inquiry phase before creating a formal dispute. If an inquiry has been open for 120 days without escalation to a chargeback, the inquiry will be marked as closed."}}, "DISPUTES": {"TABLE": {"COLUMN_HEADERS": {"AMOUNT": "Amount", "STATUS": "Status", "REASON": "Reason", "CREATED": "Created", "RESPONSE_DUE": "Response due", "DESCRIPTION": "Description", "CUSTOMER": "Account", "RESOLVED": "Resolved"}}, "DISPUTE_STATUS": {"NEEDS_RESPONSE": "Dispute needs response", "UNDER_REVIEW": "Dispute under review", "LOST": "Di<PERSON><PERSON> lost", "WON": "Dispute won", "WARNING_CLOSED": "Inquiry closed", "WARNING_NEEDS_RESPONSE": "Inquiry needs response", "WARNING_UNDER_REVIEW": "Inquiry under review"}, "DISPUTE_REASON": {"BANK_CANNOT_PROCESS": "Bank cannot process", "CHECK_RETURNED": "Check returned", "CREDIT_NOT_PROCESSED": "Credit not processed", "CUSTOMER_INITIATED": "Customer initiated", "DEBIT_NOT_AUTHORIZED": "Debit not authorized", "DUPLICATE": "Duplicate", "FRAUDULENT": "Fraudulent", "GENERAL": "General", "INCORRECT_ACCOUNT_DETAILS": "Incorrect account details", "INSUFFICIENT_FUNDS": "Insufficient funds", "PRODUCT_NOT_RECEIVED": "Product not received", "PRODUCT_UNACCEPTABLE": "Product unacceptable", "SUBSCRIPTION_CANCELED": "Subscription canceled", "UNRECOGNIZED": "Unrecognized", "UNKNOWN": "Unknown"}}}