import { DatePipe } from '@angular/common';
import { PaymentService, Transaction } from '@galaxy/billing';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap } from 'rxjs/operators';
import { stripReferencePrefixFromPaymentDescriptions } from '../common';
import { CustomerData, CustomerDataService } from '../../customer/customer-data';
import { PagedListRequestInterface, PagedResponseInterface, PaginatedAPIInterface } from '@vendasta/galaxy/table';
import { GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';

/**
 * Filters are applied to the payments rows to reduce the result set.
 */
export interface TransactionFilters {
  /**
   * Filter by a payout identifier.
   */
  payoutId?: string;
  /**
   * Filter down to a specific customer identifier.
   */
  customerId?: string;
  /**
   * Filter to payments created on or after the date
   */
  createdDateGte?: Date;
  /**
   * Filter to payments created on or before the date
   */
  createdDateLte?: Date;
  /**
   * Filter to payments only
   */
  paymentsOnly?: boolean;
  /**
   * Filter by an invoice ID (only applicable to payments/charges)
   */
  invoiceId?: string;
}

export class TransactionsGalaxyPaginatedAPI implements PaginatedAPIInterface<Transaction> {
  private merchantId$$: BehaviorSubject<string> = new BehaviorSubject<string>('');

  constructor(
    private paymentService: PaymentService,
    private datePipe: DatePipe,
    private parentFilters: TransactionFilters,
    private customerDataService?: CustomerDataService,
  ) {}

  get(req: PagedListRequestInterface): Observable<PagedResponseInterface<Transaction>> {
    const { cursor = '', pageSize = 25 } = req.pagingOptions;
    const { filters = [] } = req;
    const merchantId = this.merchantId$$.getValue();

    const transactionFilters = this.convertGalaxyFilters(filters);

    if (!merchantId) {
      return of({
        data: [],
        pagingMetadata: {
          nextCursor: '',
          hasMore: false,
          totalResults: 0,
        },
      });
    }

    const payments$ = this.paymentService
      .listRetailTransactions(
        merchantId,
        cursor,
        pageSize,
        this.parentFilters?.payoutId,
        transactionFilters?.createdDateGte,
        transactionFilters?.createdDateLte,
        this.parentFilters?.paymentsOnly,
        this.parentFilters?.customerId,
        this.parentFilters?.invoiceId,
      )
      .pipe(
        catchError((e) => {
          console.error(e);
          return of({
            results: [],
            hasMore: false,
            nextCursor: '',
          });
        }),
        map((resp) => {
          resp.results = stripReferencePrefixFromPaymentDescriptions(resp.results);
          return resp;
        }),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

    const customerData$: Observable<CustomerData[]> = payments$.pipe(
      map((resp) => {
        return resp.results
          .map((p) => {
            if (p.payment) {
              return p.payment.customerId;
            }
            if (p.refund) {
              return p.refund.payment?.customerId;
            }
            return null;
          })
          .filter((id) => !!id);
      }),
      switchMap((ids) => {
        if (!ids || ids.length === 0) {
          return of([]);
        }
        return this.customerDataService?.getMulti(ids) || of([]);
      }),
      catchError((_) => {
        // in the event of an account group error, just return an empty list
        // sometimes the user doesn't have permission to access the account group
        return of([]);
      }),
    );

    return combineLatest([payments$, customerData$]).pipe(
      map(([resp, customerData]) => {
        const customers: { [customerId: string]: CustomerData } = {};
        customerData.forEach((customerData) => {
          if (!customerData) {
            return;
          }
          customers[customerData.customerId] = customerData;
        });

        const rows: Transaction[] = [];
        resp.results.forEach((p) => {
          if (p.payment) {
            const customer = customers[p.payment.customerId];
            rows.push(Transaction.fromRetailPayment(customer?.companyName || '', p.payment));
          } else if (p.refund) {
            const customer = customers[p.refund.payment?.customerId];
            rows.push(Transaction.fromRetailRefund(customer?.companyName, p.refund));
          } else if (p.adjustment) {
            rows.push(Transaction.fromAdjustment(p.adjustment));
          } else if (p.payoutFailure) {
            rows.push(Transaction.fromRetailPayout(p.payoutFailure, this.datePipe));
          }
        });

        return {
          data: rows,
          pagingMetadata: {
            nextCursor: resp.nextCursor,
            hasMore: resp.hasMore,
          },
        };
      }),
    );
  }

  private convertGalaxyFilters(galaxyFilters): TransactionFilters {
    const result: TransactionFilters = {};

    for (const filter of galaxyFilters) {
      switch (filter.fieldId) {
        case 'date-range':
          if (
            filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_BETWEEN &&
            filter.values?.[0]?.date &&
            filter.values?.[1]?.date
          ) {
            const startDate = new Date(filter.values[0].date);
            startDate.setHours(0, 0, 0, 0);
            const endDate = new Date(filter.values[1].date);
            endDate.setHours(23, 59, 59, 999);
            result.createdDateGte = startDate;
            result.createdDateLte = endDate;
          }

          break;
      }
    }

    return result;
  }

  public updateMerchantId(merchantId: string): void {
    if (merchantId !== this.merchantId$$.getValue()) {
      this.merchantId$$.next(merchantId);
    }
  }

  public reload(): void {
    this.merchantId$$.next(this.merchantId$$.getValue());
  }
}
