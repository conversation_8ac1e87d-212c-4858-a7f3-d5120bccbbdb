@use 'design-tokens' as *;

uikit-empty-state {
  margin: 80px auto;
}

.description-col,
.customer-col {
  flex-grow: 2;
  max-width: 200px;
}

va-badge {
  mat-icon {
    vertical-align: middle;
    height: 12px;
    width: 12px;
    font-size: $font-preset-4-size;
    margin-top: -3px;
  }
}

.right-align {
  text-align: right;
  white-space: nowrap;
}

.left-align {
  text-align: left;
}

th.mat-mdc-header-cell,
td.mat-mdc-cell,
td.mat-mdc-footer-cell {
  padding-left: $spacing-4;
  padding-right: $spacing-4;
}

th.mat-mdc-header-cell {
  @include text-preset-3--bold;
  color: $secondary-text-color;
  background-color: $primary-background-color;
}

td.mat-mdc-cell {
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: $font-preset-4-size !important;
}

.glxy-empty-state {
  margin-top: $spacing-4;
}

:host ::ng-deep {
  // Hide the totals since it isn't actually known.
  .mat-mdc-paginator-range-label {
    display: none;
  }

  .toolbar-actions {
    align-items: flex-end;
    margin-right: auto;
    width: 100%;

    .mat-mdc-form-field {
      max-height: 68px;
    }
  }
}

.toolbar {
  display: flex;
  flex-flow: wrap;
  padding: 0;

  .toolbar-actions {
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .datepicker {
    width: fit-content;
  }
}

.created-time {
  font-size: $font-preset-5-size;
  color: $secondary-text-color;
}

.amount {
  font-size: $font-preset-4-size;
}

.description-type {
  font-size: $font-preset-5-size;
  color: $secondary-text-color;
}

.payment-method {
  display: flex;
  align-items: center;
  font-size: $font-preset-5-size;

  .card-img {
    margin-right: $spacing-2;
    width: $spacing-5; // consistent widths regardless of icon
    text-align: center;
  }

  .bullets {
    font-weight: bold;
  }
}

.missing {
  color: $tertiary-text-color;
}

.clickable-row {
  cursor: pointer;
}
.clickable-row:hover {
  background: $row-hover-bg-color;
}

.table {
  overflow: hidden;
}
.table-border {
  border: 1px solid $border-color;
  border-radius: $default-border-radius;
}

.list-border {
  border-top: 1px solid $border-color;
  border-bottom: 1px solid $border-color;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

.remove-header-footer {
  display: none;
}

.remove-upper-border {
  border-top: none !important;
}

.empty-state-container {
  margin-bottom: $spacing-4;
}

.truncated-text {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
