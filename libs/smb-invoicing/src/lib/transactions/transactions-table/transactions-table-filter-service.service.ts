import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import {
  GalaxyFilterChipDependencies,
  GalaxyFilterDefinitionInterface,
  GalaxyFilterInterface,
  GalaxyFilterOperator,
  GalaxyFilterType,
} from '@vendasta/galaxy/filter/chips';

@Injectable()
export class TransactionsFilterService implements GalaxyFilterChipDependencies {
  constructor(private translateService: TranslateService) {}

  private filters: GalaxyFilterDefinitionInterface[] = [
    {
      fieldId: 'date-range',
      fieldName: this.translateService.instant('PAYMENTS_PAGE.DATE'),
      type: GalaxyFilterType.FILTER_TYPE_DATE,
      supportedOperators: [GalaxyFilterOperator.FILTER_OPERATOR_IS_BETWEEN],
    },
  ];

  listObjectFilters(searchTerm: string): Observable<GalaxyFilterDefinitionInterface[]> {
    if (!searchTerm) {
      return of(this.filters);
    }
    return of(this.filters.filter((filter) => filter.fieldName.toLowerCase().includes(searchTerm.toLowerCase())));
  }

  async getInitialAppliedFilters(): Promise<GalaxyFilterInterface[]> {
    return [
      {
        fieldId: 'date-range',
        filterId: 'date-range',
        operator: GalaxyFilterOperator.FILTER_OPERATOR_IS_BETWEEN,
        definition: {
          fieldId: 'date-range',
          type: GalaxyFilterType.FILTER_TYPE_DATE,
          supportedOperators: [GalaxyFilterOperator.FILTER_OPERATOR_IS_BETWEEN],
          fieldName: this.translateService.instant('PAYMENTS_PAGE.DATE'),
        },
      },
    ];
  }
}
