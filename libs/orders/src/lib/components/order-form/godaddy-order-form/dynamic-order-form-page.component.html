<glxy-page>
  <glxy-page-wrapper [widthPreset]="'narrow'">
    <ng-container *ngIf="app$ | async as app; else loading">
      <form [formGroup]="godaddyForm">
        <mat-card appearance="outlined" class="order-form-section-header mat-card">
          <mat-card-content>
            <div class="product-overview">
              <div class="product-id">
                <va-icon
                  [iconUrl]="app?.sharedMarketingInformation?.iconUrl"
                  [iconName]="app?.sharedMarketingInformation?.name"
                  [diameter]="45"
                ></va-icon>
                <div class="product-id-text">
                  <h1 class="product-title">{{ app?.sharedMarketingInformation?.name }}</h1>
                  <span *ngIf="app?.sharedMarketingInformation?.tagline" class="tagline">
                    {{ app?.sharedMarketingInformation?.tagline }}
                  </span>
                </div>
              </div>
            </div>
            <mat-divider></mat-divider>
            <div class="form-step">
              <div class="info icon-and-message">
                <mat-icon>info</mat-icon>
                <p>
                  {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.SUPPORTED_EXTENSIONS' | translate }}
                </p>
              </div>
              <div class="domain-search">
                <div>
                  <input
                    #domainSearchText
                    type="text"
                    [placeholder]="'LIB_ORDERS.DYNAMIC_ORDER_FORM.SEARCH_FOR_A_DOMAIN' | translate"
                    formControlName="domain"
                    (submit)="submitDomain(domainSearchText.value)"
                  />
                  <button
                    class="search-button"
                    mat-raised-button
                    color="primary"
                    (click)="submitDomain(domainSearchText.value)"
                  >
                    <mat-icon>search</mat-icon>
                  </button>
                </div>
              </div>
              <div *ngIf="domainSearchResult$ | async as searchResult; else loadingSearch" class="domain-search-result">
                <div class="search-result-message">
                  <div *ngIf="searchResult.domain && searchResult.available" class="icon-and-message success">
                    <mat-icon>check_circle</mat-icon>
                    <p>
                      {{
                        'LIB_ORDERS.DYNAMIC_ORDER_FORM.DOMAIN_IS_AVAILABLE' | translate: { domain: searchResult.domain }
                      }}
                    </p>
                  </div>
                  <div *ngIf="searchResult.domain && !searchResult.available">
                    <div class="icon-and-message fail">
                      <mat-icon>error</mat-icon>
                      <p>
                        {{
                          'LIB_ORDERS.DYNAMIC_ORDER_FORM.DOMAIN_IS_NOT_AVAILABLE'
                            | translate: { domain: searchResult.domain }
                        }}
                      </p>
                      <br />
                    </div>
                    <div class="domain-error-help-text">
                      {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.SUGGESTED_DOMAINS' | translate }}
                    </div>
                    <div *ngIf="displaySuggestions" class="suggested-domains">
                      <div
                        *ngIf="domainSuggestionResults$ | async as suggestions; else loadingSuggestions"
                        class="suggested-domains-inner-wrap"
                      >
                        <mat-radio-group
                          *ngIf="suggestions?.length > 0; else noSuggestions"
                          formControlName="suggestedDomain"
                        >
                          <div class="suggestions-list">
                            <div *ngFor="let suggestion of suggestions">
                              <mat-radio-button
                                class="suggestion"
                                [value]="suggestion.domain"
                                (click)="suggestionClicked()"
                              >
                                {{ suggestion.domain }}
                              </mat-radio-button>
                            </div>
                          </div>
                        </mat-radio-group>
                        <ng-template #noSuggestions>
                          <div class="center-self">
                            {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.NO_SUGGESTED_DOMAINS' | translate }}
                          </div>
                        </ng-template>
                      </div>
                      <ng-template #loadingSuggestions>
                        <div class="center-self suggested-loading-icon">
                          <mat-spinner [diameter]="40"></mat-spinner>
                        </div>
                      </ng-template>
                    </div>
                  </div>
                  <div *ngIf="searchResult.parseError">
                    <div class="icon-and-message fail">
                      <mat-icon>error</mat-icon>
                      <p>{{ searchResult.parseError }}</p>
                      <br />
                    </div>
                    <p class="domain-error-help-text">
                      {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.SEARCH_FOR_ANOTHER_DOMAIN' | translate }}
                    </p>
                  </div>
                </div>
              </div>

              <ng-template #loadingSearch>
                <div class="domain-loading-container">
                  <mat-spinner class="domain-search-loading" [diameter]="20"></mat-spinner>
                  <div class="domain-search-loading-text">
                    {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.SEARCHING_FOR_DOMAIN' | translate }}
                  </div>
                </div>
              </ng-template>
            </div>
            <div>
              <div class="divider">
                <mat-divider></mat-divider>
              </div>
              <div class="terms-of-service-section">
                <mat-checkbox class="terms-of-service-checkbox" formControlName="tos"></mat-checkbox>
                {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.READ_AND_ACCEPTED_TERMS_OF_SERVICE' | translate }}
                <span class="terms-of-service-link" (click)="openTermsOfServiceDialog()">
                  {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.TERMS_OF_SERVICE' | translate }}
                </span>
                {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.FOR_THIS_PRODUCT' | translate }}
              </div>
            </div>
          </mat-card-content>
        </mat-card>
        <div class="button-container">
          <button [disabled]="!godaddyForm.valid" mat-raised-button color="primary" (click)="save()">
            {{ 'LIB_ORDERS.DYNAMIC_ORDER_FORM.CONFIRM' | translate }}
          </button>
        </div>
      </form>
    </ng-container>
    <ng-template #loading>
      <mat-spinner [diameter]="50"></mat-spinner>
    </ng-template>
  </glxy-page-wrapper>
</glxy-page>
