import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { catchError, interval, map, Observable, of, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { acceptedTLDs, DomainSearchResult, DynamicOrderFormService, parseDomain } from './dynamic-order-form.service';

import { UntypedFormBuilder, UntypedFormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Environment } from '@galaxy/core';
import { LineItemInterface } from '@vendasta/sales-orders';
import { OrderFormService } from '../order-form.service';
import { ProductInfoInterface } from '@vendasta/store';
import { Router } from '@angular/router';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { App, AppK<PERSON> } from '@vendasta/marketplace-apps';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { TermsOfServiceDialogComponent } from './terms-of-service-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { PackageService } from '@vendasta/marketplace-packages';

// Godaddy
export const godaddyAppId = {
  [Environment.DEMO]: 'MP-NNTJMBF6HPXR5XXC2JKCFWKJ64VZLBFQ',
  [Environment.PROD]: 'MP-4TMLZSQ5FMJQX5T75TPC43FQBWD2VXLB',
};

export const whitelabelDomainAppId = {
  [Environment.DEMO]: 'MP-ZZC3ZP6WSV3W5VL4HS5SKJZL44MPR5XK',
  [Environment.PROD]: 'MP-L3DHRP5Z2RP4QVWLLTQGJLLVSH232WR5',
};

export const DynamicOrderFormAppIds: string[] = [
  godaddyAppId[Environment.DEMO],
  godaddyAppId[Environment.PROD],
  whitelabelDomainAppId[Environment.DEMO],
  whitelabelDomainAppId[Environment.PROD],
];

@Component({
  selector: 'orders-godaddy-order-form',
  styleUrls: ['dynamic-order-form-page.component.scss'],
  templateUrl: './dynamic-order-form-page.component.html',
  providers: [DynamicOrderFormService, OrderFormService],
  standalone: false,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DynamicOrderFormComponent implements OnInit, OnDestroy {
  constructor(
    private dynamicOrderFormService: DynamicOrderFormService,
    private router: Router,
    private snackbarService: SnackbarService,
    private formBuilder: UntypedFormBuilder,
    public dialog: MatDialog,

    private packageService: PackageService,
    private appsService: AppPartnerService,
  ) {}

  @Input() lineItems: LineItemInterface[];
  @Input() partnerId: string;
  @Input() marketId: string;
  @Output() selectedDomain: EventEmitter<string> = new EventEmitter<string>();

  domainSearchResult$: Observable<DomainSearchResult>;
  domainSuggestionResults$: Observable<DomainSearchResult[]>;
  displaySuggestions: boolean;
  product: any;
  urlTracking: string;
  godaddyForm: UntypedFormGroup;
  private ngUnsubscribe$$ = new Subject<void>();

  lineItemInfo$: Observable<ProductInfoInterface>;
  app$: Observable<App>;

  @ViewChild('domainSearchText')
  domainSearchTextElement: ElementRef;

  ngOnInit(): void {
    this.godaddyForm = this.formBuilder.group({
      domain: [''],
      suggestedDomain: [''],
      tos: [false, Validators.requiredTrue],
    });
    this.godaddyForm.setValidators(this.InputProvidedValidation());

    // Remove radio selection on return to input box
    this.godaddyForm
      .get('domain')
      .valueChanges.pipe(takeUntil(this.ngUnsubscribe$$))
      .subscribe(() => {
        this.godaddyForm.get('suggestedDomain').setValue('');
        this.godaddyForm.get('domain').setErrors([{ invalidDomain: true }]);
      });

    const packageIds = this.lineItems.filter((item) => !!item?.packageId).map((item) => item.packageId);

    // Check packages for line items with dynamic order form app IDs and return those ids
    // We do not support multiple dynamic order form apps in a single single order. Only one will be used
    const packageItemIdWithDynamicForms$ = this.packageService.getMulti(packageIds).pipe(
      map((packages) => {
        return [].concat(
          ...packages.map(
            (pkg) =>
              pkg?.lineItems?.lineItems
                ?.filter((item) => DynamicOrderFormAppIds.indexOf(item?.id) !== -1)
                .map((item) => item.id) || [],
          ),
        );
      }),
      map((itemIds) => {
        return itemIds[0];
      }),
    );

    const lineItem = this.lineItems.find((item) => DynamicOrderFormAppIds.indexOf(item?.appKey?.appId) !== -1);

    if (lineItem) {
      this.app$ = this.appsService
        .getMulti([new AppKey({ appId: lineItem?.appKey?.appId })], this.partnerId, this.marketId, null, false)
        .pipe(
          map((apps: App[]) => {
            return apps[0];
          }),
        );
    } else {
      this.app$ = packageItemIdWithDynamicForms$.pipe(
        switchMap((id) => {
          return this.appsService.getMulti([new AppKey({ appId: id })], this.partnerId, this.marketId, null, false);
        }),
        map((apps: App[]) => {
          return apps[0];
        }),
      );
    }

    const selectSearchBoxSubscription = interval(100).subscribe(() => {
      if (this.domainSearchTextElement) {
        this.domainSearchTextElement.nativeElement.focus();
        selectSearchBoxSubscription.unsubscribe();
      }
    });

    this.domainSearchResult$ = of({
      domain: '',
      available: false,
      parseError: '',
    });

    this.urlTracking = this.router.url;
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe$$.next();
    this.ngUnsubscribe$$.complete();
  }

  suggestionClicked(): void {
    // suggested domains are valid choices
    this.godaddyForm.get('domain').setErrors(null);
  }

  submitDomain(inputDomain: string): void {
    if (inputDomain === '') {
      return;
    }
    const parseDomainResult = parseDomain(inputDomain);
    if (parseDomainResult.parseError !== '') {
      this.domainSearchResult$ = of({
        domain: '',
        available: false,
        parseError: parseDomainResult.parseError,
      });
      return;
    }
    this.displaySuggestions = true;
    this.domainSearchResult$ = this.dynamicOrderFormService.checkDomainAvailability(parseDomainResult.domain).pipe(
      tap((result) => {
        if (!result.available) {
          this.godaddyForm.get('domain').setErrors([{ invalidDomain: true }]);
          this.domainSuggestionResults$ = this.dynamicOrderFormService.getDomainSuggestions(
            parseDomainResult.domain,
            6,
            acceptedTLDs,
          );
        } else {
          this.godaddyForm.get('domain').setValue(parseDomainResult.domain);
          this.godaddyForm.get('domain').setErrors(null);
        }
        return;
      }),
      catchError((err) => {
        this.snackbarService.errorSnack('Failed to look up domain. Please try again.');
        return of(err);
      }),
    );
  }

  save(): void {
    const domain = this.godaddyForm.get('suggestedDomain').value || this.godaddyForm.get('domain').value;
    this.selectedDomain.emit(domain);
  }

  // Either a radio button has been selected OR a domain has been input
  InputProvidedValidation(): ValidatorFn {
    return (): ValidationErrors => {
      return this.godaddyForm.get('suggestedDomain').value || this.godaddyForm.get('domain').value
        ? null
        : { noInput: true };
    };
  }

  openTermsOfServiceDialog(): void {
    //TODO hook up terms of service
    this.dialog.open(TermsOfServiceDialogComponent, {
      width: '800px',
    });
  }
}
