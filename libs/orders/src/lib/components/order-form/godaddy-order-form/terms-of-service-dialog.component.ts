import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { DynamicOrderFormService } from './dynamic-order-form.service';

@Component({
  template: `
    @if (termsOfService$ | async; as terms) {
      <div class="terms-of-service" [innerHTML]="terms"></div>
    } @else {
      <div class="tos-loading-spinner">
        <mat-spinner [diameter]="50"></mat-spinner>
      </div>
    }
  `,
  styleUrls: ['./terms-of-service-dialog.component.scss'],
  providers: [DynamicOrderFormService],
  standalone: false,
})
export class TermsOfServiceDialogComponent {
  termsOfService$: Observable<string>;

  constructor(
    private dynamicOrderFormService: DynamicOrderFormService,
    public dialogRef: MatDialogRef<TermsOfServiceDialogComponent>,
  ) {
    this.termsOfService$ = this.dynamicOrderFormService.getGoDaddyTermsOfService('.com');
  }
}
