import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { FacebookApiService } from '@vendasta/facebook';
import { SocialPageFacebook } from '../interfaces';
import { FacebookMessengerService } from './facebook-messenger.service';
import { AccountGroupService } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { of, throwError } from 'rxjs';

describe('FacebookMessengerService', () => {
  let service: FacebookMessengerService;
  let mockFacebookApiService: any;
  let mockAccountGroupService: any;
  let mockTranslateService: any;
  let mockSnackbarService: any;

  beforeEach(() => {
    mockFacebookApiService = {
      facebookPageMessengerState: jest.fn().mockImplementation(() => {
        throw new Error('Failed to fetch Facebook page messenger state');
      }),
      assignFacebookPageOwner: jest.fn().mockReturnValue(of(true)),
    };

    mockAccountGroupService = {
      get: jest.fn().mockReturnValue(of(null)),
    };

    mockTranslateService = {
      instant: jest.fn().mockReturnValue('translated'),
    };

    mockSnackbarService = {
      openErrorSnack: jest.fn(),
      openSuccessSnack: jest.fn(),
    };

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        FacebookMessengerService,
        { provide: FacebookApiService, useValue: mockFacebookApiService },
        { provide: AccountGroupService, useValue: mockAccountGroupService },
        { provide: TranslateService, useValue: mockTranslateService },
        { provide: SnackbarService, useValue: mockSnackbarService },
      ],
    });
    service = TestBed.inject(FacebookMessengerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return false when connectPage fails', () => {
    const accountID = '123';
    const facebookPageData = { profileUrl: 'https://www.facebook.com/123', name: "Joe's Jerky" } as SocialPageFacebook;

    mockFacebookApiService.assignFacebookPageOwner.mockReturnValue(throwError(() => new Error('Failed')));

    service.connectPage(accountID, facebookPageData).subscribe((result) => {
      expect(result).toBe(false);
    });
  });

  it('should return true when connectPage succeeds', () => {
    const accountID = '123';
    const facebookPageData = { profileUrl: 'https://www.facebook.com/123', name: "Joe's Jerky" } as SocialPageFacebook;

    service.connectPage(accountID, facebookPageData).subscribe((result) => {
      expect(result).toBe(true);
    });
  });
});
