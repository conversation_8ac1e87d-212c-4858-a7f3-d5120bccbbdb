import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { FacebookApiService, TokenStatus } from '@vendasta/facebook';
import { Observable, catchError, firstValueFrom, from, map, of, shareReplay, tap } from 'rxjs';
import {
  AuthLinksResponse,
  SocialConnectionLinks,
  SocialConnections,
  SocialPageFacebook,
  VAPIResponse,
} from '../interfaces';
import { getMetadataFromError } from '@vendasta/shared';
import { AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';

export interface FacebookPageConnectionInfo {
  name: string;
  tokenStatus: TokenStatus;
  messengerEnabled: boolean;
  isAssignedPageOwner: boolean;
  statusCheckPermissionError?: boolean;
}

interface ConnectErrorDetails {
  i18nKey: string;
  organizationName?: string;
}

@Injectable({
  providedIn: 'any',
})
export class FacebookMessengerService {
  private readonly fbAPIService = inject(FacebookApiService);
  private readonly http = inject(HttpClient);
  private readonly accountGroupService = inject(AccountGroupService);
  private readonly translateService = inject(TranslateService);
  private readonly snackbarService = inject(SnackbarService);

  connectPage(accountID: string, facebookPageData: SocialPageFacebook): Observable<boolean> {
    const facebookPageID = facebookPageData.profileUrl?.split('id=')[1] ?? '';
    if (!facebookPageID) {
      this.snackbarService.openErrorSnack('INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.FAILED_TO_ENABLE');
      return of(false);
    }

    return this.fbAPIService
      .assignFacebookPageOwner({
        organizationId: accountID,
        facebookPageId: facebookPageID,
      })
      .pipe(
        map(() => null),
        catchError((err: unknown) => {
          if (err instanceof HttpErrorResponse) {
            if (err.status === 409) {
              return this.generate409ErrorMessageForEnableMessaging(err);
            }
          }

          return of({ i18nKey: 'INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.FAILED_TO_ENABLE' });
        }),
        tap((errorDetails: ConnectErrorDetails | null) => {
          if (errorDetails) {
            return void this.snackbarService.openErrorSnack(
              errorDetails.i18nKey,
              errorDetails.organizationName
                ? { interpolateTranslateParams: { organizationName: errorDetails.organizationName } }
                : undefined,
            );
          }
          this.snackbarService.openSuccessSnack('INBOX.SETTINGS.FACEBOOK_MESSENGER.SUCCESS.SUCCESSFULLY_ENABLED');
        }),
        map((errorDetails: ConnectErrorDetails | null) => !errorDetails),
      );
  }

  async redirectToFacebookAuth(accountID: string): Promise<void> {
    const protocol = window.location.protocol;
    const host = window.location.host;
    const baseUrl = `${protocol}//${host}`;
    const newUrl: URL = new URL(baseUrl);
    newUrl.pathname = `/account/location/${accountID}/settings/connections/target/inbox`;

    const authUrls = await firstValueFrom(this.getAuthUrls(accountID, newUrl));
    const serviceAuthURL = authUrls.facebook;
    const serviceName = 'Facebook';
    const oauthUrl = new URL(serviceAuthURL);
    const nextUrl = new URLSearchParams(oauthUrl.search).get('nextUrl');
    if (nextUrl !== null) {
      const nextUrlObject = new URL(nextUrl);
      nextUrlObject.searchParams.set('type', serviceName);
      oauthUrl.searchParams.set('nextUrl', nextUrlObject.toString());
    }
    window.location.href = oauthUrl.toString();
  }

  disconnectPage(accountID: string, facebookPageData: SocialPageFacebook): Observable<boolean> {
    const facebookPageID = facebookPageData.profileUrl?.split('id=')[1] ?? '';
    if (!facebookPageID) {
      return of(false);
    }

    return this.fbAPIService
      .disableMessengerForPage({
        organizationId: accountID,
        facebookPageId: facebookPageID,
      })
      .pipe(
        map(() => true),
        catchError(() => of(false)),
      );
  }

  getPagesForAccount(accountGroupId: string): Observable<SocialPageFacebook[]> {
    return this.getSocialProfileConnections(accountGroupId).pipe(
      map((connections) => {
        let fbPages: SocialPageFacebook[] = [];
        const fbUsers = connections.facebook ?? [];
        for (const fbUser of fbUsers) {
          fbPages = fbPages.concat(
            fbUser.pages.map((page: SocialPageFacebook) => {
              page.facebookUserID = fbUser.ssid.split('-')[1];
              return page;
            }),
          );
        }
        return fbPages;
      }),
    );
  }

  getPageInfo(
    facebookPageData: SocialPageFacebook,
    partnerID: string,
    accountGroupID: string,
  ): Observable<FacebookPageConnectionInfo> {
    const enabledForPage$ = this.fbAPIService.facebookPageMessengerState({
      organizationId: partnerID + ':' + accountGroupID,
      pageId: facebookPageData.profileUrl?.split('id=')[1],
      facebookUserId: facebookPageData.facebookUserID,
    });

    return enabledForPage$.pipe(
      map((enabledForPage) => {
        return {
          name: facebookPageData.name,
          tokenStatus: enabledForPage.tokenValid,
          messengerEnabled: !!enabledForPage.messengerEnabled,
          isAssignedPageOwner: !!enabledForPage.isPageOwner,
        };
      }),
      catchError((e: HttpErrorResponse) => {
        const statusCheckPermissionError = e.status === 403;
        return of({
          name: facebookPageData.name,
          tokenStatus: TokenStatus.UNKNOWN,
          messengerEnabled: false,
          isAssignedPageOwner: false,
          statusCheckPermissionError,
        });
      }),
    );
  }

  private getSocialProfileConnections(accountGroupId: string): Observable<SocialConnections> {
    return this.http
      .get<VAPIResponse>('/ajax/v1/get-social-profile-connections/', {
        params: {
          accountGroupId: accountGroupId,
        },
      })
      .pipe(
        map((response) => response.data),
        shareReplay({ refCount: true, bufferSize: 1 }),
        catchError((_) => of({} as SocialConnections)),
      );
  }

  private getAuthUrls(accountGroupId: string, nextUrl: URL): Observable<SocialConnectionLinks> {
    let newUrl = nextUrl.href;
    const urlParts = nextUrl.pathname.split('/');
    urlParts.forEach((part) => {
      if (part.startsWith('AG-')) {
        newUrl = newUrl.replace(part, accountGroupId);
      }
    });
    return this.http
      .get<AuthLinksResponse>('/ajax/v1/get-auth-urls/', {
        params: {
          accountGroupId: accountGroupId,
          nextUrl: newUrl,
        },
      })
      .pipe(
        map((response) => {
          return response.data;
        }),
      );
  }

  private async fetchOrganizationName(organizationId: string): Promise<string | undefined> {
    if (organizationId.startsWith('AG-')) {
      const accountGroup = await firstValueFrom(
        this.accountGroupService.get(organizationId, new ProjectionFilter({ napData: true })),
      );
      if (accountGroup) {
        return accountGroup.napData.companyName;
      }
    }
    return undefined;
  }

  private generate409ErrorMessageForEnableMessaging(err: HttpErrorResponse): Observable<ConnectErrorDetails> {
    const createGeneric409Error = {
      i18nKey: 'INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.MESSENGER_ALREADY_ENABLED_ON_OTHER_UNKNOWN_BUSINESS',
    };

    const metadata = getMetadataFromError(err);
    if (!('organizationId' in metadata && metadata.organizationId)) {
      return of(createGeneric409Error);
    } else {
      return from(this.fetchOrganizationName(metadata.organizationId)).pipe(
        map((organizationName) => {
          if (organizationName) {
            return {
              i18nKey: 'INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.MESSENGER_ALREADY_ENABLED_ON_OTHER_BUSINESS',
              organizationName,
            };
          }
          return createGeneric409Error;
        }),
        catchError(() => {
          return of(createGeneric409Error);
        }),
      );
    }
  }
}
