<div class="mat-container">
  <div class="header-container">
    <div class="logo">
      <img [src]="unlockModelLogoURL" alt="Logo" />
    </div>
    <div class="header-info-container">
      <div class="arr-title">{{ 'CONNECTION_SYNC_SETTINGS.AUTOMATED_REVIEW_REQUESTS' | translate }}</div>
      <div class="arr-subtitle">{{ unlockModelSubtitle }}</div>
    </div>
  </div>
  <div class="description-container">
    <mat-dialog-content>
      <div>
        <p class="description-short">{{ 'CONNECTION_SYNC_SETTINGS.PROMPT_CUSTOMERS' | translate }}</p>
        <p class="description-long">{{ dialogueBoxDetails.Description | translate }}</p>
      </div>
    </mat-dialog-content>
  </div>
  <div class="button-container">
    <mat-dialog-actions>
      <button class="description-button" mat-flat-button color="primary" (click)="redirectToSales()" mat-dialog-close>
        {{ dialogueBoxDetails.ButtonText | translate }}
      </button>
    </mat-dialog-actions>
  </div>
</div>
