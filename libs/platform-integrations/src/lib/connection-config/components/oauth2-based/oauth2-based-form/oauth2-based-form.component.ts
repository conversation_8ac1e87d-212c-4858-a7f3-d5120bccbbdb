import { ChangeDetectorRef, Component, EventEmitter, Inject, inject, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogActions,
  MatDialogClose,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { CardDataService } from '../../../../card-data.service';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
  PlatformIntegrationsI18nModule,
} from '@galaxy/platform-integrations/shared';
import { combineLatest, map, Observable, of, switchMap } from 'rxjs';
import { AppSettings } from '@vendasta/marketplace-apps/lib/_internal/objects/app-settings';
import { RmAppDetail } from '../../../../model/connection-integration-detail';
import { catchError } from 'rxjs/operators';
import { DialogBoxComponent } from '../../../../unlock-dialogue-box/unlock-dialogue-box.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ActionType, FieldType, FormConfig, SupportedContexts } from '@vendasta/platform-integrations';
import { MatRadioModule } from '@angular/material/radio';
import { ActionTypeComponent } from '../../action-type/action-type.component';

@Component({
  selector: 'platform-integration-oauth2-based-form',
  imports: [
    MatDialogActions,
    MatIconModule,
    MatDialogClose,
    MatDialogTitle,
    MatDialogContent,
    MatButtonModule,
    MatCardModule,
    MatSlideToggleModule,
    TranslateModule,
    ReactiveFormsModule,
    FormsModule,
    MatCheckboxModule,
    MatRadioModule,
    CommonModule,
    PlatformIntegrationsI18nModule,
    ActionTypeComponent,
  ],
  templateUrl: './oauth2-based-form.component.html',
  styleUrl: './oauth2-based-form.component.scss',
})
export class Oauth2BasedFormComponent implements OnInit {
  @Input() preconnectFormFields!: FormConfig;
  @Input() integrationDisplayName!: string;
  @Input() dialogRef!: MatDialogRef<any>;

  @Output() saveSyncSettingsEvent = new EventEmitter<any>();

  protected actionType = ActionType;
  protected fieldType = FieldType;

  syncSettingsForm!: FormGroup;
  private readonly cardService = inject(CardDataService);
  private readonly dialog = inject(MatDialog);
  private formBuilder = inject(FormBuilder);
  private cdRef = inject(ChangeDetectorRef);

  constructor(
    @Inject(PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$)
    public readonly partnerId$: Observable<string>,

    @Inject(PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$) public readonly marketId$: Observable<string>,
    @Inject(MAT_DIALOG_DATA) public data: any,

    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
  ) {}

  ngOnInit(): void {
    this.initializeFormGroup();
  }

  initializeFormGroup() {
    this.syncSettingsForm = this.formBuilder.group({});
    this.getSyncSettings()?.forEach((field) => {
      let control;
      if (field.fieldType === FieldType.FIELD_TYPE_SELECT || field.fieldType === FieldType.FIELD_TYPE_RADIO) {
        const values = field.defaultValue || (field.options ? field.options[0]?.value : '');
        control = this.formBuilder.control(values, field.required ? Validators.required : null);
      } else {
        control = this.formBuilder.control(field.defaultValue || '', field.required ? Validators.required : null);
      }
      this.syncSettingsForm.addControl(field.id, control);

      // Load the dependents input types.
      if (field.dependents) {
        field.dependents.forEach((dep) => {
          const depControl = this.formBuilder.control(
            { value: dep.defaultValue || '', disabled: !field.defaultValue },
            dep.required ? Validators.required : null,
          );
          this.syncSettingsForm.addControl(dep.id, depControl);
        });

        control.valueChanges.subscribe((value) => {
          field.dependents?.forEach((dep) => {
            const depControl = this.syncSettingsForm.get(dep.id);
            depControl?.setValue(dep.defaultValue);
            if (value) {
              depControl?.enable();
            } else {
              depControl?.disable();
            }
          });
        });
      }
    });

    if (this.preconnectFormFields?.fields) {
      this.initializeDependentControls();
    }
  }

  initializeDependentControls(): void {
    this.getSyncSettings().forEach((field) => {
      if (field.dependents) {
        const parentControl = this.syncSettingsForm.get(field.id);
        field.dependents.forEach((dep) => {
          const depControl = this.syncSettingsForm.get(dep.id);
          if (parentControl?.value) {
            depControl?.enable();
            depControl?.setValue(dep.defaultValue);
          } else {
            depControl?.disable();
          }
        });
      }
    });
  }

  getSyncSettings() {
    return this.preconnectFormFields?.fields?.filter((setting) => {
      return !(setting.id === 'reviewRequestData' && this.context === SupportedContexts.PI_CONTEXT_PARTNER);
    });
  }

  handleAction(action: ActionType) {
    if (action === this.actionType.ACTION_TYPE_CANCEL) {
      this.close();
    } else if (action === this.actionType.ACTION_TYPE_CONTINUE) {
      this.continue();
    }
  }
  close() {
    this.dialogRef.close();
  }
  continue() {
    this.saveSyncSettingsEvent.emit(this.syncSettingsForm.getRawValue());
    this.dialogRef.close();
  }

  protected readonly getAppSettings$: Observable<AppSettings> = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([partnerID, marketID]) => {
      return this.cardService.getRMAppSettings(partnerID, marketID);
    }),
  );

  protected readonly getRMAppDetailsMarketingPage$ = combineLatest([
    this.getAppSettings$,
    this.namespace$,
    this.partnerId$,
  ]).pipe(
    map(([rmAppSettings, namespace, partnerId]) => {
      const res = {
        hasRMPremiumActive: false,
        upgradeSetting: rmAppSettings,
        namespace: namespace,
        partnerId: partnerId,
      } as RmAppDetail;
      if (this.cardService.isRMActive()) {
        res.hasRMPremiumActive = true;
        return res;
      }
      this.syncSettingsForm.controls['reviewRequestData'].setValue(false);
      this.syncSettingsForm.controls['reviewRequestData'].disable();

      return res;
    }),
    catchError(() => {
      const defaultRes: RmAppDetail = {
        hasRMPremiumActive: false,
        upgradeSetting: {} as AppSettings,
      };
      return of(defaultRes);
    }),
  );

  handleButtonClick(rmAppDetails: RmAppDetail): void {
    this.dialog.open(DialogBoxComponent, {
      width: '463px',
      data: {
        namespace: rmAppDetails.namespace,
        partnerId: rmAppDetails.partnerId,
        rmAppSettings: rmAppDetails.upgradeSetting,
        integrationId: this.data['integrationID'],
      },
    });
  }
}
