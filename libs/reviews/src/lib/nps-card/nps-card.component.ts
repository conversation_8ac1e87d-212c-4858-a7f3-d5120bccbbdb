import { Component, DestroyRef, inject, Input, OnInit } from '@angular/core';
import { BadgeColor, GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { MatCardModule } from '@angular/material/card';
import { CommonModule } from '@angular/common';
import { GalaxyStatisticModule } from '@vendasta/galaxy/statistic';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { DateRange } from '@vendasta/uikit';
import { formatAsReadonlyPhone, PAGE_ROUTES, parsePhoneNumber } from '@galaxy/crm/static';
import { RouterLink } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ResendConfirmationModalComponent } from './resend-confirmation-modal/resend-confirmation-modal.component';
import { switchMap } from 'rxjs/operators';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateModule } from '@ngx-translate/core';
import { NetPromoterScoreAttribute, ProviderApiService, ReviewRequestApiService } from '@vendasta/reputation';
import { EMPTY, Observable } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { format } from 'date-fns';
import { ActivatedRoute } from '@angular/router';
import { NPSProviderComponent } from '../nps-provider/nps-provider.component';

export enum AppType {
  BusinessApp = 'BA',
  MultiLocation = 'ML',
}

export interface NPSData {
  netPromoterScoreId: string;
  accountGroupId: string;
  score: string;
  comment: string;
  scoreLeftTime: Date;
  created?: DateRange;
  updated?: DateRange;
  Name?: string;
  Email?: string;
  PhoneNumber?: string;
  CrmObjectId?: string;
  Address?: string;
  Providers?: Observable<string[]>;
  attributes: NetPromoterScoreAttribute[];
  providerIds?: string[];
}
@Component({
  selector: 'reviews-nps-card',
  imports: [
    MatCardModule,
    GalaxyStatisticModule,
    GalaxyBadgeModule,
    CommonModule,
    RouterLink,
    MatButtonModule,
    TranslateModule,
    NPSProviderComponent,
  ],
  templateUrl: './nps-card.component.html',
  styleUrl: './nps-card.component.scss',
})
export class NPSCardComponent implements OnInit {
  dialog = inject(MatDialog);
  snackbarService = inject(SnackbarService);
  reviewRequestApiService = inject(ReviewRequestApiService);
  private providerApiService = inject(ProviderApiService);

  route = inject(ActivatedRoute);

  destroyRef = inject(DestroyRef);

  @Input() review: NPSData;
  @Input() redirectToContact = true;
  @Input() enableResendNPSRequest: boolean;
  @Input() redirectMlToContact: string;

  formattedPhoneNumber: string;
  npsScoreBadgeColor: BadgeColor = 'red';
  scoreLeftTime: string;
  routeToContactProfile: string;
  paddingBottom: string;
  resentScoreLeftTime: string;
  parentNPSId: string;
  dialogRef: MatDialogRef<ResendConfirmationModalComponent>;
  isDetractor: boolean;

  protected providerNames$: Observable<string[]>;

  ngOnInit() {
    if (this.redirectMlToContact === AppType.MultiLocation) {
      const brandId = this.route.snapshot.params['brandname'];
      const contactId = this.review?.attributes?.find((item) => item.key === 'CRMContactID')?.value;
      const accountGroupID = this.review.accountGroupId;
      this.routeToContactProfile = `/account/brands/${brandId}/${PAGE_ROUTES.MULTI_LOCATION.CONTACTS.ROOT}/${accountGroupID}/${PAGE_ROUTES.CONTACT.SUBROUTES.PROFILE.replace(':crmObjectId', contactId)}`;
    } else if (this.redirectMlToContact === AppType.BusinessApp) {
      const accountGroupID = this.review.accountGroupId;
      const contactId = this.review?.attributes?.find((item) => item.key === 'CRMContactID')?.value;
      this.routeToContactProfile = `/account/location/${accountGroupID}/crm/contact/profile/${contactId}`;
    } else {
      this.routeToContactProfile = `../customers/${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.PROFILE.replace(':crmObjectId', this.review.CrmObjectId)}`;
    }

    if (this.review.PhoneNumber) {
      this.formattedPhoneNumber = this.formatPhoneNumber(this.review.PhoneNumber);
    }
    if (this.review.score) {
      this.npsScoreBadgeColor = this.getColor(this.review.score);
    }
    this.isDetractor = this.checkNPSDetractor(this.review?.score ?? 0);
    if (this.review.scoreLeftTime) {
      this.scoreLeftTime = this.getTimeAgo(this.review.scoreLeftTime);
    }
    if (this.review.comment) {
      this.paddingBottom = '0';
    } else {
      this.paddingBottom = '16px';
    }
    this.resentScoreLeftTime = this.review?.attributes?.find((item) => item.key === 'ResentTime')?.value;
    if (this.resentScoreLeftTime) {
      this.resentScoreLeftTime = format(parseISO(this.resentScoreLeftTime), 'MMM dd, yyyy');
    }
    this.parentNPSId = this.review?.attributes?.find((item) => item.key === 'ParentNPSID')?.value;
  }

  getColor(score: string | number): BadgeColor {
    // Convert the score to a string
    const scoreStr = String(score);
    if (scoreStr === '9' || scoreStr === '10') {
      return 'green';
    } else if (scoreStr === '7' || scoreStr === '8') {
      return 'yellow';
    } else {
      return 'red';
    }
  }

  checkNPSDetractor(score: string | number): boolean {
    const scoreNum = Number(score);
    return scoreNum >= 0 && scoreNum <= 6;
  }

  getTimeAgo(date: Date): string {
    return formatDistanceToNow(new Date(date), { addSuffix: true }).replace(/^about /, '');
  }

  formatPhoneNumber(phoneNumber: string): string {
    const parsedPhoneNumber = parsePhoneNumber(phoneNumber);
    return formatAsReadonlyPhone(parsedPhoneNumber);
  }

  resendNPSRequest(npsId: string, email: string) {
    this.dialogRef = this.dialog.open(ResendConfirmationModalComponent, {
      width: '500px',
      data: {
        emailAvailable: !!email,
      },
    });

    this.dialogRef
      .afterClosed()
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        switchMap((resend) => {
          if (resend) {
            return this.reviewRequestApiService.resendReview({ netPromoterScoreId: npsId });
          }
          return EMPTY;
        }),
      )
      .subscribe({
        next: () => {
          // Set resentScoreLeftTime after the API call is successful
          this.resentScoreLeftTime = format(new Date(), 'MMM dd, yyyy');
        },
        error: (error) => {
          this.snackbarService.openErrorSnack(error.error.message);
        },
      });
  }
}
