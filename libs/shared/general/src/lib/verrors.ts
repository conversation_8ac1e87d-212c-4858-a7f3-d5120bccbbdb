import { HttpErrorResponse } from '@angular/common/http';

const googleErrorDetailsBadRequestType = 'type.googleapis.com/google.rpc.BadRequest';
const googleErrorDetailsErrorInfoType = 'type.googleapis.com/google.rpc.ErrorInfo';

export interface FieldViolation {
  field: string;
  description: string;
}
export function getFieldViolationsFromError(err: HttpErrorResponse): FieldViolation[] {
  if (!err?.error?.details || !Array.isArray(err.error.details)) {
    return [];
  }
  return err.error.details
    .filter((detail: Record<string, unknown>) => detail['@type'] === googleErrorDetailsBadRequestType)
    .reduce((acc: FieldViolation[], curr: { fieldViolations: FieldViolation[] }) => {
      const fieldViolations = curr?.fieldViolations.map((fv: FieldViolation) => ({
        field: fv.field,
        description: fv.description,
      }));
      acc.push(...fieldViolations);
      return acc;
    }, []);
}

export function getMetadataFromError(err: HttpErrorResponse): Record<string, string | undefined> {
  if (!err?.error?.details || !Array.isArray(err.error.details)) {
    return {};
  }
  return err.error.details
    .filter((detail: Record<string, unknown>) => detail['@type'] === googleErrorDetailsErrorInfoType)
    .reduce((acc: Record<string, string>, curr: { metadata: Record<string, string> }) => {
      acc = { ...acc, ...curr.metadata };
      return acc;
    }, {});
}
