import { Component, EventEmitter, Inject, OnDestroy, OnInit, Output } from '@angular/core';
import { FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN } from '../../data-providers/providers';
import { Observable, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { CalendarSource } from '@vendasta/meetings';
import { MeetingSchedulerStoreService } from '../../data-providers/meeting-scheduler-store.service';
import { MatRadioChange } from '@angular/material/radio';
import { CALENDAR_SOURCE } from '../../constants';

@Component({
  selector: 'meeting-scheduler-calendar-selection',
  templateUrl: './calendar-selection.component.html',
  styleUrl: './calendar-selection.component.scss',
  standalone: false,
})
export class CalendarSelectionComponent implements OnInit, OnDestroy {
  selectedCalendarProvider: string;
  @Output() calendarProviderSelected = new EventEmitter<string>();
  readonly googleTooltipText: string;
  readonly microsoftTooltipText: string;
  private subscription: Subscription;

  constructor(
    @Inject(FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN)
    readonly featureMicrosoftTeamsEnabled$: Observable<boolean>,
    private readonly translate: TranslateService,
    private readonly storeService: MeetingSchedulerStoreService,
  ) {
    this.googleTooltipText = this.translate.instant(
      'MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_SIGN_IN_TOOLTIP_TEXT',
    );
    this.microsoftTooltipText = this.translate.instant(
      'MEETING_SCHEDULER.MEETING_INTEGRATIONS.MICROSOFT_SIGN_IN_TOOLTIP_TEXT',
    );
  }
  ngOnInit(): void {
    this.subscription = this.storeService.loadUserPreferences().subscribe((preferences) => {
      if (preferences.calendarIntegration === CalendarSource.CALENDAR_SOURCE_MICROSOFT) {
        this.selectedCalendarProvider = CALENDAR_SOURCE.MICROSOFT;
      } else {
        this.selectedCalendarProvider = CALENDAR_SOURCE.GOOGLE;
      }
      this.emitSelectedProvider();
    });
  }
  async onCalendarProviderChange(event: MatRadioChange) {
    try {
      await this.storeService.updateUserPreferences({
        calendarIntegration:
          event.value === CALENDAR_SOURCE.MICROSOFT
            ? CalendarSource.CALENDAR_SOURCE_MICROSOFT
            : CalendarSource.CALENDAR_SOURCE_GOOGLE,
      });
      this.selectedCalendarProvider = event.value;
      this.emitSelectedProvider();
    } catch (error) {
      console.error('Failed to update calendar preferences:', error);
    }
  }

  private emitSelectedProvider(): void {
    this.calendarProviderSelected.emit(this.selectedCalendarProvider);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
