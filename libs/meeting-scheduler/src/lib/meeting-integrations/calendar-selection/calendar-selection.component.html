<mat-radio-group
  [(ngModel)]="selectedCalendarProvider"
  class="calendar-provider-radio"
  (change)="onCalendarProviderChange($event)"
>
  <mat-radio-button value="google" [glxyTooltip]="googleTooltipText">
    <div class="icon-position">
      <div>
        <img
          class="google-icon"
          src="https://storage.googleapis.com/meeting-scheduler-prod-public-images/google_logo.svg"
        />
      </div>
      <span class="text-spacing">{{ 'MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_CALENDAR' | translate }}</span>
    </div>
  </mat-radio-button>
  <mat-radio-button
    value="microsoft"
    *ngIf="(featureMicrosoftTeamsEnabled$ | async) === true"
    [glxyTooltip]="microsoftTooltipText"
  >
    <div class="icon-position">
      <div>
        <img
          class="microsoft-icon"
          src="https://storage.googleapis.com/meeting-scheduler-prod-public-images/microsoft-logo.svg"
        />
      </div>
      <span class="text-spacing">{{ 'MEETING_SCHEDULER.MEETING_INTEGRATIONS.MICROSOFT_CALENDAR' | translate }}</span>
    </div>
  </mat-radio-button>
</mat-radio-group>
