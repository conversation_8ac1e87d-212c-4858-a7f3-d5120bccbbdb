<form [formGroup]="formGroup" novalidate #form="ngForm">
  <div>
    <glxy-form-row>
      <glxy-rich-text-editor
        class="rich-text-editor"
        elementId="email-rte"
        formControlName="loggedEmailDescription"
        [statusbar]="true"
        [enableThemes]="true"
        [formControl]="formGroup.controls['loggedEmailDescription']"
        [allowResize]="true"
        [plugins]="DEFAULT_RICH_TEXT_PLUGINS"
        [toolbar]="DEFAULT_RICH_TEXT_TOOLBAR"
      ></glxy-rich-text-editor>
      <ng-container *ngIf="showErrors">
        <glxy-error class="rich-text-error" *ngIf="formGroup.controls.loggedEmailDescription.hasError('required')">
          {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_DESCRIPTION' | translate }}
        </glxy-error>
        @if (formGroup.controls.loggedEmailDescription.hasError('maxLength')) {
          <glxy-error class="rich-text-error">
            {{ 'ACTIVITY_LOGGER.ERROR_MAX_LENGTH_EXCEEDED' | translate: { maxLength: ACTIVITY_BODY_MAX_LENGTH } }}
          </glxy-error>
        }
      </ng-container>
    </glxy-form-row>
  </div>
  <ng-content select="[crmLoggerContent='after-description']"></ng-content>
  <div>
    <glxy-form-row class="detail-form-row" [class.mobile]="mobile">
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        size="default"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'date_range'"
      >
        <input
          formControlName="loggedEmailDate"
          type="datetime-local"
          matInput
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_EMAIL.DATE_PLACEHOLDER' | translate }}"
        />
        <mat-datepicker #emailDate></mat-datepicker>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="this.formGroup.controls.loggedEmailDate.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_EMAIL_DATE' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'compare_arrows'"
      >
        <mat-select
          formControlName="loggedEmailDirection"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_EMAIL.DIRECTION_PLACEHOLDER' | translate }}"
        >
          <mat-option value="{{ ActivityDirection.Outbound }}">{{ 'ACTIVITY.OUTBOUND' | translate }}</mat-option>
          <mat-option value="{{ ActivityDirection.Inbound }}">{{ 'ACTIVITY.INBOUND' | translate }}</mat-option>
          <mat-option value="{{ ActivityDirection.Forwarded }}">
            {{ 'ACTIVITY.FORWARDED' | translate }}
          </mat-option>
        </mat-select>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="this.formGroup.controls.loggedEmailDirection.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_EMAIL_DIRECTION' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
    </glxy-form-row>
    <ng-content select="[crmLoggerContent='fields']"></ng-content>
  </div>
</form>
