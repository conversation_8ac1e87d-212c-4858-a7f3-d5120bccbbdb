<form [formGroup]="formGroup" novalidate #form="ngForm">
  <div>
    <glxy-form-row>
      <glxy-rich-text-editor
        class="rich-text-editor"
        elementId="communication-rte"
        [statusbar]="true"
        [enableThemes]="true"
        [formControl]="formGroup.controls['loggedCommunicationBody']"
        [allowResize]="true"
        [plugins]="DEFAULT_RICH_TEXT_PLUGINS"
        [toolbar]="DEFAULT_RICH_TEXT_TOOLBAR"
      ></glxy-rich-text-editor>
      <ng-container *ngIf="showErrors">
        <glxy-error class="rich-text-error" *ngIf="formGroup.controls.loggedCommunicationBody.hasError('required')">
          {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_DESCRIPTION' | translate }}
        </glxy-error>
        @if (formGroup.controls.loggedCommunicationBody.hasError('maxLength')) {
          <glxy-error class="rich-text-error">
            {{ 'ACTIVITY_LOGGER.ERROR_MAX_LENGTH_EXCEEDED' | translate: { maxLength: ACTIVITY_BODY_MAX_LENGTH } }}
          </glxy-error>
        }
      </ng-container>
    </glxy-form-row>
  </div>
  <ng-content select="[crmLoggerContent='after-description']"></ng-content>
  <div>
    <glxy-form-row class="detail-form-row" [class.mobile]="mobile">
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        size="default"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'date_range'"
      >
        <input
          type="datetime-local"
          formControlName="loggedCommunicationDate"
          matInput
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_COMMUNICATION.DATE_PLACEHOLDER' | translate }}"
        />
        <mat-datepicker #emailDate></mat-datepicker>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.loggedCommunicationDate.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_COMMUNICATION_DATE' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'feedback'"
      >
        <mat-select
          formControlName="loggedCommunicationOutcome"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_COMMUNICATION.DIRECTION_PLACEHOLDER' | translate }}"
        >
          <mat-option value="{{ ActivityOutcome.NoInterest }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.NO_INTEREST' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Interested }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.INTERESTED' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.QualityConnect }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.QUALITY_CONNECT' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Demo }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.DEMO_SCHEDULED' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Timeline }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.TIMELINE_6_12_MONTHS' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Referred }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.REFERRED' | translate }}
          </mat-option>
        </mat-select>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.loggedCommunicationOutcome.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_COMMUNICATION_OUTCOME' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
    </glxy-form-row>
    <ng-content select="[crmLoggerContent='fields']"></ng-content>
  </div>
</form>
