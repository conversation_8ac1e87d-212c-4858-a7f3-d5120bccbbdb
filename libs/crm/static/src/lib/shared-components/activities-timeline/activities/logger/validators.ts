import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function requiredFieldsIf(predicate: () => boolean, fields: AbstractControl[]): ValidatorFn {
  return (): ValidationErrors | null => {
    const when = predicate();
    if (when) {
      fields.forEach((field) => validateRequired(field));
    } else {
      fields.forEach((field) => clearRequired(field));
    }
    return null;
  };
}

export function requiredFormGroupFieldsIf(predicate: () => boolean, fieldNames: string[]): ValidatorFn {
  return (formGroup: AbstractControl): ValidationErrors | null => {
    const when = predicate();
    const controls = fieldNames.map((name) => formGroup.get(name)).filter((c): c is AbstractControl => Boolean(c));
    if (when) {
      controls.forEach((c) => validateRequired(c));
    } else {
      controls.forEach((c) => clearRequired(c));
    }

    return null;
  };
}

export function maxLengthValidator(predicate: () => boolean, maxLength: number, fieldNames: string[]): ValidatorFn {
  return (formGroup: AbstractControl): ValidationErrors | null => {
    const when = predicate();
    const controls = fieldNames.map((name) => formGroup.get(name)).filter((c): c is AbstractControl => Boolean(c));
    if (when) {
      controls.forEach((c) => validateMaxLength(c, maxLength));
    } else {
      controls.forEach((c) => clearMaxLength(c));
    }

    return null;
  };
}

function validateRequired(control: AbstractControl): void {
  if (control.value) {
    if (control.hasError('required')) {
      delete control.errors?.['required'];
    }
  } else {
    control.setErrors({ required: true });
  }
}

function clearRequired(control: AbstractControl): void {
  if (control.hasError('required')) {
    delete control.errors?.['required'];
  }
}

function validateMaxLength(control: AbstractControl, maxLength: number): void {
  const value = control.value;
  if (value && value.length > maxLength) {
    control.setErrors({ maxLength: { requiredLength: maxLength, actualLength: value.length } });
  } else {
    delete control.errors?.['maxLength'];
  }
}

function clearMaxLength(control: AbstractControl): void {
  if (control.hasError('maxLength')) {
    delete control.errors?.['maxLength'];
  }
}
