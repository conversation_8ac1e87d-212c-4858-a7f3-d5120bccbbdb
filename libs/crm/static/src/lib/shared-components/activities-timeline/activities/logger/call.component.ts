import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { ActivityInterface, FieldValueInterface } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { format as formatDate } from 'date-fns-tz';
import { CrmFieldService, StandardIds } from '../../../../shared-services/crm-services/field.service';
import { TranslationModule } from '../../../../i18n/translation-module';
import { ActivityDirection, ActivityOutcome, CallStatus } from '../activity.interface';

import { LoggerComponentInterface } from './interfaces';
import { maxLengthValidator, requiredFormGroupFieldsIf } from './validators';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { ACTIVITY_BODY_MAX_LENGTH, DEFAULT_RICH_TEXT_PLUGINS, DEFAULT_RICH_TEXT_TOOLBAR } from './constants';

@Component({
  selector: 'crm-activity-logger-call',
  templateUrl: './call.component.html',
  styleUrls: ['./shared.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    GalaxyFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    ReactiveFormsModule,
    TextFieldModule,
    GalaxyRichTextEditorModule,
  ],
})
export class CrmActivityLoggerCallComponent implements LoggerComponentInterface, AfterViewInit {
  @Input() set activity(value: ActivityInterface | null) {
    this.setValues(value);
  }
  @Input({ transform: coerceBooleanProperty }) mobile: boolean | null = null;
  @Input()
  set description(value: string | null) {
    // only patch value when values are different
    // or else rte cursor will go back to the beginning of input
    if (value !== this.formGroup.controls['loggedCallNote']?.getRawValue()) {
      this.formGroup.patchValue({ loggedCallNote: value || '' }, { emitEvent: false });
    }
  }
  @Output() descriptionChange = new EventEmitter<string | null>();

  @ViewChild('form') formGroupDirective!: FormGroupDirective;
  @ViewChild('defaultInput') defaultInput?: ElementRef<HTMLTextAreaElement>;

  protected showErrors = false;
  protected readonly ActivityDirection = ActivityDirection;
  protected readonly CallStatus = CallStatus;
  protected readonly ActivityOutcome = ActivityOutcome;
  protected readonly textRows = 6;

  DEFAULT_RICH_TEXT_PLUGINS = DEFAULT_RICH_TEXT_PLUGINS;
  DEFAULT_RICH_TEXT_TOOLBAR = DEFAULT_RICH_TEXT_TOOLBAR;
  ACTIVITY_BODY_MAX_LENGTH = ACTIVITY_BODY_MAX_LENGTH;

  formGroup = new FormBuilder().nonNullable.group(
    {
      loggedCallNote: '',
      loggedCallDirection: '',
      loggedCallDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
      loggedCallStatus: '',
      loggedCallOutcome: '',
    },
    {
      validators: [
        requiredFormGroupFieldsIf(() => this.showErrors, ['loggedCallNote', 'loggedCallDirection', 'loggedCallDate']),
        maxLengthValidator(() => this.showErrors, ACTIVITY_BODY_MAX_LENGTH, ['loggedCallNote']),
      ],
    },
  );

  constructor(private readonly fieldsService: CrmFieldService) {
    this.formGroup.controls.loggedCallNote.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe((value) => this.descriptionChange.emit(value));
  }

  ngAfterViewInit(): void {
    this.defaultInput?.nativeElement?.focus();
  }

  validate(): void {
    this.showErrors = true;
    this.formGroup.markAllAsTouched();
    this.updateValueAndValidity();
  }

  updateValueAndValidity(): void {
    this.formGroup.updateValueAndValidity();
  }

  private setValues(activity: ActivityInterface | null): void {
    if (activity) {
      const callDate = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityCallDate);
      const callBody = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityCallBody);
      const callDirection = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityCallDirection);
      const callStatus = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityCallStatus);
      const callOutcome = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityCallOutcome);

      this.formGroup.setValue({
        loggedCallNote: (callBody?.stringValue || '').replace(/\n/g, '<br>'),
        loggedCallDirection: callDirection?.stringValue || '',
        loggedCallDate: callDate?.dateValue ? formatDate(callDate.dateValue, "yyyy-MM-dd'T'HH:mm") : '',
        loggedCallStatus: callStatus?.stringValue || '',
        loggedCallOutcome: callOutcome?.stringValue || '',
      });
    } else if (this.formGroup.dirty) {
      this.resetForm();
    }
  }

  getValues(): FieldValueInterface[] {
    const formValues = this.formGroup.getRawValue();
    return [
      {
        fieldId: StandardIds.ActivityCallDate,
        dateValue: formValues.loggedCallDate ? new Date(formValues.loggedCallDate) : undefined,
      },
      {
        fieldId: StandardIds.ActivityCallBody,
        stringValue: formValues.loggedCallNote,
      },
      {
        fieldId: StandardIds.ActivityCallDirection,
        stringValue: formValues.loggedCallDirection,
      },
      {
        fieldId: StandardIds.ActivityCallStatus,
        stringValue: formValues.loggedCallStatus,
      },
      {
        fieldId: StandardIds.ActivityCallOutcome,
        stringValue: formValues.loggedCallOutcome,
      },
    ] as FieldValueInterface[];
  }

  resetForm(): void {
    this.showErrors = false;
    this.formGroup.reset({
      loggedCallNote: this.description || '',
      loggedCallDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
    });
    this.updateValueAndValidity();
  }
}
