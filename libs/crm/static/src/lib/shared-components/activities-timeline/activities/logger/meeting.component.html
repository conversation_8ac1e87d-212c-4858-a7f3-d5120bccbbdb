<form [formGroup]="formGroup" novalidate #form="ngForm">
  <div>
    <glxy-form-row>
      <glxy-rich-text-editor
        class="rich-text-editor"
        elementId="meeting-rte"
        [statusbar]="true"
        [enableThemes]="true"
        [formControl]="formGroup.controls['loggedMeetingNote']"
        [allowResize]="true"
        [plugins]="DEFAULT_RICH_TEXT_PLUGINS"
        [toolbar]="DEFAULT_RICH_TEXT_TOOLBAR"
      ></glxy-rich-text-editor>
      <ng-container *ngIf="showErrors">
        <glxy-error class="rich-text-error" *ngIf="formGroup.controls.loggedMeetingNote.hasError('required')">
          {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_DESCRIPTION' | translate }}
        </glxy-error>
        @if (formGroup.controls.loggedMeetingNote.hasError('maxLength')) {
          <glxy-error class="rich-text-error">
            {{ 'ACTIVITY_LOGGER.ERROR_MAX_LENGTH_EXCEEDED' | translate: { maxLength: ACTIVITY_BODY_MAX_LENGTH } }}
          </glxy-error>
        }
      </ng-container>
    </glxy-form-row>
  </div>
  <ng-content select="[crmLoggerContent='after-description']"></ng-content>
  <div>
    <glxy-form-row class="detail-form-row" [class.mobile]="mobile">
      <glxy-form-field
        [prefixIcon]="'date_range'"
        bottomSpacing="small"
        [showLabel]="false"
        size="default"
        class="detail-form-field"
        [class.mobile]="mobile"
      >
        <input
          formControlName="loggedMeetingDate"
          type="datetime-local"
          matInput
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_MEETING.DATE_PLACEHOLDER' | translate }}"
        />
        <mat-datepicker #meetingDate></mat-datepicker>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.loggedMeetingDate.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_MEETING_DATE' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
      <glxy-form-field
        [prefixIcon]="'timer'"
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
      >
        <mat-select
          formControlName="loggedMeetingDuration"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_MEETING.DURATION_PLACEHOLDER' | translate }}"
        >
          <mat-option [value]="MeetingDuration.FiveMin">
            {{ 'ACTIVITY.MEETING.5_MIN' | translate }}
          </mat-option>
          <mat-option [value]="MeetingDuration.FifteenMin">
            {{ 'ACTIVITY.MEETING.15_MIN' | translate }}
          </mat-option>
          <mat-option [value]="MeetingDuration.ThirtyMin">
            {{ 'ACTIVITY.MEETING.30_MIN' | translate }}
          </mat-option>
          <mat-option [value]="MeetingDuration.OneHour">
            {{ 'ACTIVITY.MEETING.1_HOUR' | translate }}
          </mat-option>
          <mat-option [value]="MeetingDuration.OneHourThirtyMin">
            {{ 'ACTIVITY.MEETING.1_HOUR_30_MIN' | translate }}
          </mat-option>
          <mat-option [value]="MeetingDuration.TwoHoursPlus">
            {{ 'ACTIVITY.MEETING.2_HOURS_OR_MORE' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
    </glxy-form-row>
    <glxy-form-row class="detail-form-row" [class.mobile]="mobile">
      <glxy-form-field
        [prefixIcon]="'done'"
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
      >
        <mat-select
          formControlName="loggedMeetingStatus"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_MEETING.STATUS_PLACEHOLDER' | translate }}"
        >
          <mat-option value="{{ MeetingStatus.Scheduled }}">
            {{ 'ACTIVITY.MEETING.SCHEDULED' | translate }}
          </mat-option>
          <mat-option value="{{ MeetingStatus.Rescheduled }}">
            {{ 'ACTIVITY.MEETING.RESCHEDULED' | translate }}
          </mat-option>
          <mat-option value="{{ MeetingStatus.Completed }}">
            {{ 'ACTIVITY.MEETING.COMPLETED' | translate }}
          </mat-option>
          <mat-option value="{{ MeetingStatus.Cancelled }}">
            {{ 'ACTIVITY.MEETING.CANCELLED' | translate }}
          </mat-option>
          <mat-option value="{{ MeetingStatus.NoShow }}">{{ 'ACTIVITY.MEETING.NO_SHOW' | translate }}</mat-option>
        </mat-select>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.loggedMeetingStatus.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_MEETING_STATUS' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
      <glxy-form-field
        [prefixIcon]="'feedback'"
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
      >
        <mat-select
          formControlName="loggedMeetingOutcome"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_MEETING.OUTCOME_PLACEHOLDER' | translate }}"
        >
          <mat-option value="{{ ActivityOutcome.NoInterest }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.NO_INTEREST' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Interested }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.INTERESTED' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.QualityConnect }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.QUALITY_CONNECT' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Demo }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.DEMO_SCHEDULED' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Timeline }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.TIMELINE_6_12_MONTHS' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Rescheduling }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.RESCHEDULING' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Referred }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.REFERRED' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
    </glxy-form-row>
    <ng-content select="[crmLoggerContent='fields']"></ng-content>
  </div>
</form>
