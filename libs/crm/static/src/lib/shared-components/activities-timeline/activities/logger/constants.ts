import { ActivityTaskStatus } from '../../../../tokens-and-interfaces';

export const STANDARD_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm";
export const DEFAULT_TASK_STATUS = ActivityTaskStatus.Open;

export const DEFAULT_RICH_TEXT_PLUGINS = 'link lists';
export const DEFAULT_RICH_TEXT_TOOLBAR = [
  'styleselect | bold italic link unlink | forecolor backcolor | alignleft aligncenter alignright | bullist numlist | removeformat',
];

export const ACTIVITY_BODY_MAX_LENGTH = 64000;
