import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  EventEmitter,
  input,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { ActivityInterface, FieldValueInterface } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import {
  ACTIVITY_BODY_MAX_LENGTH,
  DEFAULT_RICH_TEXT_PLUGINS,
  DEFAULT_RICH_TEXT_TOOLBAR,
  DEFAULT_TASK_STATUS,
  STANDARD_DATE_FORMAT,
} from './constants';
import { TranslationModule } from '../../../../i18n/translation-module';
import { format as formatDate } from 'date-fns-tz';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import {
  ActivityOutcome,
  ActivityTaskType,
  ActivityTaskTypeTranslationKeys,
  MeetingDuration,
  MeetingStatus,
} from '../activity.interface';
import { User } from '@vendasta/iamv2';
import { LoggerComponentInterface, UserWithFullName } from './interfaces';
import { maxLengthValidator, requiredFormGroupFieldsIf } from './validators';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs';
import { CrmFieldService, StandardIds, SystemFieldIds } from '../../../../shared-services';
import { CrmFormFieldActivityOwnerComponent } from '../../../form-fields/activity-owner/activity-owner.component';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { FormFieldAssociation } from '../../../association-form-field/interface';

@Component({
  selector: 'crm-activity-logger-task',
  templateUrl: './task.component.html',
  styleUrls: ['./shared.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    GalaxyFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    TextFieldModule,
    MatDatepickerModule,
    MatSelectModule,
    CrmFormFieldActivityOwnerComponent,
    GalaxyRichTextEditorModule,
  ],
})
export class CrmActivityLoggerTaskComponent implements LoggerComponentInterface, AfterViewInit {
  @Input() set activity(value: ActivityInterface | null) {
    this.setValues(value);
  }
  @Input({ transform: coerceBooleanProperty }) mobile: boolean | null = null;
  user = input<UserWithFullName | null>();
  activityOwner = input<User | undefined | null>();
  @Input()
  set description(value: string | null) {
    // only patch value when values are different
    // or else rte cursor will go back to the beginning of input
    if (value !== this.formGroup?.controls['taskInstructions']?.getRawValue()) {
      this.formGroup.patchValue({ taskInstructions: value || '' }, { emitEvent: false });
    }
  }
  @Output() descriptionChange = new EventEmitter<string | null>();
  @Input() associations?: FormFieldAssociation[];
  @Output() associationsChange = new EventEmitter<FormFieldAssociation[]>();

  @ViewChild('form') formGroupDirective!: FormGroupDirective;
  @ViewChild('defaultInput') defaultInput?: ElementRef<HTMLTextAreaElement>;

  DEFAULT_RICH_TEXT_PLUGINS = DEFAULT_RICH_TEXT_PLUGINS;
  DEFAULT_RICH_TEXT_TOOLBAR = DEFAULT_RICH_TEXT_TOOLBAR;
  ACTIVITY_BODY_MAX_LENGTH = ACTIVITY_BODY_MAX_LENGTH;

  protected showErrors = false;
  protected readonly ActivityOutcome = ActivityOutcome;
  protected readonly MeetingDuration = MeetingDuration;
  protected readonly MeetingStatus = MeetingStatus;
  protected readonly textRows = 6;

  private today = new Date();
  formGroup = new FormBuilder().nonNullable.group(
    {
      taskName: '',
      taskInstructions: '',
      taskDueDate: formatDate(this.today.setDate(this.today.getDate() + 3), STANDARD_DATE_FORMAT),
      taskType: ActivityTaskType.ToDo,
      taskPriority: '',
      taskOwner: '',
    },
    {
      validators: [
        requiredFormGroupFieldsIf(() => this.showErrors, ['taskName', 'taskType', 'taskDueDate', 'taskOwner']),
        maxLengthValidator(() => this.showErrors, ACTIVITY_BODY_MAX_LENGTH, ['taskInstructions']),
      ],
    },
  );

  protected readonly taskTypes = ActivityTaskTypeTranslationKeys;

  constructor(private readonly fieldsService: CrmFieldService) {
    this.formGroup.controls.taskInstructions.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe((value) => this.descriptionChange.emit(value));
    effect(() => {
      const user = this.user();
      const owner = this.activityOwner();
      this.setDefaultUser(owner ?? user ?? undefined);
    });
    this.formGroup.controls.taskOwner.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe((ownerId) => this.replaceUserAssociation(ownerId));
  }

  ngAfterViewInit(): void {
    this.defaultInput?.nativeElement?.focus();
  }

  resetForm(): void {
    this.showErrors = false;
    const today = new Date();
    this.formGroup.reset({
      taskInstructions: this.description || '',
      taskOwner: this.user()?.userId || '',
      taskDueDate: formatDate(today.setDate(today.getDate() + 3), STANDARD_DATE_FORMAT),
    });
    this.updateValueAndValidity();
  }

  getValues(): FieldValueInterface[] {
    const formValues = this.formGroup.getRawValue();
    return [
      {
        fieldId: this.fieldsService.getFieldId(StandardIds.ActivityName),
        stringValue: formValues.taskName,
      },
      {
        fieldId: this.fieldsService.getFieldId(StandardIds.ActivityTaskBody),
        stringValue: formValues.taskInstructions,
      },
      {
        fieldId: this.fieldsService.getFieldId(StandardIds.ActivityTaskDueDate),
        dateValue: formValues.taskDueDate ? new Date(formValues.taskDueDate) : undefined,
      },
      {
        fieldId: this.fieldsService.getFieldId(SystemFieldIds.ActivityOwnerID),
        stringValue: formValues.taskOwner,
      },
      {
        fieldId: this.fieldsService.getFieldId(StandardIds.ActivityTaskPriority),
        stringValue: formValues.taskPriority,
      },
      {
        fieldId: this.fieldsService.getFieldId(StandardIds.ActivityTaskStatus),
        stringValue: DEFAULT_TASK_STATUS,
      },
      {
        fieldId: this.fieldsService.getFieldId(StandardIds.ActivityTaskType),
        stringValue: formValues.taskType,
      },
    ] as FieldValueInterface[];
  }

  private setValues(activity: ActivityInterface | null): void {
    if (activity) {
      const taskName = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityName);
      const taskBody = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityTaskBody);
      const taskDueDate = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityTaskDueDate);
      const taskPriority = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityTaskPriority);
      const taskType = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityTaskType);
      const taskOwner = activity?.ownerId || this.user()?.userId;

      this.formGroup.setValue({
        taskName: taskName?.stringValue || '',
        taskInstructions: taskBody?.stringValue || '',
        taskDueDate: taskDueDate?.dateValue ? formatDate(taskDueDate.dateValue, STANDARD_DATE_FORMAT) : '',
        taskPriority: taskPriority?.stringValue || '',
        taskType: taskType?.stringValue || '',
        taskOwner: taskOwner || '',
      });
    } else if (this.formGroup.dirty) {
      this.resetForm();
    }
  }

  private setDefaultUser(user?: User): void {
    const userId = user?.userId;
    if (!this.formGroup.getRawValue().taskOwner) {
      this.formGroup.patchValue({
        taskOwner: userId,
      });
    }
  }

  private replaceUserAssociation(ownerId: string): void {
    const ownerAssociation = this.associations?.find((a) => a.crmObjectType === 'User');
    if (ownerAssociation) {
      ownerAssociation.crmObjectId = ownerId;
      this.associationsChange.emit(this.associations);
    }
  }

  validate(): void {
    this.showErrors = true;
    this.formGroup.markAllAsTouched();
    this.updateValueAndValidity();
  }

  updateValueAndValidity(): void {
    this.formGroup.updateValueAndValidity();
  }

  get ownerId(): string | undefined {
    return this.formGroup.getRawValue().taskOwner || undefined;
  }
}
