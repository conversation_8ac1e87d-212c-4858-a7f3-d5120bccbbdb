import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { ActivityInterface, FieldValueInterface } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { format as formatDate } from 'date-fns-tz';
import { CrmFieldService, StandardIds } from '../../../../shared-services';
import { TranslationModule } from '../../../../i18n/translation-module';
import { ActivityDirection } from '../activity.interface';

import { LoggerComponentInterface } from './interfaces';
import { maxLengthValidator, requiredFormGroupFieldsIf } from './validators';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { ACTIVITY_BODY_MAX_LENGTH, DEFAULT_RICH_TEXT_PLUGINS, DEFAULT_RICH_TEXT_TOOLBAR } from './constants';

@Component({
  selector: 'crm-activity-logger-email',
  templateUrl: './email.component.html',
  styleUrls: ['./shared.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    MatIconModule,
    GalaxyFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    ReactiveFormsModule,
    TextFieldModule,
    GalaxyRichTextEditorModule,
  ],
})
export class CrmActivityLoggerEmailComponent implements LoggerComponentInterface, AfterViewInit {
  @Input() set activity(value: ActivityInterface | null) {
    this.setValues(value);
  }
  @Input({ transform: coerceBooleanProperty }) mobile: boolean | null = null;
  @Input()
  set description(value: string | null) {
    // only patch value when values are different
    // or else rte cursor will go back to the beginning of input
    if (value !== this.formGroup.controls['loggedEmailDescription']?.getRawValue()) {
      this.formGroup.patchValue({ loggedEmailDescription: value || '' }, { emitEvent: false });
    }
  }
  @Output() descriptionChange = new EventEmitter<string | null>();

  @ViewChild('form') formGroupDirective!: FormGroupDirective;
  @ViewChild('defaultInput') defaultInput?: ElementRef<HTMLTextAreaElement>;

  protected showErrors = false;
  protected readonly ActivityDirection = ActivityDirection;
  protected readonly textRows = 6;

  DEFAULT_RICH_TEXT_PLUGINS = DEFAULT_RICH_TEXT_PLUGINS;
  DEFAULT_RICH_TEXT_TOOLBAR = DEFAULT_RICH_TEXT_TOOLBAR;
  ACTIVITY_BODY_MAX_LENGTH = ACTIVITY_BODY_MAX_LENGTH;

  formGroup = new FormBuilder().nonNullable.group(
    {
      loggedEmailDescription: '',
      loggedEmailDirection: '',
      loggedEmailDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
    },
    {
      validators: [
        requiredFormGroupFieldsIf(
          () => this.showErrors,
          ['loggedEmailDescription', 'loggedEmailDirection', 'loggedEmailDate'],
        ),
        maxLengthValidator(() => this.showErrors, ACTIVITY_BODY_MAX_LENGTH, ['loggedEmailDescription']),
      ],
    },
  );

  constructor(private readonly fieldsService: CrmFieldService) {
    this.formGroup.controls.loggedEmailDescription.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe((value) => this.descriptionChange.emit(value));
  }

  ngAfterViewInit(): void {
    this.defaultInput?.nativeElement?.focus();
  }

  validate(): void {
    this.showErrors = true;
    this.formGroup.markAllAsTouched();
    this.updateValueAndValidity();
  }

  updateValueAndValidity(): void {
    this.formGroup.updateValueAndValidity();
  }

  private setValues(activity: ActivityInterface | null): void {
    if (activity) {
      const emailDate = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityEmailDate);
      const emailBody = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityEmailBody);
      const emailDirection = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityEmailDirection,
      );

      this.formGroup.setValue({
        loggedEmailDescription: emailBody?.stringValue || '',
        loggedEmailDirection: emailDirection?.stringValue || '',
        loggedEmailDate: emailDate?.dateValue ? formatDate(emailDate.dateValue, "yyyy-MM-dd'T'HH:mm") : '',
      });
    } else if (this.formGroup.dirty) {
      this.resetForm();
    }
  }

  getValues(): FieldValueInterface[] {
    const formValues = this.formGroup.getRawValue();
    return [
      {
        fieldId: StandardIds.ActivityEmailDate,
        dateValue: formValues.loggedEmailDate ? new Date(formValues.loggedEmailDate) : undefined,
      },
      {
        fieldId: StandardIds.ActivityEmailBody,
        stringValue: formValues.loggedEmailDescription,
      },
      {
        fieldId: StandardIds.ActivityEmailDirection,
        stringValue: formValues.loggedEmailDirection,
      },
    ] as FieldValueInterface[];
  }

  resetForm(): void {
    this.showErrors = false;
    this.formGroup.reset({
      loggedEmailDescription: this.description || '',
      loggedEmailDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
    });
    this.updateValueAndValidity();
  }
}
