<form [formGroup]="formGroup" novalidate #form="ngForm">
  <div>
    <glxy-form-row>
      <glxy-form-field bottomSpacing="small" [showLabel]="false">
        <textarea
          #defaultInput
          formControlName="taskName"
          matInput
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_TASK.TITLE_PLACEHOLDER' | translate }}"
          cdkTextareaAutosize
        ></textarea>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.taskName.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_TASK_NAME_REQUIRED' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
    </glxy-form-row>
    <glxy-form-row>
      <glxy-rich-text-editor
        class="rich-text-editor"
        elementId="task-rte"
        [statusbar]="true"
        [enableThemes]="true"
        [formControl]="formGroup.controls['taskInstructions']"
        [allowResize]="true"
        [plugins]="DEFAULT_RICH_TEXT_PLUGINS"
        [toolbar]="DEFAULT_RICH_TEXT_TOOLBAR"
      ></glxy-rich-text-editor>
      @if (showErrors) {
        <ng-container>
          @if (formGroup.controls.taskInstructions.hasError('maxLength')) {
            <glxy-error class="rich-text-error">
              {{ 'ACTIVITY_LOGGER.ERROR_MAX_LENGTH_EXCEEDED' | translate: { maxLength: ACTIVITY_BODY_MAX_LENGTH } }}
            </glxy-error>
          }
        </ng-container>
      }
    </glxy-form-row>
  </div>
  <ng-content select="[crmLoggerContent='after-description']"></ng-content>
  <div>
    <glxy-form-row class="detail-form-row" [class.mobile]="mobile">
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        size="default"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'alarm'"
      >
        <input
          formControlName="taskDueDate"
          type="datetime-local"
          matInput
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_TASK.DUE_DATE_PLACEHOLDER' | translate }}"
        />
        <mat-datepicker #taskDueDateInput></mat-datepicker>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.taskDueDate.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_TASK_DUE_DATE' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
      <glxy-form-field
        [prefixIcon]="'assignment_ind'"
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
      >
        <crm-form-field-activity-owner
          insideInputWrapper
          formControlName="taskOwner"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_TASK.OWNER_PLACEHOLDER' | translate }}"
        >
        </crm-form-field-activity-owner>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.taskOwner.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_TASK_ASSIGNEE' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
    </glxy-form-row>
    <glxy-form-row class="detail-form-row" [class.mobile]="mobile">
      <glxy-form-field
        [prefixIcon]="'checklist'"
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
      >
        <mat-select
          formControlName="taskType"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_TASK.TYPE_PLACEHOLDER' | translate }}"
        >
          <mat-option *ngFor="let type of taskTypes" [value]="type.value">
            {{ type.nameKey | translate }}
          </mat-option>
        </mat-select>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.taskType.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_TASK_TYPE' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
      <glxy-form-field
        [prefixIcon]="'priority_high'"
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
      >
        <mat-select
          formControlName="taskPriority"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_TASK.PRIORITY_PLACEHOLDER' | translate }}"
        >
          <mat-option value="Low">{{ 'ACTIVITY.TASK.LOW' | translate }}</mat-option>
          <mat-option value="Medium">{{ 'ACTIVITY.TASK.MEDIUM' | translate }}</mat-option>
          <mat-option value="High">{{ 'ACTIVITY.TASK.HIGH' | translate }}</mat-option>
        </mat-select>
      </glxy-form-field>
    </glxy-form-row>
    <ng-content select="[crmLoggerContent='fields']"></ng-content>
  </div>
</form>
