import { COMM<PERSON>, ENTER } from '@angular/cdk/keycodes';
import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, EventEmitter, Inject, Input, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { ActivityInterface, FieldValueInterface } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { CrmFieldService, StandardIds } from '../../../../shared-services/crm-services/field.service';
import { TranslationModule } from '../../../../i18n/translation-module';
import { LoggerComponentInterface } from './interfaces';
import { maxLengthValidator, requiredFormGroupFieldsIf } from './validators';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs';
import { CrmDependencies, CrmInjectionToken } from '../../../../tokens-and-interfaces';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { ACTIVITY_BODY_MAX_LENGTH, DEFAULT_RICH_TEXT_PLUGINS, DEFAULT_RICH_TEXT_TOOLBAR } from './constants';

@Component({
  selector: 'crm-activity-logger-note',
  templateUrl: './note.component.html',
  styleUrls: ['./shared.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    MatIconModule,
    MatChipsModule,
    GalaxyFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    TextFieldModule,
    MatAutocompleteModule,
    MatTooltipModule,
    GalaxyRichTextEditorModule,
  ],
})
export class CrmActivityLoggerNoteComponent implements LoggerComponentInterface, AfterViewInit {
  @Input() set activity(value: ActivityInterface | null) {
    this.setValues(value);
  }
  @Input({ transform: coerceBooleanProperty }) mobile: boolean | null = null;
  @Input()
  set description(value: string | null) {
    // only patch value when values are different
    // or else rte cursor will go back to the beginning of input
    if (this.formGroup?.controls['noteBody']?.getRawValue() !== value) {
      this.formGroup.patchValue({ noteBody: value || '' }, { emitEvent: false });
    }
  }
  @Output() descriptionChange = new EventEmitter<string | null>();

  @ViewChild('form') formGroupDirective!: FormGroupDirective;
  @ViewChild('defaultInput') defaultInput?: ElementRef<HTMLTextAreaElement>;

  protected showErrors = false;
  protected readonly textRows = 6;

  DEFAULT_RICH_TEXT_PLUGINS = DEFAULT_RICH_TEXT_PLUGINS;
  DEFAULT_RICH_TEXT_TOOLBAR = DEFAULT_RICH_TEXT_TOOLBAR;
  ACTIVITY_BODY_MAX_LENGTH = ACTIVITY_BODY_MAX_LENGTH;

  formGroup = new FormBuilder().nonNullable.group(
    {
      noteBody: '',
    },
    {
      validators: [
        requiredFormGroupFieldsIf(() => this.showErrors, ['noteBody']),
        maxLengthValidator(() => this.showErrors, ACTIVITY_BODY_MAX_LENGTH, ['noteBody']),
      ],
    },
  );

  separatorKeyCodes: number[] = [ENTER, COMMA];

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly fieldsService: CrmFieldService,
  ) {
    this.formGroup.controls.noteBody.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe((value) => this.descriptionChange.emit(value));
  }

  ngAfterViewInit(): void {
    this.defaultInput?.nativeElement?.focus();
  }

  resetForm(): void {
    this.showErrors = false;
    this.formGroup.reset({ noteBody: this.description || '' });
    this.updateValueAndValidity();
  }

  getValues(): FieldValueInterface[] {
    const formValues = this.formGroup.getRawValue();
    const loggedNoteField = {
      fieldId: StandardIds.ActivityNoteBody,
      stringValue: formValues.noteBody,
    } as FieldValueInterface;

    return [loggedNoteField];
  }

  private setValues(activity: ActivityInterface | null): void {
    if (activity) {
      const noteBody = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityNoteBody);

      this.formGroup.setValue({
        noteBody: (noteBody?.stringValue || '').replace(/\n/g, '<br>'),
      });
    } else if (this.formGroup.dirty) {
      this.resetForm();
    }
  }

  validate(): void {
    this.showErrors = true;
    this.formGroup.markAllAsTouched();
    this.updateValueAndValidity();
  }

  updateValueAndValidity(): void {
    this.formGroup.updateValueAndValidity();
  }
}
