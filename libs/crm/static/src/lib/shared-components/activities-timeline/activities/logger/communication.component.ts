import { AfterViewInit, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { CommunicationSubType } from '../../../../tokens-and-interfaces';
import { TranslationModule } from '../../../../i18n/translation-module';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { FormBuilder, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { TextFieldModule } from '@angular/cdk/text-field';

import { LoggerComponentInterface } from './interfaces';
import { ActivityInterface, FieldValueInterface } from '@vendasta/crm';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { maxLengthValidator, requiredFormGroupFieldsIf } from './validators';
import { ActivityOutcome } from '../activity.interface';
import { format as formatDate } from 'date-fns-tz';
import { CrmFieldService, StandardIds } from '../../../../shared-services/crm-services/field.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { ACTIVITY_BODY_MAX_LENGTH, DEFAULT_RICH_TEXT_PLUGINS, DEFAULT_RICH_TEXT_TOOLBAR } from './constants';

@Component({
  selector: 'crm-activity-logger-communication',
  templateUrl: './communication.component.html',
  styleUrls: ['./shared.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    MatIconModule,
    GalaxyFormFieldModule,
    MatSelectModule,
    MatDatepickerModule,
    MatInputModule,
    ReactiveFormsModule,
    TextFieldModule,
    GalaxyRichTextEditorModule,
  ],
})
export class CrmActivityLoggerCommunicationComponent implements LoggerComponentInterface, AfterViewInit {
  @Input() set communicationType(value: CommunicationSubType) {
    // only patch value when values are different
    // or else rte cursor will go back to the beginning of input
    if (value !== this.formGroup.controls['loggedCommunicationChannel'].getRawValue()) {
      this.formGroup.patchValue({ loggedCommunicationChannel: value });
    }
  }
  @Input() set activity(value: ActivityInterface | null) {
    this.setValues(value);
  }
  @Input({ transform: coerceBooleanProperty }) mobile: boolean | null = null;

  @Input()
  set description(value: string | null) {
    // only patch value when values are different
    // or else rte cursor will go back to the beginning of input
    if (value !== this.formGroup.controls['loggedCommunicationBody']?.getRawValue()) {
      this.formGroup.patchValue({ loggedCommunicationBody: value || '' }, { emitEvent: false });
    }
  }
  @Output() descriptionChange = new EventEmitter<string | null>();

  @ViewChild('form') formGroupDirective!: FormGroupDirective;
  @ViewChild('defaultInput') defaultInput?: ElementRef<HTMLTextAreaElement>;

  protected showErrors = false;
  protected readonly textRows = 6;
  protected readonly ActivityOutcome = ActivityOutcome;

  DEFAULT_RICH_TEXT_PLUGINS = DEFAULT_RICH_TEXT_PLUGINS;
  DEFAULT_RICH_TEXT_TOOLBAR = DEFAULT_RICH_TEXT_TOOLBAR;
  ACTIVITY_BODY_MAX_LENGTH = ACTIVITY_BODY_MAX_LENGTH;

  formGroup = new FormBuilder().nonNullable.group(
    {
      loggedCommunicationChannel: '',
      loggedCommunicationOutcome: '',
      loggedCommunicationFromId: '',
      loggedCommunicationToId: '',
      loggedCommunicationBody: '',
      loggedCommunicationDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
    },
    {
      validators: [
        requiredFormGroupFieldsIf(() => this.showErrors, ['loggedCommunicationBody', 'loggedCommunicationDate']),
        maxLengthValidator(() => this.showErrors, ACTIVITY_BODY_MAX_LENGTH, ['loggedCommunicationBody']),
      ],
    },
  );

  constructor(private readonly fieldsService: CrmFieldService) {
    this.formGroup.controls.loggedCommunicationBody.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe((value) => this.descriptionChange.emit(value));
  }

  ngAfterViewInit(): void {
    this.defaultInput?.nativeElement?.focus();
  }

  validate(): void {
    this.showErrors = true;
    this.formGroup.markAllAsTouched();
    this.updateValueAndValidity();
  }

  updateValueAndValidity(): void {
    this.formGroup.updateValueAndValidity();
  }

  private setValues(activity: ActivityInterface | null): void {
    if (activity) {
      const communicationChannel = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityCommunicationChannel,
      );
      const communicationOutcome = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityCommunicationOutcome,
      );
      const communicationFromId = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityCommunicationFromId,
      );
      const communicationToId = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityCommunicationToId,
      );
      const communicationBody = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityCommunicationBody,
      );
      const communicationDate = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityCommunicationDate,
      );

      this.formGroup.setValue({
        loggedCommunicationChannel: communicationChannel?.stringValue || '',
        loggedCommunicationOutcome: communicationOutcome?.stringValue || '',
        loggedCommunicationFromId: communicationFromId?.stringValue || '',
        loggedCommunicationToId: communicationToId?.stringValue || '',
        loggedCommunicationBody: (communicationBody?.stringValue || '').replace(/\n/g, '<br>'),
        loggedCommunicationDate: communicationDate?.dateValue
          ? formatDate(communicationDate.dateValue, "yyyy-MM-dd'T'HH:mm")
          : '',
      });
    } else if (this.formGroup.dirty) {
      this.resetForm();
    }
  }

  getValues(): FieldValueInterface[] {
    const formValues = this.formGroup.getRawValue();
    return [
      {
        fieldId: StandardIds.ActivityCommunicationChannel,
        stringValue: formValues.loggedCommunicationChannel,
      },
      {
        fieldId: StandardIds.ActivityCommunicationBody,
        stringValue: formValues.loggedCommunicationBody,
      },
      {
        fieldId: StandardIds.ActivityCommunicationOutcome,
        stringValue: formValues.loggedCommunicationOutcome,
      },
      {
        fieldId: StandardIds.ActivityCommunicationToId,
        stringValue: formValues.loggedCommunicationToId,
      },
      {
        fieldId: StandardIds.ActivityCommunicationFromId,
        stringValue: formValues.loggedCommunicationFromId,
      },
      {
        fieldId: StandardIds.ActivityCommunicationDate,
        dateValue: formValues.loggedCommunicationDate ? new Date(formValues.loggedCommunicationDate) : undefined,
      },
    ] as FieldValueInterface[];
  }

  resetForm(): void {
    this.showErrors = false;
    this.formGroup.reset({
      loggedCommunicationBody: this.description || '',
      loggedCommunicationDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
    });
    this.updateValueAndValidity();
  }
}
