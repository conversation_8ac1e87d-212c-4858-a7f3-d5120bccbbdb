import { TextFieldModule } from '@angular/cdk/text-field';
import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { ActivityInterface, FieldValueInterface } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { CrmFieldService, StandardIds } from '../../../../shared-services/crm-services/field.service';
import { TranslationModule } from '../../../../i18n/translation-module';
import { format as formatDate } from 'date-fns-tz';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { ActivityOutcome, MeetingDuration, MeetingStatus } from '../activity.interface';
import { LoggerComponentInterface } from './interfaces';
import { maxLengthValidator, requiredFormGroupFieldsIf } from './validators';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged } from 'rxjs';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { ACTIVITY_BODY_MAX_LENGTH, DEFAULT_RICH_TEXT_PLUGINS, DEFAULT_RICH_TEXT_TOOLBAR } from './constants';

@Component({
  selector: 'crm-activity-logger-meeting',
  templateUrl: './meeting.component.html',
  styleUrls: ['./shared.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    GalaxyFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    TextFieldModule,
    MatDatepickerModule,
    MatSelectModule,
    GalaxyRichTextEditorModule,
  ],
})
export class CrmActivityLoggerMeetingComponent implements LoggerComponentInterface, AfterViewInit {
  @Input() set activity(value: ActivityInterface | null) {
    this.setValues(value);
  }
  @Input({ transform: coerceBooleanProperty }) mobile: boolean | null = null;
  @Input()
  set description(value: string | null) {
    // only patch value when values are different
    // or else rte cursor will go back to the beginning of input
    if (value !== this.formGroup.controls['loggedMeetingNote']?.getRawValue()) {
      this.formGroup.patchValue({ loggedMeetingNote: value || '' }, { emitEvent: false });
    }
  }
  @Output() descriptionChange = new EventEmitter<string | null>();

  @ViewChild('form') formGroupDirective!: FormGroupDirective;
  @ViewChild('defaultInput') defaultInput?: ElementRef<HTMLTextAreaElement>;

  protected showErrors = false;
  protected readonly ActivityOutcome = ActivityOutcome;
  protected readonly MeetingDuration = MeetingDuration;
  protected readonly MeetingStatus = MeetingStatus;
  protected readonly textRows = 6;

  DEFAULT_RICH_TEXT_PLUGINS = DEFAULT_RICH_TEXT_PLUGINS;
  DEFAULT_RICH_TEXT_TOOLBAR = DEFAULT_RICH_TEXT_TOOLBAR;
  ACTIVITY_BODY_MAX_LENGTH = ACTIVITY_BODY_MAX_LENGTH;

  formGroup = new FormBuilder().nonNullable.group(
    {
      loggedMeetingNote: '',
      loggedMeetingStatus: '',
      loggedMeetingOutcome: '',
      loggedMeetingDuration: 0,
      loggedMeetingDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
    },
    {
      validators: [
        requiredFormGroupFieldsIf(() => this.showErrors, ['loggedMeetingNote', 'loggedMeetingDate']),
        maxLengthValidator(() => this.showErrors, ACTIVITY_BODY_MAX_LENGTH, ['loggedMeetingNote']),
      ],
    },
  );

  constructor(private readonly fieldsService: CrmFieldService) {
    this.formGroup.controls.loggedMeetingNote.valueChanges
      .pipe(takeUntilDestroyed(), distinctUntilChanged())
      .subscribe((value) => this.descriptionChange.emit(value));
  }

  ngAfterViewInit(): void {
    this.defaultInput?.nativeElement?.focus();
  }

  resetForm(): void {
    this.showErrors = false;
    this.formGroup.reset({
      loggedMeetingNote: this.description || '',
      loggedMeetingDate: formatDate(new Date(), "yyyy-MM-dd'T'HH:mm"),
    });
    this.updateValueAndValidity();
  }

  getValues(): FieldValueInterface[] {
    const formValues = this.formGroup.getRawValue();
    return [
      {
        fieldId: StandardIds.ActivityMeetingStartTime,
        dateValue: formValues.loggedMeetingDate ? new Date(formValues.loggedMeetingDate) : undefined,
      },
      {
        fieldId: StandardIds.ActivityMeetingBody,
        stringValue: formValues.loggedMeetingNote,
      },
      {
        fieldId: StandardIds.ActivityMeetingStatus,
        stringValue: formValues.loggedMeetingStatus,
      },
      {
        fieldId: StandardIds.ActivityMeetingOutcome,
        stringValue: formValues.loggedMeetingOutcome,
      },
      {
        fieldId: StandardIds.ActivityMeetingDuration,
        integerValue: formValues.loggedMeetingDuration,
      },
    ] as FieldValueInterface[];
  }

  private setValues(activity: ActivityInterface | null): void {
    if (activity) {
      const meetingDate = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityMeetingStartTime);
      const meetingBody = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityMeetingBody);
      const meetingStatus = this.fieldsService.getFieldValueFromCrmObject(activity, StandardIds.ActivityMeetingStatus);
      const meetingOutcome = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityMeetingOutcome,
      );
      const meetingDuration = this.fieldsService.getFieldValueFromCrmObject(
        activity,
        StandardIds.ActivityMeetingDuration,
      );

      this.formGroup.setValue({
        loggedMeetingDate: meetingDate?.dateValue ? formatDate(meetingDate.dateValue, "yyyy-MM-dd'T'HH:mm") : '',
        loggedMeetingNote: (meetingBody?.stringValue || '').replace(/\n/g, '<br>'),
        loggedMeetingStatus: meetingStatus?.stringValue || '',
        loggedMeetingOutcome: meetingOutcome?.stringValue || '',
        loggedMeetingDuration: meetingDuration?.integerValue || 0,
      });
    } else if (this.formGroup.dirty) {
      this.resetForm();
    }
  }

  validate(): void {
    this.showErrors = true;
    this.formGroup.markAllAsTouched();
    this.updateValueAndValidity();
  }

  updateValueAndValidity(): void {
    this.formGroup.updateValueAndValidity();
  }
}
