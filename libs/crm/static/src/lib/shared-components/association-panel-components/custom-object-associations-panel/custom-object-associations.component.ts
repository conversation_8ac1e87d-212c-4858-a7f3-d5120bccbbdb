import { Component, inject, input } from '@angular/core';
import { CrmDependencies, CrmInjectionToken, ObjectType } from '../../../tokens-and-interfaces';
import { combineLatest, map, of, switchMap } from 'rxjs';
import { CRMCustomObjectTypeApiService } from '@vendasta/crm';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { CustomObjectAssociationPanelComponent } from './custom-object-association-panel.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'crm-custom-object-associations',
  template: `
    @for (subtype of customObjectTypes(); track subtype) {
      @if (objectType() && subtype) {
        <crm-custom-object-association-panel
          [id]="id()"
          [name]="name()"
          [objectType]="objectType()"
          [objectSubtype]="panelForCustomObjectType$ | async"
          [associatedObject]="'CustomObject'"
          [associatedObjectCustomType]="subtype"
        ></crm-custom-object-association-panel>
      }
    }
  `,
  imports: [AsyncPipe, CustomObjectAssociationPanelComponent],
  providers: [],
})
export class CustomObjectAssociationsComponent {
  private readonly config: CrmDependencies = inject(CrmInjectionToken);
  private readonly customObjectTypeApi = inject(CRMCustomObjectTypeApiService);

  id = input('');
  name = input('');
  objectType = input<ObjectType | null>(null);
  panelForCustomObjectType$ = this.config?.customobject?.objectSubtype$ ?? of('');

  customObjectTypes = toSignal(
    this.config.namespace$.pipe(
      takeUntilDestroyed(),
      switchMap((namespace) =>
        combineLatest([this.customObjectTypeApi.listCustomObjectTypes({ namespace }), this.panelForCustomObjectType$]),
      ),
      map(([response, currentSubtype]) => {
        return response.customObjectTypes?.filter((c) => c.customObjectTypeId !== currentSubtype);
      }),
    ),
  );
}
