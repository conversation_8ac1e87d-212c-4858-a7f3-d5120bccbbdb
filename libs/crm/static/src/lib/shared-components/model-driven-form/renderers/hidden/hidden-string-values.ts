import { Component, DestroyRef, inject, signal, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { FieldValueInterface, FormInputComponent } from '../../../../tokens-and-interfaces';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { filter } from 'rxjs/operators';
import { tap } from 'rxjs';
import { InlineEditService } from '../../inline-edit.service';
import { UpdaterService } from '../updater.service';

@Component({
  selector: 'crm-string-values-text',
  template: '',
  providers: [],
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, GalaxyFormFieldModule],
  encapsulation: ViewEncapsulation.None,
})
export class HiddenStringValuesFormInputComponent extends FormInputComponent {
  private readonly destroyRef = inject(DestroyRef);
  private readonly updaterService = inject(UpdaterService);
  private readonly inlineEditService = inject(InlineEditService);
  private readonly isInlineEdit = toSignal(this.inlineEditService.isInlineEdit$);

  _control: FormControl<string[] | null> = new FormControl<string[]>([]);
  _label = '';

  constructor() {
    super();

    this.updaterService.clearedField$
      .pipe(
        takeUntilDestroyed(),
        filter((f: FieldValueInterface) => f.fieldId === this._fieldId()),
      )
      .subscribe((_) => this.clear());
    this.updaterService.updatedField
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((data) => {
          if (data.stringValue && data.stringValue != '' && data.fieldId && data.fieldId === this._fieldId()) {
            this.control.setValue(data.stringValue);
            this.control.markAsDirty();
          }
        }),
      )
      .subscribe();
  }

  override set control(control: UntypedFormControl) {
    this._control = control;
  }
  override get control(): UntypedFormControl {
    return this._control;
  }

  set formGroup(group: UntypedFormGroup) {
    // Do nothing
  }

  _fieldId = signal('');
  override set fieldId(fieldId: string) {
    this._fieldId.set(fieldId);
  }

  override set label(label: string) {
    this._label = label;
  }
  override get hasModification(): boolean {
    return this.control?.dirty;
  }
  override get value(): FieldValueInterface {
    return {
      fieldId: this._fieldId(),
      stringValues: {
        values: this._control.value || [],
      },
    };
  }

  saveField(): void {
    this.inlineEditService.saveInlineEdit(this.objectType, [
      {
        fieldId: this.fieldId,
        stringValues: this.control.value,
      },
    ]);
  }

  clear(): void {
    this.control.reset();
    this.control.markAsDirty();
    if (this.isInlineEdit()) {
      this.saveField();
    }
  }
}
