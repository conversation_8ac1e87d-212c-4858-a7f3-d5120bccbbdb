{"ACTIONS": {"CREATE": "Create", "CANCEL": "Cancel", "SAVE": "Save", "NEXT": "Next", "BACK": "Back", "FINISH": "Finish", "APPLY": "Apply", "IMPORT": "Import", "SELECT": "Select", "SELECTED": "Selected", "SEARCH": "Search", "ADD": "Add", "DELETE": "Delete", "EDIT": "Edit", "SEND": "Send", "CONFIRM": "Confirm", "CRM": {"EXPORT": {"LABEL": "Export", "TITLE": "Export {{ objectType }}", "CONFIRMATION_MESSAGE": "This will export {{ total }} {{ objectType }}. Note that if the total number of {{ objectType }} exceeds 64000, only the first 64000 {{ objectType }} will be exported.", "CONFIRM": "Export", "CANCEL": "Cancel", "DOWNLOADING": "Downloading...", "SUCCESS": "Exported {{ objectType }}", "ERROR": "Error exporting {{ objectType }}"}}}, "ERRORS": {"GENERIC_MESSAGE": "Something went wrong", "GENERIC_LOAD_MESSAGE": "Could not load the page. Please try again.", "GENERIC_EDIT_MESSAGE": "There was an error updating. Please try again.", "RESOURCE_NOT_FOUND_ERROR": "Resource not found"}, "OBJECT_TYPES": {"CONTACT": "Contact", "CONTACT_PLURAL": "contacts", "COMPANY": "Company", "COMPANY_PLURAL": "companies", "OPPORTUNITY": "Opportunity", "OPPORTUNITY_PLURAL": "opportunities", "CUSTOM_OBJECT": "Custom object", "CUSTOM_OBJECT_PLURAL": "custom objects", "ACTIVITY": "Activity", "NOTE": "Note", "EMAIL": "Email", "CALL": "Call", "MEETING": "Meeting", "TASK": "Task", "COMMUNICATION": "Communication", "OBJECT": "Object", "CUSTOM_FIELDS": "Custom Fields", "FOR_LABEL": "for"}, "CUSTOM_FIELDS": {"CREATE": "Create field", "EDIT": "Edit field", "DELETE": "Delete field", "FIELD_NAME": "Field name", "FIELD_DESCRIPTION": "Field description", "FIELD_TYPE": "Field type", "OBJECT": "Object", "FIELD_TYPES": {"DATE": "Date", "DATE_TIME": "Date and time", "INTEGER": "Number", "STRING": "Text", "FLOAT": "Decimal number", "EMAIL": "Email", "PHONE_NUMBER": "Phone number", "BOOLEAN": "True or false"}, "FIELD_EXAMPLES": {"DATE": "e.g. birthday", "DATE_TIME": "e.g. next meeting", "INTEGER": "e.g. number of employees", "STRING": "e.g. address", "FLOAT": "e.g. review score"}, "ERROR": {"FIELD_NAME_REQUIRED": "Field name is required", "FIELD_TYPE_REQUIRED": "Field type is required", "OBJECT_REQUIRED": "Object is required", "FAILED_TO_CREATE": "Failed to create custom field", "FAILED_TO_EDIT": "Failed to edit custom field", "FAILED_TO_DELETE": "Failed to delete custom field", "INVALID_FORM": "Almost there! Please correct the highlighted issues"}, "SUCCESS": {"CREATED": "Custom field created successfully", "EDITED": "Custom field updated successfully", "DELETED": "Custom field deleted successfully"}, "DELETE_MODAL": {"TITLE": "Delete custom field?", "DESCRIPTION": "Are you sure you want to delete this custom field? This cannot be undone."}, "SYSTEM_FIELDS_MESSAGE": "This is a system field and cannot be edited.", "EXTERNAL_ID": {"LABEL": "External Identifier", "SET": "Set", "SET_IDENTIFIER": "Set Identifier", "EDIT_IDENTIFIER": "Edit Identifier", "IDENTIFIER_VALIDATION_MESSAGE": "Only lowercase letters, numbers, dash (-) and underscore (_) permitted", "IDENTIFIER_DUPLICATE_MESSAGE": "This external identifier is currently in use", "EXTERNAL_IDENTIFIER_DESCRIPTION": "This is what will be used to identify this field when working with the API. It cannot be edited once the field is created."}}, "ASSOCIATIONS": {"ACTIONS": {"TITLE": "Association", "REMOVE": "Remove association", "DETAILS": "View details", "EDIT_TAGS": "Edit association tags", "SHOW_MORE": "Show {{count}} more", "VIEW_ALL": "View all"}, "SUCCESS": {"GENERIC": "Successfully created association", "REMOVE": "Successfully removed association", "PARTIAL_WITH_DELAY": "Created {{createdCount}} of {{totalCount}} associations. It may take a few minutes for them to appear", "PARTIAL_SUCCESS": "Created {{createdCount}} of {{totalCount}} associations", "SINGLE_FAILURE": "Failed to create association"}, "ERROR": {"CREATE": "Failed to create association(s)", "REMOVE": "Failed to remove association"}, "COMPANY": {"TITLE": "Companies", "ADD_COMPANY": "Add company", "ERROR": "Failed to create an account for this company", "INVITE": "+ Invite to {{appName}}", "LAST_LOGIN_DATE": "Last login: {{lastLoginDate}}", "NEVER_LOGGED_IN": "Never"}, "PARENT_CHILD_COMPANIES": {"TITLE": "Companies", "CHILD_SECTION_TITLE": "Child companies", "PARENT_SECTION_TITLE": "Parent company", "ADD_CHILD_COMPANY": "Add child company", "SET_PARENT_COMPANY": "Set parent company", "ADD_CHILD_COMPANY_MODAL": {"TITLE": "Add child company to {{companyName}}", "SUCCESS": "Child company added", "ERROR": "Error adding child company"}, "SET_PARENT_COMPANY_MODAL": {"TITLE": "Set parent company for {{companyName}}", "SUCCESS": "Parent company set", "ERROR": "Error setting parent company"}}, "APP_ACCESS_MODAL": {"TITLE": "Invite to {{appName}}?", "DESCRIPTION": "{{contactName}} will be added as a user for {{companyName}}.", "PERMISSIONS_INFO": "You can edit their permissions at any time under the <b>Accounts > Manage Users</b> tab.", "SEND_WELCOME_EMAIL": "Send welcome email", "INVITE": "Invite", "CANCEL": "Cancel", "SUCCESS": "Contact has been successfully invited", "ERROR": "Error inviting contact"}, "MODAL": {"TITLE_CONTACT_MODAL": "Add companies to {{contactFullName}}", "ADD_ASSOCIATION": "Add company", "TITLE_COMPANY_MODAL": "Add contacts to {{companyName}}", "TITLE_ASSOCIATE_CONTACTS": "Add contacts to {{objectName}}", "TITLE_ASSOCIATE_COMPANIES": "Add companies to {{objectName}}", "TITLE_ASSOCIATION": "Association details", "TITLE_TAGS": "Association tags", "HINT_TAGS": "New tag...", "DESCRIPTION_TAGS": "Use tags to describe this relationship", "CREATE_NEW_CONTACT": "Create new contact"}, "COMPANY_MODAL": {"PRIMARY_COMPANY": "Set as primary company", "PRIMARY_DESCRIPTION": "This will replace any current primary company associations"}, "ADMIN_NOTES": {"TITLE": "Admin Notes"}, "PRODUCTS": {"TITLE": "Products", "NO_PRODUCTS": "No products yet", "ACTIVE_SINCE": "Active since", "RENEWS": "Renews", "COMMITTED_UNTIL": "Committed until", "TRIAL_SINCE": "Trial since", "ENDS": "Ends", "DAYS_REMAINING": "{{days}} days remaining", "TRIAL_UPGRADE": "Upgrade product to continue service", "ERROR": "Error loading data, refresh and try again", "STATUS": {"TRIAL": "Trial", "TRIAL_ENDED": "Trial ended"}}, "REPORTS": {"TITLE": "Reports"}, "OPPORTUNITIES": {"TITLE": "Opportunities"}, "UTM_ATTRIBUTES": {"TITLE": "UTM Attributes"}, "AUTOMATIONS": {"TITLE": "Automations"}, "CAMPAIGNS": {"TITLE": "Campaigns", "ADD_ASSOCIATION": "Add campaign", "START": "Start campaign...", "START_NOW": "Campaign will start on", "START_LATER": "Campaign will start at the scheduled time", "ADD_TO_CAMPAIGN": "Add to campaign", "STATUS": {"ONGOING": "Ongoing", "PAUSED": "Paused", "COMPLETED": "Completed"}, "MENU": {"PAUSE": {"CONFIRMATION": {"TITLE": "Pause {{ title }} for {{ recipient }}?", "MESSAGE": "Scheduled campaign events will be paused for this recipient only. This action may take a few minutes to complete.", "CONFIRM": "Pause campaign", "DECLINE": "Cancel"}, "PAUSE_CAMPAIGN": "Pause campaign", "SUCCESS": "Campaign paused", "FAILURE": "There was a problem pausing the campaign. Refresh the page and try again."}, "RESUME": {"CONFIRMATION": {"TITLE": "Resume {{ title }} for {{ recipient }}?", "MESSAGE": "The campaign will resume as scheduled for this recipient only. This recipient won’t receive emails they’ve already received. This action may take a few minutes to complete.", "CONFIRM": "Resume campaign", "DECLINE": "Cancel"}, "RESUME_CAMPAIGN": "Resume campaign", "SUCCESS": "Campaign resumed", "FAILURE": "There was a problem resuming the campaign. Refresh the page and try again."}}, "MODAL": {"SUCCESS": "Successfully added to campaign", "ERROR": "Error adding to campaign", "FAILED_ADDRESS_ERROR_EMAIL": "Failed to add contact. Email address missing.", "FAILED_ADDRESS_ERROR_SMS": "Failed to add contact. Phone number missing.", "UPDATED": "Last updated:", "NO_CAMPAIGNS": "No campaigns found", "SEARCH_PLACEHOLDER": "Search active campaigns"}, "START_CAMPAIGN": {"NOW": "Now", "LATER": "Later"}, "VIEW_ALL": "View all"}, "FILES": {"TITLE": "Files"}, "FULFILLMENT_PROJECTS": {"TITLE": "Projects", "SUBTITLE": {"DUE": "Due", "COMPLETED": "Completed"}, "STATUS": {"OPEN": "Open", "IN_PROGRESS": "In progress", "WAITING_ON_CUSTOMER": "Waiting on customer", "COMPLETED": "Completed", "OVERDUE": "Overdue"}, "NO_PROJECTS": "No projects yet"}, "ORDERS": {"TITLE": "Orders"}, "COMPANY_PRIORITIES": {"TITLE": "Company Priorities"}, "PROPOSALS": {"TITLE": "Proposals"}, "TASKS": {"TITLE": "Tasks"}, "PANEL_IN_PROGRESS": {"BUTTON": "View / add {{ category }}", "DESCRIPTION": "We’re still working on this! To add or view existing {{ category }}, you’ll be taken back to the account details page.", "ALERT": {"TITLE": "The associations panel is still in progress", "DESCRIPTION": "- You'll be taken back to the legacy page to access some features."}}, "CONTACT": {"TITLE": "Contacts", "ADD_ASSOCIATION": "Add contact", "ACTIONS": {"EDIT": "Edit contact"}}, "REMOVE_DIALOG": {"TITLE": "Remove association?", "MESSAGE": "{{removeName}} will no longer be associated with {{fromName}}.", "CONFIRM": "Remove", "CANCEL": "Cancel", "SUCCESS": "Association removed", "ERROR": "Error removing association"}, "ACCOUNT": {"TITLE": "Account", "CREATE_ACCOUNT": "Create account", "CREATE_DESCRIPTION": "Accounts handle the billing relationship with a company. Use them to submit orders, create invoices, and collect payments.", "GO_TO_ACCOUNT": "Go to account", "OPEN_BUSINESS_APP": "Open in"}, "EDIT_ASSOCIATION_DIALOG": {"ACTION": "Edit association tags", "TITLE": "Association details", "SUCCESS": "Association tags updated", "ERROR": "Error updating tags"}, "FORM_FIELD": {"PLACEHOLDER": "Associate object..."}}, "CONTACT": {"TITLE": "Contacts", "ALL_OBJECTS": "All contacts", "MY_OBJECTS": "My contacts", "ADD_OBJECT_BUTTON": "Create contact", "VIEW_OBJECT": "View contact", "CREATE_PAGE": {"TITLE": "Create contact", "SAVE": {"SUCCESS": "Contact creation was successful", "ERROR": "Something went wrong"}, "LOAD": {"ERROR": "Something went wrong"}}, "EDIT_PAGE": {"TITLE": "Edit contact", "SAVE": {"SUCCESS": "Contact was updated successfully"}}, "ASSOCIATION_PANEL": {"TITLE": "Contacts", "ADD_ASSOCIATION": "Add contact", "TITLE_ASSOCIATE": "Add contacts to {{objectName}}", "CREATE_BUTTON": "Create and associate a new contact", "CONFIRM_EXISTING_PRIMARY_MODAL": {"TITLE": "Are you sure you want to proceed?", "CONFIRMATION_MESSAGE": "The following contacts already have primary associations and won't be created as primary:"}}, "ASSOCIATION_FORM": {"TITLE": "Contacts", "PLACEHOLDER": "Associate contact..."}, "CONTACT_PROFILE": {"TITLE": "Contact profile", "EDIT_CONTACT": "Edit contact", "ACTIVITIES_TIMELINE_TAB": "Activity", "ABOUT_TAB": "About", "ASSOCIATIONS_TAB": "Associations"}, "PROFILE_PAGE": {"TITLE": "Contact profile", "EDIT": "Edit contact", "PREVIOUS_PAGE_TITLE": "Contacts", "ACTIVITIES_TIMELINE_TAB": "Activity", "ABOUT_TAB": "About", "ASSOCIATIONS_TAB": "Associations", "PERSONAL_EVENT": "Your personal event links", "MEETING_ICON_TOOLTIP": "Book a meeting", "EMAIL_ICON_TOOLTIP": "Send an email", "PHONE_ICON_TOOLTIP": "Make a phone call", "CHAT_ICON_TOOLTIP": "Send a message", "NPS": "NPS: {{score}}", "MEETING_EVENT_LIST_TITLE": "Please select an event link", "LEAD_SCORE": "Score: {{score}}"}, "ACTIVITIES_TIMELINE": {"EMPTY": {"IMAGE_ALT": "Graphic showing an abstract activity card floating", "HEADER": "Activities Timeline", "HINT": "Add an Activity"}, "EMPTY_SEARCH": {"HEADER": "No activities found that match your search", "HINT": "Try changing the keywords"}, "SECTIONS": {"UPCOMING": "Upcoming"}}, "ACTIVITIES": {"UNKNOWN_NAME": "Your expert", "HEADER_ADDITIONAL_TEXT": {"NOTE": "added a note", "LOGGED_EMAIL": "logged an email", "LOGGED_CALL": "logged a call", "LOGGED_MEETING": "logged a meeting", "LOGGED_COMMUNICATION": "logged a message", "TASK": "was assigned a task", "CONVERSATION": "Conversation created"}, "CREATED_FROM": "from", "CREATED_WITH": "with"}, "RECORD_CHANGE_ACTIVITY": {"CREATED": "Contact created", "CREATED_VIA": "Contact created via", "DETAILS_UPDATED": "Contact details updated"}, "LIST_OBJECTS_PAGE": {"BACK_TO_USERS": "Back to Manage Users"}, "LIST_OBJECTS_TABLE": {"EMPTY_TABLE": {"IMAGE_ALT": "Graphic showing a contact to be filled in", "HEADER": "Create your first contact", "HINT": "Add a single contact or import existing contact data from a CSV file"}, "CUSTOM_COLUMNS": {"FULL_NAME": "Name", "PRIMARY_COMPANY_NAME": "Primary company name"}, "ACTIONS": {"EDIT": {"TITLE": "Edit contact"}, "DELETE": {"ACTION": "Delete contact", "TITLE": "Delete {{ name }}?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted contact", "ERROR": "Error deleting contact"}, "DELETE_SELECTED": {"ACTION": "Delete contacts", "TITLE": "Delete contacts?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted contacts", "ERROR": "Error deleting contacts"}}, "PIPELINE": {"EMPTY_STATE": {"TITLE": "Set up your pipeline and create your first contact", "MESSAGE": "Start with a default pipeline, or create a custom pipeline that matches your sales process", "SETUP_PIPELINE": "Set up pipeline"}}}, "TABLE_FILTERS": {"HEADER": "Contacts"}, "STANDARD_FIELD_RULES": {"UNIQUE_FIELD_VALUE": {"ENFORCED": "A contact with this {{label}} already exists", "WARNING": "One or more contacts with this {{label}} already exists", "PERMISSION_DENIED": "A contact with this {{label}} already exists in your organization"}, "E164_FORMAT": {"WARNING": "Incorrect phone number format (ex. *************)"}}, "SELECT_MODAL": {"ONE_SELECTED": "Add contact", "MORE_SELECTED": "Add {{totalSelected}} contacts", "NO_SELECTED": "Please select a contact", "CREATE_NEW": "Create new contact"}, "SELECT_FIELD": "Select a contact field"}, "ACTIVITY_TYPES": {"NOTE": "Note", "EMAIL": "Email", "CALL": "Call", "MEETING": "Meeting", "CAMPAIGN": "Campaign email", "TASK": "Task", "MORE": "More", "YESWARE_CAMPAIGN": "Yesware campaign email", "COMMUNICATION_SUBTYPES": {"INBOX": "Inbox", "SMS": "SMS", "CAMPAIGN_SMS": "Campaign SMS", "LINKEDIN": "LinkedIn"}}, "ACTIVITY_LOGGER": {"ADD": "Add", "LOG_ACTIVITY": "Log activity", "CREATE_TASK": "Create task", "SAVE": "Save", "CANCEL": "Cancel", "SUCCESS_MESSAGE": "Successfully created activity", "ERROR_MESSAGE": "There was a problem creating your activity. Please try again.", "EDIT_SUCCESS_MESSAGE": "Successfully updated activity", "EDIT_ERROR_MESSAGE": "There was a problem updating your activity. Please try again.", "ERROR_SET_REQUIRED_FIELDS": "Fill all required fields and address all errors", "ERROR_REQUIRE_DESCRIPTION": "Description is required", "ERROR_MAX_LENGTH_EXCEEDED": "Description must be less than {{maxLength}} characters", "ERROR_REQUIRE_EMAIL_DIRECTION": "Email direction is required", "ERROR_REQUIRE_CALL_DIRECTION": "Call direction is required", "ERROR_REQUIRE_EMAIL_DATE": "Email date is required", "ERROR_REQUIRE_CALL_DATE": "Call date is required", "ERROR_REQUIRE_MEETING_DATE": "Meeting date is required", "ERROR_REQUIRE_TASK_DUE_DATE": "Task due date is required", "ERROR_REQUIRE_COMMUNICATION_DATE": "Date is required", "ERROR_REQUIRE_TASK_TYPE": "Task type is required", "ERROR_REQUIRE_TASK_ASSIGNEE": "Task assignee is required", "ERROR_REQUIRE_TASK_NAME_REQUIRED": "Task name is required", "ERROR_REQUIRE_COMMUNICATION_OUTCOME": "Outcome is required", "EMPTY_ASSOCIATIONS": "Activities must have at least one association", "NOTE_PLACEHOLDER": "Share an update...", "ASSOCIATIONS": "Associations", "ADD_COMPANY_ASSOCIATION": "Associate company...", "ADD_CONTACT_ASSOCIATION": "Associate contact...", "ADD_TEAM_MEMBER_ASSOCIATION": "Add team members...", "ADD_OPPORTUNITY_ASSOCIATION": "Associate opportunity...", "ADD_TAGS": "Add tags...", "TAGS": "Tags", "LOGGED_EMAIL": {"DATE_PLACEHOLDER": "Email date", "DESCRIPTION_PLACEHOLDER": "Describe the email...", "DIRECTION_PLACEHOLDER": "Email direction"}, "LOGGED_CALL": {"DATE_PLACEHOLDER": "Call date", "NOTE_PLACEHOLDER": "Describe the call...", "DIRECTION_PLACEHOLDER": "Call direction", "OUTCOME_PLACEHOLDER": "Call outcome", "STATUS_PLACEHOLDER": "Call status"}, "LOGGED_MEETING": {"DATE_PLACEHOLDER": "Meeting date", "NOTE_PLACEHOLDER": "Describe the meeting...", "DURATION_PLACEHOLDER": "Meeting duration", "OUTCOME_PLACEHOLDER": "Meeting outcome", "STATUS_PLACEHOLDER": "Meeting status"}, "LOGGED_TASK": {"TITLE_PLACEHOLDER": "Add a task", "INSTRUCTIONS_PLACEHOLDER": "Add task instructions", "PRIORITY_PLACEHOLDER": "Task priority", "TYPE_PLACEHOLDER": "Task type", "DUE_DATE_PLACEHOLDER": "Task due date", "OWNER_PLACEHOLDER": "Task assignee", "CREATE": {"SUCCESS": "Successfully created task", "ERROR": "There was a problem creating your task. Please try again."}, "EDIT": {"SUCCESS": "Successfully updated task", "ERROR": "There was a problem updating your task. Please try again."}}, "LOGGED_COMMUNICATION": {"DIRECTION_PLACEHOLDER": "Outcome", "DESCRIPTION_PLACEHOLDER": "Share an update..", "DATE_PLACEHOLDER": "Date"}, "LOGGED_FOLLOW_UP_TASK": {"CREATE": "Create follow-up task", "TITLE": "Follow-up task", "FOLLOW_UP_WITH": "Follow up with"}, "ACTIVITY_TYPE_SELECTOR_ARIA_LABEL": "Activity type selector"}, "ACTIVITY": {"FEED": {"TITLE": "Activity feed"}, "TITLE": "Activity", "MENU": {"EDIT": "Edit activity", "COPY_LINK": "Copy link"}, "AUDIO_PLAYBACK_ERROR": "Audio playback is not supported", "DIRECTION": "Direction", "OUTCOME": "Outcome", "STATUS": "Status", "DATE": "Date", "DURATION": "Duration", "LOCATION": "Location", "DUE": "Due", "OVERDUE": "Overdue", "OUTBOUND": "Outbound", "INBOUND": "Inbound", "FORWARDED": "Forwarded", "OUTCOME_OPTIONS": {"BUSINESS_REVIEW": "Business review", "DEMO_SCHEDULED": "Demo scheduled", "DEMO_COMPLETED": "Demo completed", "DISCOVERY": "Discovery", "INTERESTED": "Interested", "NO_INTEREST": "No interest", "PRESENTATION": "Presentation", "QUALITY_CONNECT": "Quality connect", "RESCHEDULING": "Rescheduling", "REFERRED": "Referred to other representatives", "TIMELINE_6_12_MONTHS": "Timeline 6-12 months"}, "LOGGED_EMAIL": {"DIRECTION": "Email direction"}, "LOGGED_CALL": {"DIRECTION": "Call direction", "STATUS": "Call status", "OUTCOME": "Call outcome"}, "EMAIL_TITLE": {"FORWARDED": "forwarded to", "INBOUND": "sent to", "OUTBOUND": "from", "BOUNCED": "bounced by", "CLICKED": "clicked by", "DEFERRED": "deferred for", "DELIVERED": "delivered to", "DROPPED": "dropped by", "FAILED": "failed for", "OPENED": "opened by", "PROCESSED": "processed for", "RECEIVED": "received by", "REPLY": "replied by", "RESUBSCRIBED": "resubscribed by", "SEND": "sent to", "SENT": "sent to", "SPAM": "marked as spam for", "UNSUBSCRIBED": "unsubscribed by", "FORWARDED_V2": "forwarded", "INBOUND_V2": "sent", "OUTBOUND_V2": "from", "BOUNCED_V2": "bounced", "CLICKED_V2": "clicked", "DEFERRED_V2": "deferred", "DELIVERED_V2": "delivered", "DROPPED_V2": "dropped", "FAILED_V2": "failed", "OPENED_V2": "opened", "PROCESSED_V2": "processed", "RECEIVED_V2": "received", "REPLY_V2": "replied", "RESUBSCRIBED_V2": "resubscribed", "SEND_V2": "sent", "SENT_V2": "sent", "UNSUBSCRIBED_V2": "unsubscribed", "EMAIL_TO": "to", "EMAIL_FOR": "for", "EMAIL_BY": "by", "EMAIL_FROM": "from"}, "EMAIL_STATUS": {"FORWARDED": "Forwarded", "BOUNCED": "Bounced", "CLICKED": "Clicked", "DEFERRED": "Deferred", "DELIVERED": "Delivered", "DROPPED": "Dropped", "FAILED": "Failed", "OPENED": "Opened", "PROCESSED": "Processed", "RECEIVED": "Received", "REPLY": "Replied", "RESUBSCRIBED": "Resubscribed", "SEND": "<PERSON><PERSON>", "SENT": "<PERSON><PERSON>", "SPAM": "Marked as spam", "UNSUBSCRIBED": "Unsubscribed"}, "SMS_TITLE": {"SENT": "sent to", "DELIVERED": "delivered to", "FAILED": "failed for", "UNDELIVERED": "undelivered to"}, "TASK": {"DUE_DATE": "Due date", "ASSIGNED_TO": "Assigned to", "TYPE": "Type", "PRIORITY": "Priority", "LOW": "Low", "MEDIUM": "Medium", "HIGH": "High", "TO_DO": "To-do", "EMAIL": "Email", "CALL": "Call", "MESSAGE": "Message", "MEETING": "Meeting", "LINKEDIN": "LinkedIn"}, "MEETING": {"TITLE": "Meeting", "SCHEDULED": "Scheduled", "RESCHEDULED": "Rescheduled", "COMPLETED": "Completed", "CANCELLED": "Cancelled", "NO_SHOW": "No show", "5_MIN": "5 minutes", "15_MIN": "15 minutes", "30_MIN": "30 minutes", "1_HOUR": "1 hour", "1_HOUR_30_MIN": "1 hour 30 minutes", "2_HOURS_OR_MORE": "2 hours or more", "STATUS_HEADER": {"SCHEDULED": "Meeting scheduled", "RESCHEDULED": "Meeting rescheduled", "CANCELLED": "Meeting cancelled", "COMPLETED": "Meeting completed", "NO_SHOW": "Meeting no show", "UNKNOWN_STATUS": "Meeting status unknown", "BY_OWNER": "by {{owner<PERSON>ame}}"}, "SUMMARY_BY_AI": "Summarized by AI", "SENTIMENT_SCORE": "Sentiment score", "SALES_SCORE": "Sales score", "MEETING_ANALYSIS": {"VIEW_SUMMARY": "View summary", "VIEW_SUMMARY_TOOLTIP": "Click on the title to see the full summary", "SUMMARY_TITLE": "Summary", "KEY_TAKEAWAYS": "Key takeaways", "ACTION_ITEMS": "Action items", "SENTIMENT_SCORE": "Sentiment score", "ASSOCIATIONS": "Associations", "SALES_SCORE": "Sales score", "DOWNLOAD_TRANSCRIPT": "Download transcript", "VIEW_RECORDING": "View recording", "GO_TO_MEETING": "Go to meeting", "CLOSE": "Close", "DOWNLOAD_WILL_START_SHORTLY": "Your download will start shortly", "FEEDBACK": {"TITLE": "What did you not like?", "INSTRUCTIONS": "Please select at least one reason why you did not like the AI generated meeting summary. This will help us make better summaries.", "INACCURATE_SUMMARY": "Inaccurate summary", "INACCURATE_TAKEAWAYS": "Inaccurate takeaways", "INACCURATE_ACTION_ITEMS": "Inaccurate action items", "LACK_OF_DETAILS": "Lack of details", "WRONG_SPEAKERS": "Wrong / unidentified speakers", "OTHER": "Other", "ADDITIONAL_INFO": "Additional information", "SEND_FEEDBACK": "Send feedback", "FEEDBACK_CANCEL": "Cancel", "SUCCESS": "<PERSON><PERSON><PERSON> sent successfully", "ERROR": "Error sending feedback"}}}, "CALL": {"TITLE": "Call", "CONNECTED": "Connected", "BUSY": "Busy", "REJECTED": "Rejected", "LEFT_VOICEMAIL": "Left voicemail", "NO_ANSWER": "No answer", "NOT_IN_SERVICE": "Not in service", "WRONG_NUMBER": "Wrong number", "TRANSCRIPT": "Call transcript", "VIEW_TRANSCRIPT": "View call transcript"}, "NOTE": {"FORM_SUBMITTED": "Form submitted", "LIST": {"LIST_MEMBERSHIP": "List membership", "ADDED_TO": "Added to", "REMOVED_FROM": "Removed from"}}, "COMMUNICATION": {"TYPE": "Type", "CONVERSATION_CREATED": "Conversation created via", "CONVERSATION_EVENT": "Conversation event via"}, "RECORD_CHANGE": {"CREATED": "Created", "CREATED_VIA": "Created via", "SALESPERSON": "Salesperson:", "IMPORTANT_FIELD_UPDATED_SALESPERSON_UNKNOWN": "Deleted salesperson", "BY": "by"}, "UNKNOWN_TITLE": "An activity occurred", "ASSOCIATION": "association", "ASSOCIATIONS": "associations", "POPOVER": {"CONTACTS": "Contacts", "COMPANIES": "Companies", "OPPORTUNITIES": "Opportunities", "TEAM_MEMBERS": "Team members"}, "HIDE_RELATED_ACTIVITIES": "Hide related activities", "SHOW_RELATED_ACTIVITIES": "Show related activities", "LOGGED_ACTIVITY": {"HEADER_ADDITIONAL_TEXT": {"NOTE": "Note", "EMAIL": {"OUTBOUND": "Outbound email logged", "INBOUND": "Inbound email logged"}, "CALL": {"OUTBOUND": "Outbound call logged", "INBOUND": "Inbound call logged"}, "MEETING": "Meeting logged", "COMMUNICATION": {"INBOX": "Message logged", "SMS": "SMS logged", "LINKEDIN": "LinkedIn message logged"}, "TASK": "Task assigned", "BY": "by", "TO": "to"}}}, "AI_SUMMARY": {"TITLE": "Profile summary", "ERROR": "Error loading profile summary", "NO_SUMMARY": "There isn’t enough data to provide actionable insights. Check back later.", "REGENERATE_SUMMARY": "Regenerate summary", "BUTTON": {"TITLE": "Summarize"}, "MODAL": {"TITLE": "{{ objectName }} summary", "REGENERATE_SUMMARY": "Try again", "CONFIRMATION": "Got it"}}, "COMPANY": {"TITLE": "Companies", "ALL_OBJECTS": "All companies", "MY_OBJECTS": "My companies", "ADD_OBJECT_BUTTON": "Create company", "PROSPECT_OBJECT_BUTTON": "Find leads", "VIEW_OBJECT": "View company", "DEFAULT_NAME": "this company", "CREATE_PAGE": {"TITLE": "Create company", "SAVE": {"SUCCESS": "Company creation was successful"}}, "EDIT_PAGE": {"TITLE": "Edit company", "SAVE": {"SUCCESS": "Company was updated successfully"}}, "ASSOCIATION_PANEL": {"TITLE": "Companies", "ADD_ASSOCIATION": "Add company", "TITLE_ASSOCIATE": "Add companies to {{objectName}}", "CREATE_BUTTON": "Create and associate a new company", "SET_PRIMARY_MODAL": {"RADIO_BUTTON_TEXT": "Set as primary company", "RADIO_BUTTON_DESCRIPTION": "This will replace any current primary company associations"}, "CHANGE_PRIMARY_DIALOG": {"TITLE": "Change primary company?", "MESSAGE": "{{newAssociationName}} will replace {{previousAssociationName}} as the primary company. "}}, "ASSOCIATION_FORM": {"TITLE": "Companies", "PLACEHOLDER": "Associate company..."}, "RECORD_CHANGE_ACTIVITY": {"CREATED": "Company created", "CREATED_VIA": "Company created via", "DETAILS_UPDATED": "Company details updated", "IMPORTANT_FIELD_UPDATED": "{{<PERSON><PERSON><PERSON>}} updated by", "IMPORTANT_FIELD_UPDATED_SALESPERSON": "Assigned to", "IMPORTANT_FIELD_UPDATED_SALESPERSON_NOT_ASSIGNED": "Unassigned", "IMPORTANT_FIELD_UPDATED_SALESPERSON_UNKNOWN": "deleted salesperson"}, "OPPORTUNITY_CHANGE_ACTIVITY": {"OPPORTUNITY_UPDATED_BY": "Opportunity updated by", "OPPORTUNITY_UPDATED": "Opportunity updated", "STATUS_CHANGE_MESSAGE": "Open →", "REASON_MESSAGE": "Reason:", "NOTES_MESSAGE": "Notes:", "STATUS": "Status:", "BY": "by"}, "PROFILE_PAGE": {"TITLE": "Company profile", "EDIT": "Edit company", "ACTIVITIES_TIMELINE_TAB": "Activity", "ABOUT_TAB": "About", "ASSOCIATIONS_TAB": "Associations", "PREVIOUS_PAGE_TITLE": "Companies", "LEAD_SCORE": "Score: {{score}}"}, "LIST_OBJECTS_PAGE": {"BACK_TO_ACCOUNTS": "Back to Manage Accounts"}, "LIST_OBJECTS_TABLE": {"EMPTY_TABLE": {"IMAGE_ALT": "Graphic showing a company to be filled in", "HEADER": "Create your first company", "HINT": "Add a single company or import existing company data from a CSV file"}, "ACTIONS": {"EDIT": {"TITLE": "Edit company"}, "DELETE": {"ACTION": "Delete company", "TITLE": "Delete {{ name }}?", "CONFIRMATION_MESSAGE": "Deleting this company cannot be undone. Contacts that are associated with the company will remain, but they will no longer have the association.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted company", "ERROR": "Error deleting company"}, "DELETE_SELECTED": {"ACTION": "Delete companies", "TITLE": "Delete companies?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted companies", "ERROR": "Error deleting companies"}, "OPEN_BUSINESS_APP": "Open in", "START_AUTOMATION": "Start automation", "IMPERSONATE": "Impersonate", "APPLY_CONFIGURATION": "Apply configuration"}, "PROFILE_PAGE": {"EDIT": "Edit company"}, "PIPELINE": {"EMPTY_STATE": {"TITLE": "Set up your pipeline and create your first company", "MESSAGE": "Start with a default pipeline, or create a custom pipeline that matches your sales process", "SETUP_PIPELINE": "Set up pipeline"}}}, "TABLE_FILTERS": {"HEADER": "Companies"}, "STANDARD_FIELD_RULES": {"E164_FORMAT": {"WARNING": "Incorrect phone number format (ex. *************)"}, "UNIQUE_FIELD_VALUE": {"ENFORCED": "A company with this {{label}} already exists", "WARNING": "One or more companies with this {{label}} already exists"}}, "SELECT_MODAL": {"ONE_SELECTED": "Add company", "MORE_SELECTED": "Add {{totalSelected}} companies", "NO_SELECTED": "Please select a company", "CREATE_NEW": "Create new company"}, "SELECT_FIELD": "Select a company field"}, "TASK": {"TITLE": "Tasks", "ADD_OBJECT_BUTTON": "Create task", "VIEW_OBJECT": "View task", "RECORD_CHANGE_ACTIVITY": {"CREATED": "Task created", "CREATED_VIA": "Task created via"}, "SELECT_MODAL": {"ONE_SELECTED": "Add task", "MORE_SELECTED": "Add {{totalSelected}} tasks", "NO_SELECTED": "Please select a task", "CREATE_NEW": "Create new task"}, "CREATE_PAGE": {"TITLE": "Create task", "SAVE": {"SUCCESS": "Task creation was successful", "ERROR": "Something went wrong"}, "LOAD": {"ERROR": "Something went wrong"}}, "EDIT_PAGE": {"TITLE": "Edit task", "SAVE": {"SUCCESS": "Task was updated successfully"}}, "LIST_OBJECTS_TABLE": {"EMPTY_TABLE": {"IMAGE_ALT": "Graphic showing a task to be filled in", "HEADER": "Create your first task", "HINT": "Track, manage and complete your daily tasks"}, "ACTIONS": {"EDIT": {"TITLE": "Edit task", "ERROR_LOADING_TASK": "Failed to load resource. Contact your administrator."}, "DELETE": {"ACTION": "Delete task", "TITLE": "Delete {{ name }}?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted task", "ERROR": "Error deleting task"}, "DELETE_SELECTED": {"ACTION": "Delete tasks", "TITLE": "Delete tasks?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted tasks", "ERROR": "Error deleting tasks"}, "CREATE": {"TITLE": "Create task"}, "TASK_QUEUE": {"START_TASKS": "Start tasks", "START_TASK": "Start task", "START_COUNT": "Start {{count}} task(s)", "NO_TASKS_SELECTED": "No tasks selected to start working on", "ASSOCIATIONS_CHIP": "{{count}} associations"}, "COMPLETE": {"SINGLE": {"TITLE": "Complete task", "SUCCESS": "Task completed", "ERROR": "Error completing task"}, "PLURAL": {"TITLE": "Complete tasks", "SUCCESS": "Tasks completed", "ERROR": "Error completing {{ amount }} or {{ total }} tasks"}}}, "CUSTOM_COLUMNS": {"ASSIGNED_TO": "Assigned to", "DAYS_OVERDUE": "Overdue {{ amount }} days", "HOURS_OVERDUE": "Overdue {{ amount }} hours", "ASSOCIATED_COMPANY": "Company", "ASSOCIATED_CONTACT": "Contact"}, "PIPELINE": {"EMPTY_STATE": {"TITLE": "Set up your pipeline and create your first task", "MESSAGE": "Start with a default pipeline, or create a custom pipeline that matches your sales process", "SETUP_PIPELINE": "Set up pipeline"}}}, "TABLE_FILTERS": {"HEADER": "Tasks"}, "STATUS_UPDATE": {"SUCCESS": "Task status updated", "ERROR": "Task status update failed"}, "PERMISSION_DIALOG": {"TITLE": "Permission Required", "NO_ACCESS_MESSAGE": "You don't have permission to access {{inaccessibleCount}} of {{totalCount}} tasks", "CONTINUE_QUESTION": "Do you want to continue with the accessible tasks?"}}, "LIST_OBJECTS_TABLE": {"CUSTOM_COLUMNS": {"FULL_ADDRESS": "Address"}, "ACTIONS": {"DELETE": {"MODAL_TITLE": "Delete {{name}}?"}}, "ACTIONS_COLUMN": {"TITLE": "Actions"}, "CREATED_COLUMN": {"TITLE": "Created"}, "DATE_DEFAULT": {"INVALID": "Exact date", "TODAY": "Today", "YESTERDAY": "Yesterday", "TOMORROW": "Tomorrow", "THIS_WEEK": "This week", "LAST_WEEK": "Last week", "NEXT_WEEK": "Next week", "THIS_MONTH": "This month", "LAST_MONTH": "Last month", "NEXT_MONTH": "Next month", "THIS_QUARTER": "This quarter", "LAST_QUARTER": "Last quarter", "NEXT_QUARTER": "Next quarter", "THIS_YEAR": "This year", "LAST_YEAR": "Last year", "NEXT_YEAR": "Next year"}, "EXTERNAL_ID_COLUMN": {"TITLE": "External ID"}, "ID_COLUMN": {"TITLE": "ID"}, "UPDATED_COLUMN": {"TITLE": "Updated"}, "STANDARD_FIELD_RULES": {"UNIQUE_FIELD_VALUE": {"ENFORCED": "A company with this {{label}} already exists", "WARNING": "One or more companies with this {{label}} already exists"}}, "VIEW_TABS": {"EDIT_TABS": "Edit views", "NEW_VIEW": "New view", "HINT": "Organise your workspace by showing, hiding, renaming or creating new pre-saved views. New views will be made based on your currently applied filters.", "CUSTOM_VIEWS_TAB_GROUP": "Custom views", "DEFAULT_VIEWS_TAB_GROUP": "Default views", "ERRORS": {"NAME_REQUIRED": "Changes cannot be saved as one or more view is missing a name", "FAILED_TO_SAVE_DEFAULT_VIEWS": "Failed to save changes to default views", "FAILED_TO_SAVE_CUSTOM_VIEWS": "Failed to save changes to custom views", "FAILED_TO_SAVE_VIEW_ORDER": "Failed to save changes to view order"}}}, "CRM_FORM_SERVICE": {"RESOURCE_NOT_FOUND_ERROR": "Resource not found"}, "STANDARD_FIELD_GROUPS": {"CONTACT": {"BASIC_INFORMATION": "Basic information", "BASIC_GROUP2_INFORMATION": "Primary location", "BASIC_GROUP3_INFORMATION": "About contact", "COMMUNICATION_CONSENT": "Communication consent"}, "COMPANY": {"BASIC_INFORMATION": "Basic information", "ADDITIONAL_INFORMATION": "Additional information"}, "OPPORTUNITY": {"BASIC_INFORMATION": "Basic information", "ADDITIONAL_INFORMATION": "Additional information"}, "CUSTOM_FIELDS": "Custom fields"}, "STANDARD_FIELD_RULES": {"CONTACT": {"FIRST_OR_LAST_NAME_OR_PHONE_OR_EMAIL_REQUIRED": "One of first name, last name, phone or email is required"}, "COMPANY": {"COMPANY_NAME_REQUIRED": "Company name is required"}, "OPPORTUNITY": {"OPPORTUNITY_NAME_REQUIRED": "Opportunity name is required", "PIPELINE_REQUIRED": "Pipeline is required", "STAGE_REQUIRED": "Stage is required"}, "CUSTOM_OBJECT": {"CUSTOM_OBJECT_NAME_REQUIRED": "Name is required"}}, "PAGE_NOT_FOUND": {"HEADER": "404 Not Found", "HINT": "The page or information requested could not be found."}, "BULK_IMPORT": {"TITLE": "Bulk import", "UPLOAD_CSV_STEP": {"TITLE": "Upload CSV", "INSTRUCTIONS": "Upload an existing CSV of customer data.", "REQUIREMENTS": {"CONTACT": "A name, phone number <strong>OR</strong> email must be included for each contact.", "COMPANY": "A name must be selected for each company.", "UNIQUE_HEADER_NAMES": "Each column header must be unique.", "SALESPERSON_MAPPING": "Salesperson emails can be mapped to Salesperson ID for contacts and companies.", "PHONE_NUMBER_RECOMMENDATION": "When uploading phone numbers outside of the US, specify the country name in a separate column."}, "MAXIMUM_FILE_SIZE": "Maximum File Size: 5MB", "REQUIRED": "This field is required", "SUCCESSFUL_UPLOAD": "Successfully uploaded {{filename}}", "DOWNLOAD_CSV_TEMPLATE": "Download CSV template"}, "MAP_FIELDS_STEP": {"TITLE": "Map fields", "INSTRUCTIONS": "Confirm that the columns in your file map to the correct fields. A name, phone number <strong>OR</strong> email must be selected for contacts. A name must be selected for companies.", "PARTNER_INSTRUCTIONS": "Salesperson emails can be mapped to Salesperson ID for contacts and companies.", "COLUMN_HEADER": "Column header", "PREVIEW": "Preview", "IMPORT_AS": "Import as", "MAP_TO": "Map to", "ARIA_LABEL": "Map to fields", "OTHER_FIELDS": "Other fields", "DO_NOT_IMPORT": "Do not import", "REQUIRED": "Mapping a name, phone number OR email is required to proceed", "UNABLE_TO_MAP_FIELD": "{{fieldName}} is already mapped to {{header}}", "NO_IMPORT_TYPES_SELECTED": "Please select an import type", "DUPLICATE_HEADER_NAME": "Duplicate header", "DUPLICATE_HEADER_NAME_DESCRIPTION": "This column header was detected more than once in your file. You are still able to import, but only the first occurrence of this header in your file will be used. Please ensure that each column header is unique."}, "REVIEW_IMPORT_STEP": {"TITLE": "Review import", "INSTRUCTIONS": "Importing this will take several minutes depending on the number of customers you are creating.", "ESTIMATED_TOTAL": "an estimated total of ", "ADDING_CUSTOMERS_FROM_FILENAME": "Adding {{estimatedText}}{{rows}} customers from {{csvFilename}}.", "UPDATE_DUPLICATES": "Update existing customers", "UPDATE_DUPLICATES_INSTRUCTIONS": "Check this box to update duplicates with new information. Duplicates are identified using ID and external ID for both companies and contacts, email address for contacts, and company name for companies. If left unchecked, all duplicates will be skipped.", "UPDATE_DUPLICATES_TOOLTIP": "If this is not selected, existing contacts will be skipped. This will detect duplicates based on the ID field, external ID field, and fields marked unique like email."}, "IMPORT_SUCCESS": "Started import for {{filename}}", "IMPORT_ERROR": "Uh-oh. Looks like something went wrong on our end. Please try again."}, "TABLE_FILTERS": {"ADD_FILTER": "Add filter", "CLEAR_FILTERS": "Clear all", "FIND_FILTER": "Find filter", "INVALID_FILTER_TYPE": "Filter type is invalid", "INCOMPLETE_FILTER": "Filter is incomplete. Complete the filter before applying it.", "SELECT_OPERATOR_PLACEHOLDER": "Select operator...", "SELECT_EXPRESSION_PLACEHOLDER": "Select...", "SELECT_START_PLACEHOLDER": "Start date", "SELECT_END_PLACEHOLDER": "End date"}, "MODEL_DRIVEN_FORM": {"LOADING_STATUS_ERROR": {"TITLE": "Something went wrong", "TEXT": "Please wait a few moments and try again. If the problem persists, please contact an administrator"}, "STRING_LIMIT_HINT": "Maximum {{characters}} characters", "TAG_LIMIT_ERROR": "Only {{maximumTags}} tags are allowed", "INVALID_DROPDOWN_ERROR": "Select a valid option", "SUBMITTED_PRISTINE": "You have not made any changes.", "SUBMITTED_WITH_ERROR": "Failed to create object. Please ensure that fields are filled out correctly.", "CREATE_ERROR": "Failed to create {{objectType}}. Please ensure that fields are filled out correctly.", "UPDATE_ERROR": "Failed to update {{objectType}}. Please ensure that fields are filled out correctly.", "INVALID_EMAIL_ERROR": "Email entered is invalid", "ADD_TAG_PROMPT": "Add a tag...", "ADD_STRINGLIST_ITEM_PROMPT": "Add an item...", "SELECT_PROMPT": "Select...", "INVALID_URL_ERROR": "URL entered is invalid", "PHONE": {"INVALID_PHONE_ERROR": "Phone number entered is invalid", "COUNTRY_LABEL": "Area code", "EXTENSION_LABEL": "Extension", "SEARCH_COUNTRY_LABEL": "Search", "SEARCH_COUNTRY_NO_RESULTS": "No results found"}, "BOOLEAN": {"TRUE": "Yes", "FALSE": "No"}, "ADD_FIELD_VALUE": "Add {{fieldName}}...", "EDIT_FIELD": "Edit field"}, "COLLAPSIBLE_SECTION": {"VIEW_MORE": "View more", "VIEW_LESS": "View less"}, "PERMISSION": {"TITLE": "Permission required", "DESCRIPTION": "You don't have the necessary permissions to view this page.", "REQUEST_ACCESS": "Request access from your administrator.", "PREV_PAGE": "Back to previous page", "HOME_PAGE": "Go to home page", "IMAGE_ALT": "Graphic showing denied access due to permissions"}, "CAMPAIGNS": {"TITLE": "Campaigns", "NO_CAMPAIGNS": "No email campaigns have been started for this account", "DELETED_CAMPAIGN": "Deleted Campaign", "CONTACTS": {"MODAL": {"SELECT_ALL_SUCCESS": "{{ totalObjects }} contacts added to campaign: {{ campaignTitle }}", "ID": "ID", "NAME": "Name", "EMAIL": "Email", "NO_CAMPAIGNS": "No campaigns found", "PUBLISH": "Publish now", "BACK": "Back", "START_CAMPAIGN": "Start campaign", "CANCEL": "Cancel", "RECIPIENT_COUNT": "Estimated recipients: {{recipientCount}}", "UPDATED": "Last updated: ", "SELECT_CAMPAIGN": "Add {{recipientCount}} recipients to campaign", "MISSING_EMAIL_ADDRESS": "{{contactCount}} missing email addresses — ", "MISSING_EMAIL": "contacts without email addresses will not receive campaign emails.", "SEARCH_PLACEHOLDER": "Search active campaigns", "RECIPIENTS_TOOLTIP": "The recipient count is an estimate, as it doesn't account for contacts who have unsubscribed, are already on the campaign, or have invalid or missing email addresses.", "ERRORS": {"CAMPAIGN_CONFIGURATION_ERROR": "There was a problem with the campaign configuration. Please fix and try again.", "GENERIC_ERROR": "There was a problem starting the campaign. Please try again.", "FAILED_ADDRESS_ERROR": "'Failed to add the following addresses: '", "NO_CAMPAIGN_SELECTED": "Please select a campaign", "NO_VALID_CONTACTS_SELECTED": "Please select contacts with either an email or phone number", "NO_CONTACTS_SELECTED": "No contacts have been selected. Please select campaign recipients."}, "SUCCESS": "recipients added to campaign: "}}}, "DYNAMIC_LISTS": {"TITLE": "Lists", "EMPTY_STATE": {"EMPTY_PREVIEW": "Start building your list", "EMPTY_PREVIEW_DESCRIPTION": "Use filters to segment your customers. Results will appear here."}, "MISC": {"AND": "AND"}, "COLUMNS": {"ID": "ID", "NAME": "Name", "DESCRIPTION": "Description", "LIST_TYPE": "List type", "RESOURCE_TYPE": "Object type", "STATUS": "Status", "CREATED": "Created", "UPDATED": "Updated", "ACTIONS": "Actions"}, "ITEM_TABLE_COLUMNS": {"RESOURCE_ID": "ID", "RESOURCE_NAME": "Name", "RESOURCE_URL": "URL"}, "LIST_TYPES": {"STATIC": "Static", "SMART": "Smart"}, "FILTERS": "Filters", "PREVIEW": "Preview", "ESTIMATED_LIST_SIZE": "Estimated list size", "ESTIMATED_SIZE_INFINITY": "many", "ESTIMATED_SIZE_UNKNOWN": "-", "LIST_TYPES_DESCRIPTION": "Static lists are fixed. Customers must be added or removed manually. They're useful for organizing customers based on specific criteria. \n\nSmart lists automatically add or remove the customers within them based on the filters you set. They're useful for things like targeted campaigns or responding to real-time interactions.", "CREATE_MODAL": {"TITLE": "Create list", "FORM": {"RESOURCE_TYPE": "Object", "LIST_TYPE": "List type", "NAME": "Name", "NAME_PLACEHOLDER": "Add a name...", "NAME_REQUIRED": "Name is required", "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Add a description..."}}, "LIST_DETAILS_MODAL": {"TITLE": "Details"}, "SELECT_LIST_MODAL": {"TITLE": "Add to static list", "SELECT_CONTACT_LIST_LABEL": "Add contacts to list:", "SELECT_COMPANY_LIST_LABEL": "Add companies to list:", "SELECT_LIST_REQUIRED": "List is required", "SELECT_LIST_PLACEHOLDER": "Search lists", "AMOUNT_OF_SELECTED_COMPANIES": "<strong>{{amount}}</strong> companies selected", "AMOUNT_OF_SELECTED_CONTACTS": "<strong>{{amount}}</strong> contacts selected"}, "FILTERS_EDITOR": {"SELECT_FIELD": "Select a field"}, "ERRORS": {"MISSING_LIST_NAME": "Please add a name to your list", "INVALID_LIST_TYPE": "Please select a valid list type for your list", "INVALID_RESOURCE_TYPE": "Please select a valid object type for your list", "EMPTY_FILTERS": "Please add at least one filter to the criteria", "INVALID_FIELD": "Please select a field for the filter", "INVALID_OPERATOR": "Please select an operator for the filter", "INVALID_VALUE": "Please add a value for the filter", "FAILED_TO_UPDATE": "Failed to update list", "FAILED_TO_ADD": "Failed to add to {{listName}}. Please try again.", "STILL_PROCESSING_1": "This list is processing", "STILL_PROCESSING_2": "Depending on the size of the list, it may take a while.", "STILL_PROCESSING_3": "Try checking back later, or refreshing the page."}, "PROCESSING": "Processing", "SUCCESS": {"CREATED": "List created successfully", "UPDATED": "List updated successfully", "ADDED_TO_LIST": "{{numberOfItems}} items added to {{listName}}", "ADDED_TO_LIST_SINGULAR": "{{numberOfItems}} item added to {{listName}}", "ADDING_TO_LIST": "{{numberOfItems}} items are being added to {{listName}}", "ADDING_TO_LIST_SINGULAR": "{{numberOfItems}} item is being added to {{listName}}"}, "PENDING_CHANGES_MODAL": {"TITLE": "You have unsaved changes!", "MESSAGE": "Press 'Cancel' to go back and save these changes, or 'Proceed' to lose these changes.", "ACTIONS": {"PROCEED": "Proceed", "CANCEL": "Cancel"}}, "ACTIONS": {"CREATE": "Create list", "EDIT": "Edit details", "DETAILS": "Details", "ADD_FILTER": "Add filter", "DELETE_FILTER": "Delete filter", "DELETE": "Delete", "ADD": "Add", "ADD_TO_STATIC_LIST": "Add to static list", "REMOVE_ITEM": "Remove item", "START_AUTOMATION": "Start automation", "DELETE_CONFIRMATION": {"TITLE": "Delete list?", "MESSAGE": "This action cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "List deleted", "ERROR": "Error deleting list"}, "ITEM_DELETE_CONFIRMATION": {"TITLE": "Remove item from list?", "MESSAGE": "This action cannot be undone.", "CONFIRM": "Remove item", "CANCEL": "Cancel", "SUCCESS": "Item removed from list", "ERROR": "Error removing item from list"}, "CANNOT_EDIT": "Lists cannot be edited."}}, "YESWARE": {"ACTIONS": {"ADD_TO_CAMPAIGN": "Add to Yesware campaign"}, "ERRORS": {"FAILED_TO_ADD": "Failed to add to {{name}} campaign. Please try again.", "ALREADY_ON_CAMPAIGN_SINGULAR": "Contact is already active on a campaign, and cannot be added to {{name}} campaign", "ALREADY_ON_CAMPAIGN": "Contacts are already active on a campaign, and cannot be added to {{name}} campaign"}, "SUCCESS": {"CREATED": "List created successfully", "UPDATED": "List updated successfully", "ADDED_TO_CAMPAIGN": "{{numberOfContacts}} contacts added to {{name}} campaign", "ADDED_TO_CAMPAIGN_SINGULAR": "{{numberOfContacts}} item added to {{name}} campaign", "ADDING_TO_CAMPAIGN": "{{numberOfContacts}} contacts are being added to {{name}} campaign", "ADDING_TO_CAMPAIGN_SINGULAR": "{{numberOfContacts}} contacts is being added to {{name}} campaign", "ADDED_TO_CAMPAIGN_PARTIAL": "{{successCount}} contacts added to {{name}} campaign. {{failedCount}} contacts failed to add to campaign."}, "SELECT_YESWARE_CAMPAIGN_MODAL": {"TITLE": "Add to Yesware campaign", "SELECT_YESWARE_CAMPAIGN_LABEL": "Add contacts to Yesware campaign:", "SELECT_YESWARE_CAMPAIGN_REQUIRED": "Yesware campaign is required", "SELECT_YESWARE_CAMPAIGN_PLACEHOLDER": "Search Yesware campaigns", "AMOUNT_OF_SELECTED_COMPANIES": "<strong>{{amount}}</strong> companies selected", "AMOUNT_OF_SELECTED_CONTACTS": "<strong>{{amount}}</strong> contacts selected", "NO_CAMPAIGNS": "No Yesware campaigns found"}}, "VIEWS": {"ACTIONS": {"CREATE_VIEW": "Create view"}, "DELETE_CONFIRMATION": {"TITLE": "Delete view?", "MESSAGE": "{{ viewName }} will be deleted."}, "EMPTY": "Add filters to create a view", "ERRORS": {"VIEW_NAME_REQUIRED": "View name is required", "EQUAL_TO_PRESET_NAME": "View name cannot be an existing name", "VIEW_NAME_TOO_LONG": "View name cannot be longer than 36 characters", "NO_FILTERS": "Add filters before creating a view"}, "VIEW_NAME": "View name", "VIEWS": "Views", "PRESET_FILTERS": {"ALL": "All", "NOTES": "Notes", "TASKS": "Tasks", "EMAILS": "Emails", "CALLS": "Calls", "MEETINGS": "Meetings", "COMMUNICATIONS": "Communications", "EXCLUDE_CAMPAIGNS": "Exclude campaigns"}}, "LIFECYCLE_STAGE": {"VISITOR": "Visitor", "LEAD": "Lead", "MARKETING_QUALIFIED_LEAD": "Marketing qualified lead", "SALES_ACCEPTED_LEAD": "Sales accepted lead", "SALES_QUALIFIED_LEAD": "Sales qualified lead", "PROSPECT": "Prospect", "CUSTOMER": "Customer", "FORMER_CUSTOMER": "Former customer"}, "TASK_QUEUE": {"ACTIONS": {"SKIP": "<PERSON><PERSON>", "MARK_AS_COMPLETE": "Mark as complete", "UNDO": "Undo", "NEXT": "Next", "BACK_TO_TASKS": "Back to tasks", "RESCHEDULE": "Reschedule"}, "STATUS": {"COMPLETED": "Completed", "RESCHEDULED": "Rescheduled"}, "TASKS": "Tasks", "RESCHEDULE_TASK": "Reschedule task", "ERRORS": {"MARK_AS_COMPLETE": "There was a problem marking the task as completey. Please try again.", "RESCHEDULE": "There was a problem rescheduling the task. Please try again.", "UNDO": "There was a problem updating the task. Please try again.", "INVALID_DUE_DATE": "Invalid due date"}, "SUCCESS_MESSAGES": {"RESCHEDULE": "Task rescheduled"}}, "SCORE_SETTINGS": {"TITLES": {"EDIT_SCORE": "Edit score", "POSITIVE": "Positive ({{ numberOfDefinitions }})", "NEGATIVE": "Negative ({{ numberOfDefinitions }})", "VIEW": {"POINTS": "Points: {{ points }}"}, "EDIT": {"POINTS": "Points: {{ sign }}"}}, "SUBTITLES": {"POSITIVE": "Add points to the total score when criteria has been met", "NEGATIVE": "Subtract points from the total score when criteria has been met"}, "CTA": {"ADD_CRITERIA": "Add criteria", "ADD_FILTER": "Add filter"}, "MISC": {"AND": "And", "UNSAVED_CHANGES": "Unsaved changes"}, "EMPTY_STATE": {"FILTERS": "Add filters to this criteria", "PAGE": {"TITLE": "Create scoring rule", "SUBTITLE": "Set criteria to add and subtract points from the total score based on customer behaviour and fit or use our default set of criteria to categorize your leads", "CREATE_RULE_MYSELF": "Create rule myself", "CREATE_RULE_FOR_ME": "Create rule for me"}}, "ERRORS": {"EMPTY_SCORING_DEFINITIONS": "Please add at least one criteria", "EMPTY_SCORING_RULES": "Please add at least one filter to the criteria", "INVALID_CONTRIBUTION": "Please add a valid contribuition to the criteria", "INVALID_FIELD": "Please select a field for the filter", "INVALID_OPERATOR": "Please select an operator for the filter", "INVALID_VALUE": "Please add a value for the filter", "FAILED_TO_UPDATE": "Failed to update scoring definitions"}, "SUCCESS_MESSAGES": {"UPDATE": "Scoring definitions updated"}}, "COMMON": {"YES": "yes", "NO": "no", "SNACKBAR_MESSAGE": {"COPY_SUCCESS": "Successfully copied to clipboard", "MEETING_EMAIL_SUCCESS": "Meeting request mail sent", "MEETING_EMAIL_FAILURE": "Failed to send meeting request mail", "NO_MEETING_TYPES": "At least one event type required to send a mail"}}, "SYNC": {"TITLE": "Data syncing", "DESCRIPTION": "This data is automatically synced with the", "ACCOUNT": "associated account"}, "OPPORTUNITY": {"TITLE": "Opportunities", "STATUS": {"WON": "Won", "LOST": "Lost"}, "ACTIONS": {"REOPEN": "Reopen", "CLOSE_WON": "Won", "CLOSE_LOST": "Lost"}, "ADD_OBJECT_BUTTON": "Create opportunity", "PROFILE_PAGE": {"TITLE": "Opportunity details", "EDIT": "Edit opportunity", "PREVIOUS_PAGE_TITLE": "Opportunities", "ACTIVITIES_TIMELINE_TAB": "Activity", "ABOUT_TAB": "About", "ASSOCIATIONS_TAB": "Associations", "CLOSE_LOST_MODAL": {"ACTION": "Close opportunity", "HINT": "This will close the opportunity with the current revenue and packages. The opportunity will no longer be editable.", "SELECT_PLACEHOLDER": "Select a reason", "NOTES_PLACEHOLDER": "Notes", "OPTIONS": {"PRICE": "Price", "NO_BUDGET": "No budget", "LOST_TO_COMPETITOR": "Lost to competitor", "NOT_READY": "Not ready", "OTHER": "Other"}}}, "EDIT_PAGE": {"TITLE": "Edit opportunity", "SAVE": {"SUCCESS": "Opportunity was updated successfully", "FAIL": "Failed to update opportunity"}}, "ASSOCIATION_PANEL": {"TITLE": "Opportunities", "ADD_ASSOCIATION": "Add opportunity", "CREATE_BUTTON": "Create and associate a new opportunity", "TITLE_ASSOCIATE": "Add opportunities to {{objectName}}", "VALUE": "Value", "ACTIVITY": "Last activity", "CLOSE_DATE": "Expected close date", "STAGE": "Stage", "STATUS": {"OPEN": "Open", "CLOSED_LOST": "Closed lost", "CLOSED_WON": "Closed won"}}, "ASSOCIATION_FORM": {"TITLE": "Opportunities", "PLACEHOLDER": "Associate opportunity..."}, "SELECT_MODAL": {"ONE_SELECTED": "Add opportunity", "MORE_SELECTED": "Add {{totalSelected}} opportunities", "NO_SELECTED": "Please select an opportunity", "CREATE_NEW": "Create new opportunity"}, "LIST_OBJECTS_TABLE": {"CUSTOM_COLUMNS": {"PRIMARY_ASSOCIATIONS": "Primary association"}, "EMPTY_TABLE": {"IMAGE_ALT": "Opportunities", "HEADER": "Create your first opportunity", "HINT": "Add an opportunity to track and manage your sales pipeline"}, "ACTIONS": {"EDIT": {"TITLE": "Edit opportunity"}, "DELETE": {"ACTION": "Delete opportunity", "TITLE": "Delete opportunity?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted opportunity", "ERROR": "Error deleting opportunity"}, "DELETE_SELECTED": {"ACTION": "Delete opportunities", "TITLE": "Delete opportunities?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted opportunities", "ERROR": "Error deleting opportunities"}, "CREATE": {"TITLE": "Create opportunity"}}, "PIPELINE": {"EMPTY_STATE": {"TITLE": "Set up your pipeline and create your first opportunity", "MESSAGE": "Start with a default pipeline, or create a custom pipeline that matches your sales process", "SETUP_PIPELINE": "Set up pipeline"}}}, "PIPELINE": {"SELECTOR": {"TITLE": "Select pipeline...", "NO_PIPELINES": "No pipelines", "EMPTY": "There are no pipelines yet. Please create a pipeline to continue."}, "LAST_ACTIVITY": "Last activity", "EXPECTED_CLOSE_DATE": "Expected close date", "ACTUAL_CLOSE_DATE": "Close date", "PERCENTAGE_OF": "% of"}, "STAGE": {"SELECTOR": {"LABEL": "Stage", "TITLE": "Select stage..."}}, "TABLE_FILTERS": {"HEADER": "Opportunities"}, "CREATE_PAGE": {"TITLE": "Create opportunity", "NAME_PLACEHOLDER": "Add opportunity name...", "SAVE": {"SUCCESS": "Opportunity creation was successful", "ERROR": "Something went wrong", "ASSOCIATIONS_REQUIRED": "Associate at least one company or contact"}}, "RECORD_CHANGE_ACTIVITY": {"CREATED": "Opportunity created", "CREATED_VIA": "Opportunity created via"}}, "PIPELINES": {"TITLE": "Pipelines", "SETTINGS": {"TITLE": "Manage pipelines", "CTA": {"CREATE_PIPELINE": "Create pipeline", "DELETE_PIPELINE": "Delete pipeline", "ADD_STAGE": "Add stage"}, "FORM": {"TITLES": {"PIPELINE_NAME": "Pipeline name", "PIPELINE_STAGES": "Pipeline stages", "STAGE_NAME": "Stage name", "CLOSE_PROBABILITY": "Close probability"}, "SUBTITLES": {"PIPELINE_STAGES": "Opportunities may shift within the pipeline if you delete a stage or change its probability."}, "PLACEHOLDER": {"PIPELINE_NAME": "My new pipeline"}}, "MODALS": {"DELETE_TITLE": "Delete pipeline?", "DELETE_MESSAGE": "Deleting a pipeline with opportunities in it will cause them to no longer show on the pipeline board. Ensure that you have removed all opportunities from this pipeline before deleting it.", "UNSAVED_CHANGES_TITLE": "Unsaved changes", "UNSAVED_CHANGES_MESSAGE": "Are you sure you want to leave? You will lose any unsaved changes.", "UNSAVED_CHANGES_CONFIRM": "Discard changes"}, "SNACKBARS": {"CREATE_FAILED": "Failed to create a new pipeline, please try again later.", "CREATE_SUCCESS": "Successfully created a new pipeline", "DELETE_FAILED": "Failed to delete pipeline, please try again later.", "DELETE_SUCCESS": "Successfully deleted pipeline", "SAVE_SUCCESS": "Successfully updated pipeline", "SAVE_FAILED": "Failed to update pipeline, please try again later.", "FORM_INVALID": "Failed to update pipeline, please make sure all fields are correct."}, "DEFAULTS": {"NAME": "My new pipeline", "STAGE_ONE": "Demo scheduled", "STAGE_TWO": "Proposal requested", "STAGE_THREE": "Negotiation initiated", "STAGE_FOUR": "Contract sent", "CLOSED_WON": "Closed won", "CLOSED_LOST": "Closed lost"}, "EMPTY_STATE": {"TITLE": "Create your first pipeline", "MESSAGE": "Track and manage your sales opportunities from start to close"}}}, "LEADERBOARD": {"TITLE": "Leaderboard", "COLUMNS": {"RANK": "Rank", "NAME": "Name", "INBOUND_CALLS": "Inbound calls", "OUTBOUND_CALLS": "Outbound calls", "INBOUND_EMAILS": "Inbound emails", "OUTBOUND_EMAILS": "Outbound emails", "MEETINGS": "Meetings"}, "AGGREGATIONS": {"TOTAL": "Total", "AVERAGE": "Average"}, "TIME_RANGE": "Time range", "DATE_RANGES": {"THIS_MONTH": "This month", "LAST_MONTH": "Last month", "THIS_YEAR": "This year", "LAST_YEAR": "Last year", "CUSTOM": "Custom"}, "CUSTOM_RANGE": {"START": "Start date", "END": "End date"}, "DELETED_USER": "Deleted User"}, "CUSTOM_OBJECT": {"ASSOCIATION_FORM": {"TITLE": "Custom objects", "PLACEHOLDER": "Associate custom object..."}}, "OBJECT": {"TITLE": "{{ pluralObjectName }}", "TITLE_SINGLE": "{{ singularObjectName }}", "ADD_OBJECT_BUTTON": "Create {{ singularObjectName }}", "CREATE_PAGE": {"TITLE": "Create {{ singularObjectName }}", "SAVE": {"SUCCESS": "{{ singularObjectName }} creation was successful", "ERROR": "Something went wrong"}, "LOAD": {"ERROR": "Something went wrong"}}, "EDIT_PAGE": {"TITLE": "Edit {{ singularObjectName }}", "SAVE": {"SUCCESS": "{{ singularObjectName }} was updated successfully"}}, "PROFILE_PAGE": {"TITLE": "{{ singularObjectName }} profile", "EDIT": "Edit {{ singularObjectName }}", "PREVIOUS_PAGE_TITLE": "{{ pluralObjectName }}", "ABOUT_TAB": "About", "ACTIVITIES_TIMELINE_TAB": "Activity", "ASSOCIATIONS_TAB": "Associations"}, "LIST_OBJECTS_TABLE": {"EMPTY_TABLE": {"IMAGE_ALT": "Graphic showing a {{singularObjectName}} to be filled in", "HEADER": "Create your first {{singularObjectName}}", "HINT": "Add a single {{singularObjectName}}"}, "SELECT_ALL": {"SELECT_ALL_OBJECTS": "select all {{ totalObjects }} {{ pluralObjectName }}", "CLEAR_SELECTION": "clear selection", "TOTAL_SELECTED": "{{ totalSelected }} selected"}, "ACTIONS": {"EDIT": {"TITLE": "Edit {{ singularObjectName }}"}, "DELETE": {"ACTION": "Delete {{ singularObjectName }}", "TITLE": "Delete {{ singularObjectName }}?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted {{ singularObjectName }}", "ERROR": "Error deleting {{ singularObjectName }}"}, "DELETE_SELECTED": {"ACTION": "Delete {{ pluralObjectName }}", "TITLE": "Delete {{ pluralObjectName }}?", "CONFIRMATION_MESSAGE": "This cannot be undone.", "CONFIRM": "Delete", "CANCEL": "Cancel", "SUCCESS": "Deleted {{ pluralObjectName }}", "ERROR": "Error deleting {{ pluralObjectName }}"}}}, "TABLE_FILTERS": {"HEADER": "{{ pluralObjectName }}"}, "RECORD_CHANGE_ACTIVITY": {"CREATED": "{{ singularObjectName }} created", "CREATED_VIA": "{{ singularObjectName }} created via", "DETAILS_UPDATED": "{{ singularObjectName }} details updated", "IMPORTANT_FIELD_UPDATED": "{{<PERSON><PERSON><PERSON>}} updated by", "IMPORTANT_FIELD_UPDATED_SALESPERSON": "Assigned to", "IMPORTANT_FIELD_UPDATED_SALESPERSON_NOT_ASSIGNED": "Unassigned", "IMPORTANT_FIELD_UPDATED_SALESPERSON_UNKNOWN": "deleted salesperson"}, "ASSOCIATION_PANEL": {"TITLE": "{{ pluralObjectName }}", "ADD_ASSOCIATION": "Add {{ singularObjectName }}", "TITLE_ASSOCIATE": "Add {{ pluralObjectName }} to {{objectName}}", "CREATE_BUTTON": "Create and associate a new {{ singularObjectName }}", "CONFIRM_EXISTING_PRIMARY_MODAL": {"TITLE": "Are you sure you want to proceed?", "CONFIRMATION_MESSAGE": "The following {{ pluralObjectName }} already have primary associations and won't be created as primary:"}, "EDIT_OBJECT": "Edit {{singularObjectName}}"}, "SELECT_MODAL": {"ONE_SELECTED": "Add {{singularObjectName}}", "MORE_SELECTED": "Add {{totalSelected}} {{pluralObjectName}}", "NO_SELECTED": "Please select a {{singularObjectName}}", "CREATE_NEW": "Create new {{singularObjectName}}"}}}