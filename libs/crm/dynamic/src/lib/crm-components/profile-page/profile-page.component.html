<div class="page-wrapper">
  <crm-task-queue></crm-task-queue>
  <glxy-page [pagePadding]="false" *ngIf="objectType() as objectType">
    <glxy-page-toolbar>
      <glxy-page-title>
        @let profileTitle = profileTitle$() | async;
        @if (profileTitle && profileTitle.length > 0 && profileDataInjector()) {
          @for (title of profileTitle; track $index) {
            <ng-container *ngComponentOutlet="title.component; injector: profileDataInjector()"></ng-container>
          }
        } @else {
          {{ 'PROFILE_PAGE.TITLE' | translateForCrmObject: objectType | async }}
        }
      </glxy-page-title>
      @if (isMultiLocation()) {
        <glxy-badge color="grey">
          {{ locationName() }}
        </glxy-badge>
      }
      <glxy-page-actions class="action-buttons">
        @if (profileDataInjector(); as data) {
          <ng-container *ngFor="let button of actionButtons$() | async">
            <ng-container *ngComponentOutlet="button.component; injector: data"></ng-container>
          </ng-container>
        }
        @if (enableAiSummary() && hasTimelineActivitiesFeatureFlag()) {
          <crm-ai-summary-button
            [crmObjectId]="(crmObjectId$ | async) ?? ''"
            [crmObjectType]="objectType"
            [crmObjectName]="(headerName$ | async) ?? ''"
            [loadingCrmObjectName]="loadingHeaderStatus()"
          />
        }
      </glxy-page-actions>
      <glxy-page-nav>
        <glxy-page-nav-button
          *ngIf="previousPageRoute$ | async as previousPageRoute"
          [previousPageTitle]="('PROFILE_PAGE.PREVIOUS_PAGE_TITLE' | translateForCrmObject: objectType | async) ?? ''"
          [previousPageUrl]="previousPageRoute"
          [useHistory]="true"
          [historyBackButtonTitle]="
            ('PROFILE_PAGE.PREVIOUS_PAGE_TITLE' | translateForCrmObject: objectType | async) ?? ''
          "
        ></glxy-page-nav-button>
      </glxy-page-nav>
    </glxy-page-toolbar>
    <crm-three-panel-page
      class="crm-three-panel-page"
      [leftPanelTitle]="('PROFILE_PAGE.ABOUT_TAB' | translateForCrmObject: objectType | async) ?? ''"
      [centerPanelTitle]="('PROFILE_PAGE.ACTIVITIES_TIMELINE_TAB' | translateForCrmObject: objectType | async) ?? ''"
      [rightPanelTitle]="('PROFILE_PAGE.ASSOCIATIONS_TAB' | translateForCrmObject: objectType | async) ?? ''"
      [showRightPanel]="showRightPanel$() | async"
    >
      <crm-three-panel-page-content-header>
        <div class="header-container">
          <crm-profile-header
            [objectType]="objectType"
            [(loadingStatus)]="loadingHeaderStatus"
            [name]="(headerName$ | async) || ''"
            [email]="(email$ | async) || ''"
            [phone]="(phone$ | async) || ''"
            [showMeetingIcon]="(showMeetingIcon$ | async) || false"
            [contactId]="(contactId$ | async) || ''"
            [subtitle]="subtitle$ | async"
            [onClickOpenConversation]="(showMessageButton$ | async) ? onClickOpenConversation.bind(this) : undefined"
            [onClickEdit]="hideHeaderEditAction ? undefined : onClickEdit.bind(this)"
            [crmObject]="crmObject$ | async"
            data-action="clicked-crm-profile-page-edit-from-header"
          ></crm-profile-header>
        </div>
      </crm-three-panel-page-content-header>
      <crm-three-panel-page-left-panel>
        <ng-container *ngIf="(isMobile$ | async) === false">
          <div class="header-container">
            <crm-profile-header
              [objectType]="objectType"
              [(loadingStatus)]="loadingHeaderStatus"
              [name]="(headerName$ | async) || ''"
              [email]="(email$ | async) || ''"
              [phone]="(phone$ | async) || ''"
              [showMeetingIcon]="showMeetingIcon$ | async"
              [contactId]="(contactId$ | async) || ''"
              [website]="(website$ | async) || ''"
              [subtitle]="subtitle$ | async"
              [onClickOpenConversation]="(showMessageButton$ | async) ? onClickOpenConversation.bind(this) : undefined"
              [onClickEdit]="hideHeaderEditAction ? undefined : onClickEdit.bind(this)"
              [crmObject]="crmObject$ | async"
              data-action="clicked-crm-profile-page-edit-from-header"
            ></crm-profile-header>
          </div>
        </ng-container>
        <crm-contact-profile-content
          class="profile-content"
          [objectType]="objectType"
          [crmObject]="(crmObject$ | async) ?? {}"
          [fieldIdsToHide]="hideFieldIds()"
          [(loadingStatus)]="loadingContentStatus"
          (objectChanged)="refreshObject()"
        ></crm-contact-profile-content>
        <div class="footer-container">
          <crm-profile-footer
            [objectType]="objectType"
            [onClickEdit]="onClickEdit.bind(this)"
            data-action="clicked-crm-profile-page-edit-from-footer"
          ></crm-profile-footer>
        </div>
      </crm-three-panel-page-left-panel>
      <crm-three-panel-page-center-panel>
        <crm-activities-timeline
          [crmObjectId]="(crmObjectId$ | async) ?? ''"
          [activityObjectType]="objectType"
          [enableAISummary]="enableAiSummary()"
          [objectSubtype]="objectSubtype$ | async"
        ></crm-activities-timeline>
      </crm-three-panel-page-center-panel>
      <crm-three-panel-page-right-panel>
        @if (objectType === 'Company') {
          <crm-company-profile-cards-panel [company]="companyProfileData$ | async"></crm-company-profile-cards-panel>
        } @else {
          <crm-profile-cards-panel
            [cardData]="profileData$ | async"
            [cards]="profileCards$() | async"
          ></crm-profile-cards-panel>
        }
      </crm-three-panel-page-right-panel>
    </crm-three-panel-page>
  </glxy-page>
</div>
