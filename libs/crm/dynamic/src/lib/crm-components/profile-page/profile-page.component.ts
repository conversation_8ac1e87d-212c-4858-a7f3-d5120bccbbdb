import { catchError, filter, map, shareReplay, switchMap, tap } from 'rxjs/operators';
import { BehaviorSubject, combineLatest, firstValueFrom, Observable, of, take } from 'rxjs';
import {
  booleanAttribute,
  Component,
  computed,
  DestroyRef,
  Inject,
  inject,
  Injector,
  input,
  Input,
  model,
  OnInit,
  Optional,
  signal,
  WritableSignal,
} from '@angular/core';
import { RxState } from '@rx-angular/state';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  ActionButton,
  BREAKPOINT_WIDTH,
  CompanyProfileCardData,
  CompanyTableCustomizationService,
  CrmActivitiesTimelineComponent,
  CrmAISummaryButtonComponent,
  CrmAssociationService,
  CrmDependencies,
  CrmFieldService,
  CrmInjectionToken,
  CrmMultiLocationDependencies,
  CrmMultilocationInjectionToken,
  CrmObjectDependencies,
  CrmObjectDisplayService,
  CrmObjectInjectionToken,
  CrmObjectService,
  CRMTrackingService,
  formatAsReadonlyPhone,
  LoadingStatus,
  ObjectType,
  PAGE_ROUTES,
  parsePhoneNumber,
  PlatformExtensionFieldIds,
  PROFILE_PAGE_DATA_TOKEN,
  ProfileCard,
  ProfileCardData,
  ProfilePageData,
  ProfileTitle,
  SNACKBAR_DURATION,
  StandardExternalIds,
  StandardIds,
  TranslateForCrmObjectPipe,
  TranslationModule,
} from '@galaxy/crm/static';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { ActivatedRoute, Params, Router, RouterModule } from '@angular/router';
import { CrmContactProfileContentComponent } from '../profile/profile-content.component';
import {
  CrmObjectInterface,
  FieldType,
  GetMultiCrmObjectResponseInterface,
  ObjectProjectionFilterInterface,
} from '@vendasta/crm';
import { BreakpointObserver } from '@angular/cdk/layout';
import { CrmProfileFooterComponent } from '../profile/profile-footer.component';
import { CrmProfileHeaderComponent } from '../profile/profile-header.component';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatIconModule } from '@angular/material/icon';
import { CompanyProfileCardsPanelComponent } from '../profile/profile-cards-panel/company-to-contact-associations/company-profile-cards-panel.component';

import { CrmTaskQueueComponent } from '../task-queue';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ProfileCardsPanelComponent } from '../profile/profile-cards-panel/profile-cards-panel.component';
import { CrmThreePanelPageModule } from '@galaxy/crm/components/three-panel-page';
import { ConversationChannel } from '@vendasta/conversation';
import { switchWhenFalsy } from '@vendasta/rx-utils';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

@Component({
  selector: 'crm-profile-page',
  templateUrl: './profile-page.component.html',
  styleUrls: ['./profile-page.component.scss'],
  providers: [RxState],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    GalaxyPageModule,
    MatButtonModule,
    MatTabsModule,
    MatCardModule,
    RouterModule,
    CrmActivitiesTimelineComponent,
    CrmContactProfileContentComponent,
    CrmProfileFooterComponent,
    CrmProfileHeaderComponent,
    CrmThreePanelPageModule,
    GalaxyEmptyStateModule,
    MatIconModule,
    CompanyProfileCardsPanelComponent,
    CrmTaskQueueComponent,
    ProfileCardsPanelComponent,
    TranslateForCrmObjectPipe,
    GalaxyBadgeModule,
    CrmAISummaryButtonComponent,
  ],
})
export class CrmProfilePageComponent implements OnInit {
  objectType = input.required<ObjectType>();
  @Input() previousPageUrl?: string;
  @Input({ transform: booleanAttribute }) hideHeaderEditAction = false;

  crmObjectId$ = this.state.select('crmObjectId');
  crmObject$ = this.state.select('crmObject');
  crmObjectCache$$ = new BehaviorSubject<CrmObjectInterface | undefined>(undefined);
  defaultPageUrl$ = this.state.select('defaultPageUrl');
  readonly objectType$ = toObservable(this.objectType);
  enableAiSummary = computed(() => {
    const objectType = this.objectType();
    return objectType === 'Contact' || objectType === 'Company';
  });

  headerName$ = combineLatest([this.crmObject$, this.objectType$]).pipe(
    map(([crmObject, objectType]) => {
      let headerName = '';
      if (this.crmObjectDependencies?.getDisplayName) {
        headerName = this.crmObjectDependencies.getDisplayName(crmObject) ?? '';
      } else {
        headerName = this.objectDisplayService.getObjectDisplayName(objectType, crmObject);
      }
      if (crmObject && Object.keys(crmObject).length > 0) {
        // header loading status is based off header name
        this.loadingHeaderStatus.set(LoadingStatus.OK);
      }
      return headerName;
    }),
  );
  email$ = this.crmObject$.pipe(
    map((crmObject) => {
      const emailFieldId = this.crmFieldService.getFieldId(StandardExternalIds.Email);
      const emailField = crmObject.fields?.find((f) => f.fieldId === emailFieldId);
      return emailField?.stringValue || '';
    }),
  );
  showMeetingIcon$: Observable<boolean> = combineLatest([this.email$, this.isMeetingIconNeeded()]).pipe(
    map(([email, isMeetingIconNeeded]) => {
      return isMeetingIconNeeded && !!email;
    }),
  );
  contactId$ = this.crmObject$.pipe(
    map((crmObject) => {
      const contact = crmObject.crmObjectId;
      return contact || '';
    }),
  );
  phone$ = combineLatest([this.crmObject$, this.objectType$]).pipe(
    map(([crmObject, objectType]) => {
      const phoneFieldId = this.getPhoneFieldId(objectType);
      const phoneField = crmObject.fields?.find((f) => f.fieldId === phoneFieldId);
      if (phoneField?.phoneFieldValues != null) {
        return formatAsReadonlyPhone(phoneField?.phoneFieldValues);
      }
      const parsedPhoneNumber = parsePhoneNumber(phoneField?.stringValue || '');
      return formatAsReadonlyPhone(parsedPhoneNumber);
    }),
  );
  website$ = this.crmObject$.pipe(
    map((crmObject) => {
      const websiteFieldId = this.crmFieldService.getFieldId(StandardIds.CompanyWebsite);
      const websiteField = crmObject.fields?.find((f) => f.fieldId === websiteFieldId);
      if (!websiteField?.stringValue?.startsWith('http') && websiteField?.stringValue) {
        return 'https://' + websiteField?.stringValue || '';
      }
      return websiteField?.stringValue || '';
    }),
  );
  address$ = this.crmObject$.pipe(
    map((crmObject) => {
      const address = this.companyTableCustomizationService.getRowFullAddress(crmObject);
      return address || '';
    }),
  );

  // if the appropriate method is provided in the config and has access then show the message button
  showMessageButton$ = combineLatest([
    this.objectType$,
    this.config.canShowInboxForContact$,
    this.config.hasInboxAccess$,
  ]).pipe(
    map(([objectType, showInbox, canAccessInboxFeature]) => {
      if (!canAccessInboxFeature) {
        return false;
      }

      switch (objectType) {
        case 'Contact':
          return !!this.config.openContactConversation && showInbox;
        case 'Company':
          return !!this.config.openAccountGroupConversation;
        case 'Opportunity':
          return false;
        default:
          return false;
      }
    }),
  );

  profileData$: Observable<ProfileCardData> = combineLatest([this.crmObject$, this.headerName$, this.objectType$]).pipe(
    map(([crmObject, headerName, objectType]) => {
      return { id: crmObject?.crmObjectId || '', name: headerName, objectType: objectType };
    }),
  );

  companyProfileData$: Observable<CompanyProfileCardData> = this.crmObject$.pipe(
    map((crmObject) => {
      const fieldValue = crmObject.fields?.find((field) => field.fieldId === PlatformExtensionFieldIds.AccountGroupID);
      return {
        company_id: crmObject.crmObjectId || '',
        account_group_id: fieldValue?.stringValue,
        name: crmObject.fields?.find((field) => field.fieldId === StandardIds.CompanyName)?.stringValue || '',
        group_id: crmObject.groupId || '',
        id: crmObject.crmObjectId || '',
        objectType: 'Company',
      };
    }),
  );

  accountGroupId$ = this.crmObject$.pipe(
    map((crmObject) => {
      const fieldValue = this.crmFieldService.getFieldValueFromCrmObject(
        crmObject,
        PlatformExtensionFieldIds.AccountGroupID,
      );
      return fieldValue?.stringValue;
    }),
  );

  readonly actionButtons$ = computed(() => this.getActionButtons(this.objectType()));
  readonly profileTitle$ = computed(() => this.getProfileTitleComponents(this.objectType()));
  readonly profileCards$ = computed(() => this.getProfileCards(this.objectType()));
  private readonly injector = inject(Injector);
  private readonly destroyRef = inject(DestroyRef);
  readonly profileDataInjector = signal<Injector | undefined>(undefined);
  private readonly crmAssociationService = inject(CrmAssociationService);

  readonly hideFieldIds = computed(() => this.getFieldsToHide(this.objectType()));

  readonly loadingHeaderStatus: WritableSignal<LoadingStatus> = model(LoadingStatus.LOADING);
  readonly loadingContentStatus: WritableSignal<LoadingStatus> = model(LoadingStatus.LOADING);

  MOBILE_BREAKPOINT = `(max-width: ${BREAKPOINT_WIDTH.MOBILE})`;
  isMobile$: Observable<boolean> = this.breakpointObserver
    .observe([this.MOBILE_BREAKPOINT])
    .pipe(map((result) => result.matches));

  private readonly returnUrl$ = this.route.queryParams.pipe(
    map((params: Params) => params['returnUrl'] ?? ''),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );
  previousPageRoute$ = this.returnUrl$.pipe(map((returnUrl) => returnUrl || this.previousPageUrl));

  readonly showRightPanel$ = computed(() => this.getShowRightPanel(this.objectType()));

  readonly subtitle$ = combineLatest([this.objectType$, this.crmObject$, this.isMobile$]).pipe(
    switchMap(([objectType, crmObject, isMobile]) => {
      switch (objectType) {
        case 'Opportunity':
          return this.getPrimaryAssociationName(objectType, crmObject);
        default:
          return isMobile ? of('') : this.address$;
      }
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  isMultiLocation = toSignal(this.multiLocationConfig.isMultiLocation$ ?? of(false));
  locationName = toSignal(this.multiLocationConfig.currentLocationName$ ?? of(''));
  multiLocationNamespace = toSignal(this.config.namespace$ ?? of(''));

  protected readonly hasTimelineActivitiesFeatureFlag = toSignal(this.config.hasTimelineActivitiesFeatureFlag$);
  protected readonly objectSubtype$ = this.crmObjectDependencies?.objectSubtype$
    ? this.crmObjectDependencies.objectSubtype$
    : of('');

  constructor(
    private router: Router,
    private readonly state: RxState<ComponentState>,
    private readonly route: ActivatedRoute,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly crmObjectService: CrmObjectService,
    private readonly translationService: TranslateService,
    private readonly objectDisplayService: CrmObjectDisplayService,
    private readonly companyTableCustomizationService: CompanyTableCustomizationService,
    private readonly crmFieldService: CrmFieldService,
    private readonly snackService: SnackbarService,
    private readonly trackingService: CRMTrackingService,
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    @Inject(CrmMultilocationInjectionToken) private readonly multiLocationConfig: CrmMultiLocationDependencies,
    @Optional()
    @Inject(CrmObjectInjectionToken)
    private readonly crmObjectDependencies: CrmObjectDependencies,
  ) {
    state.set({
      refreshObject: true,
      crmObjectId: '',
      crmObject: {},
      defaultPageUrl: '',
    });
  }

  ngOnInit(): void {
    this.state.connect(
      'crmObjectId',
      this.route.params.pipe(
        tap(() => {
          this.loadingHeaderStatus.set(LoadingStatus.LOADING);
          this.loadingContentStatus.set(LoadingStatus.LOADING);
        }),
        map((params: Params) => params['crmObjectId']),
        shareReplay({ bufferSize: 1, refCount: true }),
      ),
    );
    if (this.crmObjectDependencies) {
      this.state.connect('defaultPageUrl', this.crmObjectDependencies.defaultPageUrl$ || of(''));
    }

    const refreshObject$ = this.state.select('refreshObject').pipe(filter((refreshObject) => refreshObject));

    this.state.connect(
      'crmObject',
      combineLatest([this.crmObjectId$, this.objectType$, refreshObject$]).pipe(
        switchMap(([crmObjectId, objectType]) =>
          this.crmObjectService.getMultiObject(objectType, [crmObjectId], [
            { filterType: 1, fromFieldType: FieldType.FIELD_TYPE_PHONE, toFieldType: FieldType.FIELD_TYPE_PHONE },
          ] as ObjectProjectionFilterInterface[]),
        ),
        map((resp: GetMultiCrmObjectResponseInterface) => {
          if (resp?.crmObjects?.length === 0 || !resp?.crmObjects?.[0].crmObjectId) {
            throw new Error(this.translationService.instant('ERRORS.RESOURCE_NOT_FOUND_ERROR'));
          }
          return resp.crmObjects[0];
        }),
        catchError((error) => {
          this.errorSnackbar(error, 'ERRORS.GENERIC_MESSAGE');
          return of({});
        }),
        tap(() => this.state.set({ refreshObject: false })),
        tap(() => this.crmObjectCache$$.next(undefined)),
        shareReplay({ bufferSize: 1, refCount: true }),
      ),
    );

    combineLatest([this.crmObject$, this.crmObjectCache$$])
      .pipe(
        filter(([crmObject]) => crmObject.crmObjectId !== undefined),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([crmObject, crmObjectCache]) => {
        const accountGroupField = this.crmFieldService.getFieldValueFromCrmObject(
          crmObjectCache ?? crmObject,
          PlatformExtensionFieldIds.AccountGroupID,
        );
        const profilePageInjector = Injector.create({
          providers: [
            {
              provide: PROFILE_PAGE_DATA_TOKEN,
              useValue: {
                accountGroupId: accountGroupField?.stringValue,
                crmObject: crmObjectCache ?? crmObject,
                onChangeCallback: (value: CrmObjectInterface) => this.crmObjectCache$$.next(value),
              } as ProfilePageData,
            },
          ],
          parent: this.injector,
        });
        this.profileDataInjector.set(profilePageInjector);
      });

    this.crmObject$
      .pipe(
        filter((crmObject) => crmObject.crmObjectId !== undefined),
        tap((crmObject) => {
          if (this.objectType() === 'Company' && this.config.company?.onProfilePageInit) {
            this.config.company.onProfilePageInit(crmObject);
          }
          if (this.objectType() === 'Contact' && this.config.contact?.onProfilePageInit) {
            this.config.contact.onProfilePageInit(crmObject);
          }
          if (this.objectType() === 'Opportunity' && this.config?.opportunity?.onProfilePageInit) {
            this.config.opportunity.onProfilePageInit(crmObject);
          }
        }),
        take(1),
      )
      .subscribe();
  }

  refreshObject(): void {
    this.state.set({ refreshObject: true });
  }

  private getActionButtons(objectType: ObjectType): Observable<ActionButton[]> {
    if (this.crmObjectDependencies) {
      return this.crmObjectDependencies.actionButtons$ || of([]);
    }
    switch (objectType) {
      case 'Contact':
        return this.config.contact?.actionButtons$ || of([]);
      case 'Company':
        return this.config.company?.actionButtons$ || of([]);
      case 'Opportunity':
        return this.config?.opportunity?.actionButtons$ || of([]);
      default:
        return of([]);
    }
  }

  private getProfileTitleComponents(objectType: ObjectType): Observable<ProfileTitle[]> {
    if (this.crmObjectDependencies) {
      return this.crmObjectDependencies.profileTitle$ || of([]);
    }
    switch (objectType) {
      case 'Contact':
        return this.config.contact?.profileTitle$ || of([]);
      case 'Company':
        return this.config.company?.profileTitle$ || of([]);
      case 'Opportunity':
        return this.config?.opportunity?.profileTitle$ || of([]);
      default:
        return of([]);
    }
  }

  private getProfileCards(objectType: ObjectType): Observable<ProfileCard[]> {
    if (this.crmObjectDependencies) {
      return this.crmObjectDependencies.profileCards$ || of([]);
    }
    switch (objectType) {
      case 'Contact':
        return this.config?.contact?.profileCards$ || of([]);
      case 'Opportunity':
        return this.config?.opportunity?.profileCards$ || of([]);
      default:
        return of([]);
    }
  }

  private getPhoneFieldId(objectType: ObjectType): string {
    if (this.crmObjectDependencies) {
      return '';
    }
    switch (objectType) {
      case 'Contact':
        return this.crmFieldService.getFieldId(StandardExternalIds.PhoneNumber);
      case 'Company':
        return this.crmFieldService.getFieldId(StandardIds.CompanyPhoneNumber);
      case 'Opportunity':
        return '';
      default:
        console.error(`Unsupported object type: ${this.objectType}`);
        return '';
    }
  }

  private getFieldsToHide(objectType: ObjectType): string[] {
    if (this.crmObjectDependencies) {
      return [];
    }
    switch (objectType) {
      case 'Contact':
        return [
          this.crmFieldService.getFieldId(StandardExternalIds.FirstName),
          this.crmFieldService.getFieldId(StandardExternalIds.LastName),
        ];
      case 'Company':
        return [this.crmFieldService.getFieldId(StandardIds.CompanyName)];
      case 'Opportunity':
        return [
          this.crmFieldService.getFieldId(StandardIds.OpportunityName),
          this.crmFieldService.getFieldId(StandardIds.OpportunityPipelineID),
          this.crmFieldService.getFieldId(StandardIds.OpportunityProbability),
        ];
      default:
        return [];
    }
  }

  private getShowRightPanel(objectType: ObjectType): Observable<boolean> {
    if (this.crmObjectDependencies) {
      return this.crmObjectDependencies.showRightHandProfilePanel$ || of(true);
    }
    let showRightPanel$: Observable<boolean>;
    switch (objectType) {
      case 'Contact':
        showRightPanel$ = this.config.contact?.showRightHandProfilePanel$ || of(true);
        break;
      case 'Company':
        showRightPanel$ = this.config.company?.showRightHandProfilePanel$ || of(true);
        break;
      case 'Opportunity':
        showRightPanel$ = this.config?.opportunity?.showRightHandProfilePanel$ || of(true);
        break;
      default:
        console.info(`Unsupported object type: ${this.objectType}`);
        showRightPanel$ = of(false);
    }
    return showRightPanel$;
  }

  private getPrimaryAssociationName(objectType: ObjectType, object: CrmObjectInterface): Observable<string> {
    if (!object.crmObjectId) {
      return of('');
    }
    const contact$ = this.crmAssociationService
      .getAssociationsWithAssociatedObjectForObject$(object.crmObjectId, objectType, 'Contact')
      .pipe(
        map((result) => result?.find((r) => Boolean(r.associatedObject))),
        map((result) =>
          result ? this.objectDisplayService.getObjectDisplayName('Contact', result.associatedObject) : '',
        ),
      );
    const company$ = this.crmAssociationService
      .getAssociationsWithAssociatedObjectForObject$(object.crmObjectId, objectType, 'Company')
      .pipe(
        map((result) => result?.find((r) => Boolean(r.associatedObject))),
        map((result) =>
          result ? this.objectDisplayService.getObjectDisplayName('Company', result.associatedObject) : '',
        ),
      );
    return contact$.pipe(switchWhenFalsy(company$));
  }

  private errorSnackbar(error: Error, defaultMessage: string) {
    this.snackService.openErrorSnack(error?.message ?? defaultMessage, {
      duration: SNACKBAR_DURATION,
    });
  }

  async onClickOpenConversation() {
    switch (this.objectType()) {
      case 'Contact': {
        const namespace = await firstValueFrom(this.config.namespace$);
        const parentNamespace = await firstValueFrom(this.config.parentNamespace$);
        const contactId = await firstValueFrom(this.crmObjectId$);
        const phone = await firstValueFrom(this.phone$);
        const channel = phone
          ? ConversationChannel.CONVERSATION_CHANNEL_SMS
          : ConversationChannel.CONVERSATION_CHANNEL_EMAIL;
        if (namespace && contactId)
          this.config.openContactConversation?.(namespace, parentNamespace, contactId, channel);
        break;
      }
      case 'Company': {
        const accountGroupId = await firstValueFrom(this.accountGroupId$);
        if (accountGroupId) this.config.openAccountGroupConversation?.(accountGroupId);
        break;
      }
      case 'Opportunity': {
        break;
      }
      default:
        break;
    }
  }

  async onClickEdit() {
    this.trackingService.trackEvent(this.objectType(), 'edit-crm-object-from-profile');
    const crmObjectId = await firstValueFrom(this.crmObjectId$);
    if (!crmObjectId) {
      return;
    }
    const defaultPageUrl = await firstValueFrom(this.defaultPageUrl$);

    let routePrefix = '';
    if (this.isMultiLocation()) {
      routePrefix = await firstValueFrom(this.multiLocationConfig.routePrefix$);
    } else {
      routePrefix = await firstValueFrom(this.config.routePrefix$);
    }
    const route = this.buildEditRoute(routePrefix, crmObjectId, defaultPageUrl);
    const queryParams = this.buildQueryParams(routePrefix, crmObjectId, defaultPageUrl);
    this.router.navigate([route], { queryParams });
  }

  buildEditRoute(routePrefix: string, crmObjectId: string, defaultPageUrl: string): string {
    let routeTemplate = '';
    if (this.crmObjectDependencies) {
      routeTemplate = `${defaultPageUrl}/${PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.EDIT}`;
    }
    switch (this.objectType()) {
      case 'Contact':
        if (this.isMultiLocation()) {
          routeTemplate = `${routePrefix}/${PAGE_ROUTES.MULTI_LOCATION.CONTACTS.ROOT}/${PAGE_ROUTES.MULTI_LOCATION.CONTACTS.SUBROUTES.EDIT}`;
        } else {
          routeTemplate = `${routePrefix}/${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.EDIT}`;
        }
        break;
      case 'Company':
        routeTemplate = `${routePrefix}/${PAGE_ROUTES.COMPANY.ROOT}/${PAGE_ROUTES.COMPANY.SUBROUTES.EDIT}`;
        break;
      case 'Opportunity':
        routeTemplate = `${routePrefix}/${PAGE_ROUTES.OPPORTUNITY.ROOT}/${PAGE_ROUTES.OPPORTUNITY.SUBROUTES.EDIT}`;
        break;
    }
    return routeTemplate
      .replace(':crmObjectId', crmObjectId)
      .replace(':namespace', this.multiLocationNamespace() ?? '');
  }

  buildQueryParams(routePrefix: string, crmObjectId: string, defaultPageUrl: string): { [key: string]: string } {
    const queryParams: { [key: string]: string } = {};
    let returnUrlTemplate = '';
    if (this.crmObjectDependencies) {
      returnUrlTemplate = `${defaultPageUrl}/${PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.PROFILE}`;
    }
    switch (this.objectType()) {
      case 'Contact':
        if (this.isMultiLocation()) {
          returnUrlTemplate = `${routePrefix}/${PAGE_ROUTES.MULTI_LOCATION.CONTACTS.ROOT}/${PAGE_ROUTES.MULTI_LOCATION.CONTACTS.SUBROUTES.PROFILE}`;
        } else {
          returnUrlTemplate = `${routePrefix}/${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.PROFILE}`;
        }
        break;
      case 'Company':
        returnUrlTemplate = `${routePrefix}/${PAGE_ROUTES.COMPANY.ROOT}/${PAGE_ROUTES.COMPANY.SUBROUTES.PROFILE}`;
        break;
      case 'Opportunity':
        returnUrlTemplate = `${routePrefix}/${PAGE_ROUTES.OPPORTUNITY.ROOT}/${PAGE_ROUTES.OPPORTUNITY.SUBROUTES.PROFILE}`;
        break;
      case undefined:
        console.warn('Object type undefined while building return route');
    }
    if (returnUrlTemplate) {
      const returnUrl = returnUrlTemplate
        .replace(':crmObjectId', crmObjectId)
        .replace(':namespace', this.multiLocationNamespace() ?? '');
      queryParams['returnUrl'] = returnUrl;
    }
    return queryParams;
  }

  isMeetingIconNeeded(): Observable<boolean> {
    return this.config.isMeetingIconNeeded$ || of(false);
  }
}

export interface ComponentState {
  refreshObject: boolean;
  crmObjectId: string;
  crmObject: CrmObjectInterface;
  defaultPageUrl: string;
}
