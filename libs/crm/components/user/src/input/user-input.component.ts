import { Component, ElementRef, ViewChild, ViewEncapsulation, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormControl, ReactiveFormsModule, UntypedFormGroup } from '@angular/forms';
import {
  CrmInjectionToken,
  FieldValueInterface,
  FormInputComponent,
  InlineEditService,
  SimplifiedUser,
} from '@galaxy/crm/static';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, debounceTime, distinctUntilChanged, firstValueFrom, map, Observable, switchMap } from 'rxjs';

interface UserOption {
  id: string;
  displayName: string;
}

@Component({
  templateUrl: './user-input.component.html',
  providers: [],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    GalaxyLoadingSpinnerModule,
    MatIcon,
    MatIconButton,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class CrmUserInputComponent extends FormInputComponent {
  @ViewChild('searchInput') searchInput?: ElementRef<HTMLInputElement>;

  public readOnly = false;

  protected loading = signal(false);
  private readonly displayName$$ = new BehaviorSubject<string>('');
  protected readonly displayName$ = this.displayName$$.asObservable();
  readonly options$: Observable<UserOption[]> = new Observable<UserOption[]>();

  private readonly userService = inject(CrmInjectionToken)?.services?.userService;

  _control = new FormControl<UserOption>({} as UserOption, {
    nonNullable: true,
  });
  set control(control: FormControl<UserOption | string>) {
    this.loading.set(true);
    this.controlValueToUserOption(control.value)
      .then((value) => {
        this.control.setValue(value);
      })
      .finally(() => this.loading.set(false));
  }
  get control(): FormControl<UserOption> {
    return this._control;
  }

  _group: UntypedFormGroup;
  set formGroup(group: UntypedFormGroup) {
    this._group = group;
  }
  get formGroup(): UntypedFormGroup {
    return this._group;
  }

  _fieldId = '';
  set fieldId(fieldId: string) {
    this._fieldId = fieldId;
  }
  get fieldId(): string {
    return this._fieldId;
  }

  get hasModification(): boolean {
    return this.control.dirty;
  }

  _label = '';
  set label(label: string) {
    this._label = label;
  }
  get label(): string {
    return this._label;
  }

  get value(): FieldValueInterface {
    return {
      fieldId: this.fieldId,
      stringValue: this.control.value?.id ?? '',
    };
  }

  constructor(private readonly inlineEditService: InlineEditService) {
    super();
    if (!this.readOnly) {
      this.options$ = this.displayName$$.pipe(
        takeUntilDestroyed(),
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((searchTerm) => this.userService?.searchUsers?.(searchTerm) ?? []),
        map((users) => this.mapSimplifiedUsersToOption(users)),
      );
      this.validateSearchPermission();
    }
  }

  clear(): void {
    this.control.reset();
    this.displayName$$.next('');
  }

  displayFn(user: UserOption): string {
    return user?.displayName || user?.id || '';
  }

  saveField(): void {
    if (!this.control.errors && !this.readOnly) {
      this.inlineEditService.saveInlineEdit(this.objectType, [this.value]);
    }
  }

  handleSearchTermInput(): void {
    const searchTerm = this.searchInput?.nativeElement.value ?? '';
    this.displayName$$.next(searchTerm);
    this.control.markAsDirty();
    this.formGroup.markAsDirty();
  }

  private async controlValueToUserOption(value: UserOption | string): Promise<UserOption> {
    if (typeof value == 'string') {
      if (this.userService) {
        try {
          const users = await firstValueFrom(this.userService.getMultiUsers([value]));
          if (users?.[0]?.userId) {
            return this.mapSimplifiedUsersToOption(users)[0];
          }
        } catch (error) {
          console.error(error);
        }
      }
    } else {
      return value;
    }
    return {} as UserOption;
  }

  private async validateSearchPermission(): Promise<void> {
    // disable the input if the searchUsers function is not available or the user don't have access to the results
    let validResult: Promise<void>;
    if (this.userService?.searchUsers) {
      validResult = firstValueFrom(this.userService.searchUsers('')).then(() => undefined);
    } else {
      validResult = Promise.reject();
    }
    validResult.catch(() => this.control.disable());
  }

  private mapSimplifiedUsersToOption(users: SimplifiedUser[]): UserOption[] {
    return users.map((u) => ({ id: u.userId, displayName: u.displayName }));
  }
}
