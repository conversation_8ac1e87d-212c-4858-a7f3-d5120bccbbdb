import { inject, Injectable } from '@angular/core';
import {
  AssistantApiService,
  AssistantInterface,
  AssistantType,
  ConnectionInterface,
  PromptModule<PERSON>ey,
  UpsertAssistantResponse,
} from '@vendasta/ai-assistants';
import { ACCOUNT_GROUP_ID_TOKEN, AI_DEFAULT_WORKFORCE_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import {
  AiAssistant,
  AiConnection,
  ConnectionType,
  MY_LISTING_CONNECTION_NAME,
} from '../../../core/interfaces/ai-assistant.interface';
import { getNamespace } from '../../../core/services/ai-assistant-utils';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ActivatedRoute } from '@angular/router';
import {
  catchError,
  combineLatest,
  EMPTY,
  filter,
  firstValueFrom,
  from,
  map,
  Observable,
  of,
  shareReplay,
  switchMap,
} from 'rxjs';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { AiAssistantForm, AiKnowledgeFormArray } from '../../../core/forms';
import { AiKnowledgeService } from '@galaxy/ai-knowledge';
import { KnowledgeSource } from '@vendasta/embeddings';
import { knowledgeAppType } from '../../../core/ai-assistant.constants';
import { WEBCHAT_WIDGET_CONNECTION_TYPE } from '../../../core';
import { ConversationApiService } from '@vendasta/conversation';
import { HttpResponse } from '@angular/common/http';
import { AiToolFormRegistryService } from '../../../core/services/ai-tool-form-registry.service';

@Injectable()
export class AiAssistantFormV2Service {
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly defaultWorkforce$ = inject(AI_DEFAULT_WORKFORCE_TOKEN);
  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly assistantApiService = inject(AssistantApiService);
  private readonly route = inject(ActivatedRoute);
  private readonly snackbarService = inject(SnackbarService);
  private readonly conversationApiService = inject(ConversationApiService);
  private readonly knowledgeService = inject(AiKnowledgeService);
  private readonly toolFormRegistry = inject(AiToolFormRegistryService);

  private readonly assistantId$ = this.route.paramMap.pipe(
    map((params) => params.get('assistantId') || ''),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  readonly aiAssistant$ = combineLatest([
    this.assistantId$,
    this.partnerId$,
    this.accountGroupId$,
    this.defaultWorkforce$,
  ]).pipe(
    switchMap(([assistantId, partnerId, accountGroupId, defaultWorkforce]): Observable<AiAssistant | null> => {
      // If we're creating a new assistant
      if (!assistantId) {
        const namespace = getNamespace(accountGroupId, partnerId);
        if (!namespace) {
          throw of(null);
        }
        return of({
          assistant: {
            id: '',
            name: 'Custom Assistant',
            type: AssistantType.ASSISTANT_TYPE_CUSTOM,
            avatarUrl: '',
            namespace: namespace,
          },
        });
      }

      return this.aiAssistantService.getAssistant(partnerId, accountGroupId, assistantId).pipe(
        map((assistant) => {
          return this.aiAssistantService.hydrateAssistantWithDefaultInfo(assistant, defaultWorkforce, accountGroupId);
        }),
        catchError((err) => {
          if (err.status === 404) {
            return this.aiAssistantService.getDefaultAssistant(partnerId, accountGroupId, assistantId).pipe(
              map((assistant) => assistant),
              catchError(() => {
                this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_NOT_FOUND');
                return EMPTY;
              }),
            );
          } else {
            this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ERROR_FETCHING_ASSISTANT');
            return EMPTY;
          }
        }),
      );
    }),
    filter((assistant) => !!assistant),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  readonly promptContents$: Observable<Record<string, string>> = this.aiAssistant$.pipe(
    switchMap((assistant) => {
      const namespace = assistant.assistant.namespace;
      if (namespace === undefined) {
        return of({});
      }

      const promptModuleKeys =
        assistant.assistant.configurableGoals
          ?.map(
            (goal) =>
              goal.goal?.promptModules?.map((pm) => new PromptModuleKey({ id: pm.id, namespace: pm.namespace })) || [],
          )
          .flat() || [];

      if (promptModuleKeys.length === 0) {
        return of({});
      }

      return from(this.aiAssistantService.getMultiPromptModuleVersions(promptModuleKeys));
    }),
  );

  readonly knowledge$ = this.assistantId$.pipe(switchMap((assistantId) => this.getKnowledge(assistantId)));

  private readonly aiConnectionsInfo$ = combineLatest([this.partnerId$, this.accountGroupId$, this.aiAssistant$]).pipe(
    switchMap(([partnerId, accountGroupId, aiAssistant]) => {
      // Custom assistants have no possible connections right now
      if (!aiAssistant?.assistant?.id || aiAssistant?.assistant?.type === AssistantType.ASSISTANT_TYPE_CUSTOM) {
        return of([]);
      }
      return this.aiAssistantService.listConnectionsForAssistant(partnerId, accountGroupId, aiAssistant?.assistant?.id);
    }),
    catchError(() => {
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.CHANNELS.FETCH_ERROR');
      return of([] as AiConnection[]);
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  private readonly widgetConnectionNames$ = this.aiConnectionsInfo$.pipe(
    map((aiConnections) => {
      return aiConnections
        .filter((aiConnection) => aiConnection.connection.connectionType === WEBCHAT_WIDGET_CONNECTION_TYPE)
        .map((aiConnection) => aiConnection.connection.id);
    }),
    switchMap((webchatWidgetConnectionIds) => {
      const widgetIds = webchatWidgetConnectionIds.filter((id): id is string => !id || !id.includes('default'));
      if (widgetIds?.length > 0) {
        return this.conversationApiService.getMultiWidget({ widgetIds: widgetIds }).pipe(
          map((response) => response.widgets),
          catchError(() => {
            this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.CHANNELS.FETCH_WIDGET_NAME_WARNING');
            return of([]);
          }),
        );
      }
      return of([]);
    }),
    map((widgets) => {
      return widgets.reduce(
        (acc, widget) => {
          acc[widget.widgetId] = widget.name;
          return acc;
        },
        {} as Record<string, string>,
      );
    }),
  );

  public readonly aiConnections$ = combineLatest([this.aiConnectionsInfo$, this.widgetConnectionNames$]).pipe(
    map(([aiConnections, widgetConnectionNames]) => {
      return aiConnections.map((aiConnection) => {
        const connection = aiConnection.connection;
        const connectionName = this.getConnectionName(connection, widgetConnectionNames);
        return {
          ...aiConnection,
          connection: {
            ...connection,
            name: connectionName,
          },
        };
      });
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  public readonly form$ = combineLatest([
    this.aiAssistant$,
    this.promptContents$,
    this.aiConnections$,
    this.knowledge$,
  ]).pipe(
    map(([assistant, promptModuleContents, hydratedAiConnections, knowledgeSources]) => {
      assistant.connections = hydratedAiConnections;
      return new AiAssistantForm(assistant, promptModuleContents, knowledgeSources, this.toolFormRegistry);
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  public getKnowledge(assistantId: string): Observable<KnowledgeSource[]> {
    const businessKSID = this.knowledgeService.businessProfileKSId();
    return from(this.knowledgeService.listAllKnowledgeSourcesForApp('APP-AI' + assistantId)).pipe(
      map((knowledgeSources) => {
        const businessIndex = knowledgeSources.findIndex((ks) => ks.id === businessKSID);

        if (businessIndex > -1) {
          const businessKS = knowledgeSources[businessIndex];
          return [
            businessKS,
            ...knowledgeSources.slice(0, businessIndex),
            ...knowledgeSources.slice(businessIndex + 1),
          ];
        }
        return knowledgeSources;
      }),
    );
  }

  public upsertAssistant(assistant: AssistantInterface, applyDefaults = false): Observable<UpsertAssistantResponse> {
    return this.assistantApiService.upsertAssistant({
      assistant: assistant,
      options: {
        applyDefaults: applyDefaults,
      },
    });
  }

  public updateConnections(
    assistant: AssistantInterface,
    connections: AiConnection[],
    connectionStates: Array<{ connectionId: string; enabled: boolean }>,
  ): Observable<HttpResponse<null>> {
    return this.assistantApiService.setAssistantConnections({
      assistantKey: {
        id: assistant?.id,
        namespace: assistant?.namespace,
      },
      associationStates: connections
        ?.map((aiConnection) =>
          !aiConnection?.connection.isConnectionLocked
            ? {
                connectionKey: {
                  id: aiConnection.connection.id,
                  namespace: aiConnection.connection.namespace,
                  connectionType: aiConnection.connection.connectionType,
                },
                isAssociated:
                  connectionStates.find((state) => state.connectionId === aiConnection.connection.id)?.enabled ?? false,
              }
            : null,
        )
        .filter((connection) => connection !== null),
    });
  }

  private getConnectionName(connection: ConnectionInterface, widgetConnectionNames: Record<string, string>): string {
    if (
      connection.namespace?.accountGroupNamespace &&
      connection.connectionType === ConnectionType.WebchatWidget &&
      connection.name !== MY_LISTING_CONNECTION_NAME &&
      !connection.assistantKeys?.length
    ) {
      return 'AI_ASSISTANT.SETTINGS.CONNECTIONS.WEBCHAT_CREATING';
    }

    if (!connection.id) {
      return connection.name || '';
    }
    return widgetConnectionNames[connection.id] || '';
  }

  async updateKnowledgeWithChanges(knowledgeFormArray: AiKnowledgeFormArray): Promise<void> {
    const updatePromises: Promise<void>[] = [];

    for (const knowledgeForm of knowledgeFormArray.controls) {
      if (knowledgeForm.dirty) {
        const newKnowledge = knowledgeForm.toKnowledgeSource();
        if (newKnowledge) {
          updatePromises.push(this.knowledgeService.updateKnowledgeSourceNameAndConfig(newKnowledge));
        }
      }
    }

    await Promise.all(updatePromises);
  }

  buildAiKnowledgeAppConfigurationUrl(assistantId: string): Observable<string> {
    return this.accountGroupId$.pipe(
      map((accountGroupId) => {
        if (accountGroupId) {
          return `/account/location/${accountGroupId}/ai/assistants/${assistantId}/edit`;
        }
        return `/ai/assistants/${assistantId}/edit`;
      }),
    );
  }

  async updateAssistantKnowledge(appId: string, appName: string, knowledgeForms: AiKnowledgeFormArray): Promise<void> {
    await this.updateKnowledgeWithChanges(knowledgeForms);
    const knowledgeSources = knowledgeForms.toKnowledgeSources();
    const configurationUrl = await firstValueFrom(
      this.assistantId$.pipe(
        switchMap((assistantId) => this.aiAssistantService.buildAiKnowledgeAppConfigurationUrl(assistantId)),
      ),
    );
    return this.knowledgeService.upsertKnowledgeForApp(
      appId,
      appName,
      configurationUrl,
      knowledgeAppType,
      knowledgeSources,
    );
  }
}
