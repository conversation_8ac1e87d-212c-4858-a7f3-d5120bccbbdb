import { Component, inject, input, output, signal } from '@angular/core';
import { AiCapabilityFormComponent } from '../ai-capability-form/ai-capability-form.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatBadgeModule } from '@angular/material/badge';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule, MatIconButton } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { MatDividerModule } from '@angular/material/divider';
import { AiCapabilityForm, AiCapabilityFormArray, AiToolForm } from '../../../core/forms';
import { MatDialog } from '@angular/material/dialog';
import { AiCapabilitySelectComponent } from '../ai-capability-select-dialog/ai-capability-select-dialog.component';
import { GoalInterface, PromptModuleKey } from '@vendasta/ai-assistants';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BookMeNowCapabilityFormComponent } from '../ai-capability-form/book-me-now-capability-form/book-me-now-capability-form.component';
import { MatMenuModule } from '@angular/material/menu';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';

const DEFAULT_MEETING_BOOKING_GOAL_ID = 'BookAppointmentsWithBookMeNow';

@Component({
  selector: 'ai-capabilities-accordion',
  imports: [
    AiCapabilityFormComponent,
    MatMenuModule,
    MatBadgeModule,
    MatCardModule,
    MatDividerModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatIconButton,
    TranslateModule,
    BookMeNowCapabilityFormComponent,
  ],
  templateUrl: './ai-capabilities-accordion.component.html',
  styleUrl: './ai-capabilities-accordion.component.scss',
  host: {
    class: 'ai-capabilities-accordion',
  },
})
export class AiCapabilityAccordionComponent {
  readonly capabilities = input.required<AiCapabilityFormArray>();
  readonly toolFormDisplay = output<{ parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined>();

  readonly addDialog = inject(MatDialog);
  private readonly assistantService = inject(AiAssistantService);
  private readonly snackbar = inject(SnackbarService);
  private readonly confirmationModal = inject(OpenConfirmationModalService);

  readonly expandedPanels = signal<number[]>([]);

  handleCapabilityRemoveClicked(event: Event, i: number) {
    event.stopPropagation();
    this.confirmationModal
      .openModal({
        type: 'warn',
        title: 'AI_ASSISTANT.SETTINGS.REMOVE_CAPABILITY_TITLE',
        message: 'AI_ASSISTANT.SETTINGS.REMOVE_CAPABILITY_CONFIRMATION',
        confirmButtonText: 'AI_ASSISTANT.SETTINGS.REMOVE',
      })
      .subscribe((confirmed: boolean) => {
        if (confirmed) {
          this.capabilities().removeAt(i);

          this.expandedPanels.update((panels) =>
            panels.filter((index) => index !== i).map((index) => (index > i ? index - 1 : index)),
          );
        }
      });
  }

  async handleCapabilityAddClicked() {
    const dialogRef = this.addDialog.open(AiCapabilitySelectComponent, {
      data: {
        excludedGoalIds: this.capabilities()
          .controls.map((capabilityForm) => capabilityForm.controls.goalId.value)
          .filter((id) => id != null),
      },
    });

    dialogRef.afterClosed().subscribe(async (result: { goal?: GoalInterface; newGoal: boolean } | '') => {
      if (result === '' || !result) {
        return;
      }

      const { goal: chosenGoal, newGoal } = result;

      if (chosenGoal) {
        let promptModuleContents: Record<string, string> = {};
        const promptModuleKeys =
          chosenGoal?.promptModules?.map((pm) => new PromptModuleKey({ id: pm.id, namespace: pm.namespace })) || [];
        if (promptModuleKeys.length > 0) {
          try {
            promptModuleContents = await this.assistantService.getMultiPromptModuleVersions(promptModuleKeys);
          } catch (err) {
            this.snackbar.openErrorSnack('AI_ASSISTANT.GOALS.ADD_GOAL.ERROR_LOADING_CAPABILITY');
            return;
          }
        }
        const newCapabilityForm = new AiCapabilityForm({ goal: chosenGoal, configuration: [] }, promptModuleContents);
        this.capabilities().push(newCapabilityForm);
      } else if (newGoal) {
        const newCapabilityForm = new AiCapabilityForm();
        this.capabilities().push(newCapabilityForm);
        this.expandedPanels.update((panels) => [...panels, this.capabilities().length - 1]);
      }
    });
  }

  moveCapability(fromIndex: number, toIndex: number): void {
    const formArray = this.capabilities();

    // Don't move if indices are invalid
    if (toIndex < 0 || toIndex >= formArray.controls.length) {
      return;
    }

    const control = formArray.at(fromIndex);
    formArray.removeAt(fromIndex);
    formArray.insert(toIndex, control);

    // Update expanded panels to maintain expansion state
    const expandedPanels = this.expandedPanels();
    const updatedPanels = expandedPanels.map((panel) =>
      panel === fromIndex
        ? toIndex
        : panel > fromIndex && panel <= toIndex
          ? panel - 1
          : panel < fromIndex && panel >= toIndex
            ? panel + 1
            : panel,
    );

    this.expandedPanels.set(updatedPanels);
  }

  expandPanel(index: number) {
    if (this.expandedPanels().includes(index)) {
      return;
    }

    this.expandedPanels.update((panels) => [...panels, index]);
  }

  collapsePanel(index: number) {
    this.expandedPanels.update((panels) => panels.filter((panelIndex) => panelIndex !== index));
  }

  isMeetingBookingCapability(c: AiCapabilityForm): boolean {
    return c.controls?.goalId?.getRawValue() === DEFAULT_MEETING_BOOKING_GOAL_ID;
  }
}
