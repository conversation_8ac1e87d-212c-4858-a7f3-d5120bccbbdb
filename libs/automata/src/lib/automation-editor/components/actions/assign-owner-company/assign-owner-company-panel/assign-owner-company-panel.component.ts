import { Component, Injector, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { CommonActionPanelComponent } from '../../../common/common-action-panel-component.directive';
import { DataType, RuleInterface } from '@vendasta/automata';
import { AutomationViewService } from '../../../../../automation/automation-view.service';
import { UsersService } from '../../../../../data-services/users.service';
import { AutomationReplacementChipsComponent } from '../../../../../common/components';
import { MatRadioModule } from '@angular/material/radio';
import { UserSelectorService } from '../../../../../data-services/user-selector.service';

@Component({
  selector: 'automata-assign-owner-company-panel',
  templateUrl: './assign-owner-company-panel.component.html',
  styleUrls: ['./assign-owner-company-panel.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    AutomationReplacementChipsComponent,
    MatRadioModule,
  ],
})
export class AssignOwnerToCompanyPanelComponent extends CommonActionPanelComponent implements OnInit {
  supportedDataTypes: DataType[] = [DataType.DATA_TYPE_SMB_USER_ID, DataType.DATA_TYPE_STRING];
  userSelectorService = this.injector.get(UserSelectorService);
  form: FormGroup;
  formControl: FormControl = new FormControl(null);

  constructor(
    protected readonly automationService: AutomationViewService,
    protected readonly usersService: UsersService,
    private injector: Injector,
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    const jsonData = this.step ? JSON.parse(this.step.data) : { fields: [] };
    this.formControl.setValue(jsonData.user_id);
  }

  getRules(): RuleInterface[] {
    return [];
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step.data) : {};

    jsonData.user_id = this.formControl.value;
    return jsonData;
  }

  getControlsToValidateOnSave(): (FormControl | FormGroup | FormArray)[] {
    return [this.formControl];
  }

  isFormValid(): boolean {
    return this.formControl.valid;
  }

  finishedLoadingSelector(): void {
    this.savingEnabled = true;
  }
}
