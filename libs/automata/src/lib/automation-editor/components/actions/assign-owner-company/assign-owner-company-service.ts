import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivityInterface, WorkflowStepInterface } from '@vendasta/automata';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap } from 'rxjs/operators';
import { UsersService } from '../../../../data-services/users.service';
import { CommonDisplayActivityService } from '../../common/common-display-activity.service';
import { CommonDisplayActionService } from '../../common/common-display-action.service';

@Injectable({ providedIn: 'root' })
export class AssignOwnerToCompanyService implements CommonDisplayActivityService, CommonDisplayActionService {
  constructor(
    protected readonly translateService: TranslateService,
    private readonly usersService: UsersService,
  ) {}

  getActivityTitle(_namespace: string, activity: ActivityInterface): Observable<string> {
    const activityData = activity?.activityData ? JSON.parse(activity.activityData) : {};
    const userId = activityData?.new_owner;

    if (userId) {
      return this.getUserName(userId).pipe(
        switchMap((name) =>
          this.translateService.stream('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.COMPANY_OWNER', {
            owner: name,
          }),
        ),
      );
    }
    return this.translateService.stream('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.COMPANY_OWNER', {
      owner: this.translateService.instant('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.OWNER'),
    });
  }

  getActionSubtitle(_namespace: string, step: WorkflowStepInterface): Observable<string> {
    const data = step ? JSON.parse(step.data) : {};

    return this.getActionSubtitleSuccessState(step).pipe(
      catchError((err) => {
        console.error(err);
        return this.getActionSubtitleErrorState(data, step);
      }),
    );
  }
  getTemplateActionSubtitle(_namespace: string, step: WorkflowStepInterface): Observable<string> {
    return this.getActionSubtitleEmptyState(step);
  }
  getActivitySubtitle(_namespace: string, _activity: ActivityInterface): Observable<string> {
    return of('');
  }
  getActionSubtitleSuccessState(step: WorkflowStepInterface): Observable<string> {
    const data = step ? JSON.parse(step.data) : {};
    const userId = data.user_id;

    return this.usersService.getUsersById([userId]).pipe(
      switchMap((users) => {
        const name = this.usersService.getUserDisplay(users, userId);

        return this.translateService.stream('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.COMPANY_OWNER', {
          owner: name,
        });
      }),
      catchError((err) => {
        console.error('Failed to fetch user', err);
        return this.translateService.stream('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.OWNER');
      }),
    );
  }

  getActionSubtitleErrorState(_params: any, _step: WorkflowStepInterface): Observable<string> {
    return this.translateService.stream('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.OWNER');
  }

  getActionSubtitleEmptyState(_step: WorkflowStepInterface): Observable<string> {
    return this.translateService.stream('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.OWNER');
  }

  getUserName(userId: string): Observable<string> {
    return this.usersService.getUsersById([userId]).pipe(
      map(
        (users) =>
          this.usersService.getUserDisplay(users, userId) ||
          this.translateService.instant('AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.OWNER'),
      ),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }
}
