import { Injectable } from '@angular/core';
import { ConfigService } from './config/config.service';
import { Observable, of } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { FeatureFlagMultiResponse, FeatureFlagService } from '@vendasta/businesses';
import { PartnerRouteService } from './pid-route.service';

@Injectable()
export class SMFeaturesService {
  businessNavigationEnabled$: Observable<boolean>;
  postTemplatesEnabled$: Observable<boolean>;
  limitSocialServicesEnabled$: Observable<boolean>;

  calendarWeekImprovements$: Observable<boolean>;
  noUpgradePath$: Observable<boolean>;
  calendarReportEnabled$: Observable<boolean>;
  postOrchestrationProxy$: Observable<boolean>;
  showReauthMetaAccountBanner$: Observable<boolean>;
  showReauthFacebookAccountBanner$: Observable<boolean>;
  aiContentEnhancementEnabled$: Observable<boolean> = of(false);
  enableYouTube$: Observable<boolean> = of(false);
  enableTikTok$: Observable<boolean> = of(false);
  enableInstagramStories$: Observable<boolean> = of(false);
  enableInstagramStoriesML$: Observable<boolean> = of(false);

  instagramMultilocationEnabled$: Observable<boolean> = of(false);
  linkedinMultilocationEnabled$: Observable<boolean> = of(false);
  aiCustomInstructionsEnabled$: Observable<boolean> = of(false);
  unsplashImageEnabled$: Observable<boolean> = of(false);
  wordpressConnectionEnabled$: Observable<boolean> = of(false);

  // Partner page feature flags
  autoPost$: Observable<boolean>;
  autoPostSettings$: Observable<boolean>;
  adminDashboard$: Observable<boolean>;

  aiBundleCreate$: Observable<boolean> = of(false);
  composerWithHideDraft$: Observable<boolean> = of(false);
  composerWithHideDraftMulti$: Observable<boolean> = of(false);
  isDALLE3Enabled$: Observable<boolean> = of(false);
  isDALLE3EnabledML$: Observable<boolean> = of(false);
  enableAiChatbot$: Observable<boolean> = of(false);
  enableAiChatbotML$: Observable<boolean> = of(false);
  enableAiDataConsent$: Observable<boolean> = of(false);
  enableAiDataConsentML$: Observable<boolean> = of(false);
  displayNewPostFlow$: Observable<boolean> = of(false);
  displayToolsOption$: Observable<boolean> = of(false);
  enableAiKnowledge$: Observable<boolean> = of(false);
  enableAiKnowledgeML$: Observable<boolean> = of(false);
  enableBlog$: Observable<boolean> = of(false);
  linkedInMigrationEnabled$: Observable<boolean> = of(false);
  postCampaignEnable$: Observable<boolean> = of(false);
  enableStepperinBulkCSVML$: Observable<boolean> = of(false);
  enableAddAnotherPost$: Observable<boolean> = of(false);

  constructor(
    private configService: ConfigService,
    private featureFlagService: FeatureFlagService,
    private partnerService: PartnerRouteService,
  ) {
    const features = [
      'business_navigation',
      'sm_post_templates',
      'sm_limit_social_services',
      'sm_calendar_week_improvements',
      'no_upgrade_path',
      'sm_report_calendar',
      'sm_post_orchestration_proxy',
      'reauth_meta_account_banner_2023',
      'reauth_facebook_account_banner_2023',
      'sm_youtube_wip',
      'sm_instagram_stories',
      'sm_ai_custom_instructions',
      'sm_new_stock_image_layout',
      'ai_create_bulk',
      'composer_hide_draft',
      'sm_dalle_3_wip',
      'sm_tiktok_connection',
      'ai_chatbot_sm',
      'ai_data_usage_sm',
      'create_posts_split_flows',
      'improved_tools_option',
      'sm_ai_knowledge_integration',
      `wordpress_connection_sm`,
      'sm_blog',
      'linkedin_migration_sm',
      'sm_post_campaign',
      'sm_stepper_bulk_upload_ml',
      'add_another_post',
    ];

    const featuresEnabled$ = this.featuresEnabled(features);

    this.businessNavigationEnabled$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['business_navigation']),
    );

    this.postTemplatesEnabled$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['sm_post_templates']));

    this.limitSocialServicesEnabled$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_limit_social_services']),
    );

    this.calendarWeekImprovements$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_calendar_week_improvements']),
    );
    this.noUpgradePath$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['no_upgrade_path']));

    this.calendarReportEnabled$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_report_calendar']),
    );

    this.postOrchestrationProxy$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_post_orchestration_proxy']),
    );

    this.enableInstagramStories$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_instagram_stories']),
    );
    this.aiBundleCreate$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['ai_create_bulk']));

    this.displayNewPostFlow$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['create_posts_split_flows']),
    );

    this.displayToolsOption$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['improved_tools_option']),
    );

    this.enableAiKnowledge$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_ai_knowledge_integration']),
    );

    this.linkedInMigrationEnabled$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['linkedin_migration_sm']),
    );

    this.postCampaignEnable$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['sm_post_campaign']));

    this.enableStepperinBulkCSVML$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_stepper_bulk_upload_ml']),
    );

    this.enableAddAnotherPost$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['add_another_post']));

    this.isDALLE3Enabled$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['sm_dalle_3_wip']));

    if (partnerService) {
      const partnerFeatures = [
        'sm_autoposting',
        'sm_auto_posting_settings',
        'sm_admin_dashboard',
        'sm_multilocation_instagram',
        'sm_instagram_stories',
        'composer_hide_draft',
        'sm_dalle_3_wip',
        'ai_chatbot_sm',
        'ai_data_usage_sm',
        'sm_ai_knowledge_integration',
        'sm_multilocation_linkedin',
      ];
      const partnerFeaturesEnabled$ = this.partnerFeaturesEnabled(partnerFeatures);
      this.autoPost$ = partnerFeaturesEnabled$.pipe(map((partnerFeature) => partnerFeature['sm_autoposting']));
      this.autoPostSettings$ = partnerFeaturesEnabled$.pipe(
        map((partnerFeature) => partnerFeature['sm_auto_posting_settings']),
      );
      this.adminDashboard$ = partnerFeaturesEnabled$.pipe(
        map((partnerFeature) => partnerFeature['sm_admin_dashboard']),
      );
      this.instagramMultilocationEnabled$ = partnerFeaturesEnabled$.pipe(
        map((partnerFeature) => partnerFeature['sm_multilocation_instagram']),
      );
      this.linkedinMultilocationEnabled$ = partnerFeaturesEnabled$.pipe(
        map((partnerFeature) => partnerFeature['sm_multilocation_linkedin']),
      );
      this.enableInstagramStoriesML$ = partnerFeaturesEnabled$.pipe(
        map((featuresEnabled) => featuresEnabled['sm_instagram_stories']),
      );
      this.composerWithHideDraftMulti$ = partnerFeaturesEnabled$.pipe(
        map((featuresEnabled) => Boolean(featuresEnabled?.composer_hide_draft)),
      );
      this.isDALLE3EnabledML$ = partnerFeaturesEnabled$.pipe(
        map((featuresEnabled) => featuresEnabled['sm_dalle_3_wip']),
      );
      this.enableAiChatbotML$ = partnerFeaturesEnabled$.pipe(
        map((featuresEnabled) => featuresEnabled['ai_chatbot_sm']),
      );
      this.enableAiDataConsentML$ = partnerFeaturesEnabled$.pipe(
        map((featuresEnabled) => featuresEnabled['ai_data_usage_sm']),
      );
      this.enableAiKnowledgeML$ = partnerFeaturesEnabled$.pipe(
        map((featuresEnabled) => featuresEnabled['sm_ai_knowledge_integration']),
      );
    }

    this.showReauthMetaAccountBanner$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['reauth_meta_account_banner_2023']),
    );
    this.showReauthFacebookAccountBanner$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['reauth_facebook_account_banner_2023']),
    );
    this.enableYouTube$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['sm_youtube_wip']));
    this.enableTikTok$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['sm_tiktok_connection']));
    this.aiCustomInstructionsEnabled$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_ai_custom_instructions']),
    );

    this.wordpressConnectionEnabled$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['wordpress_connection_sm']),
    );

    this.enableBlog$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['sm_blog']));

    this.unsplashImageEnabled$ = featuresEnabled$.pipe(
      map((featuresEnabled) => featuresEnabled['sm_new_stock_image_layout']),
    );

    this.composerWithHideDraft$ = featuresEnabled$.pipe(
      map((featuresEnabled) => Boolean(featuresEnabled?.composer_hide_draft)),
    );

    this.enableAiChatbot$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['ai_chatbot_sm']));

    this.enableAiDataConsent$ = featuresEnabled$.pipe(map((featuresEnabled) => featuresEnabled['ai_data_usage_sm']));
  }

  featuresEnabled(featureIds: string[]): Observable<FeatureFlagMultiResponse> {
    return this.configService.config$.pipe(
      switchMap((config) =>
        this.featureFlagService.checkFeatureFlagsMulti(config.partner_id, config.market_id, featureIds),
      ),
      shareReplay(1),
    );
  }

  partnerFeaturesEnabled(featureIds: string[]): Observable<FeatureFlagMultiResponse> {
    if (!this.partnerService.partnerId) {
      return of({});
    }
    return this.featureFlagService.checkFeatureFlagsMulti(
      this.partnerService.partnerId,
      this.partnerService.marketId,
      featureIds,
    );
  }
}
