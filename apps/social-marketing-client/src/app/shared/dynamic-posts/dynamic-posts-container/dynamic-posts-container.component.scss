@use 'design-tokens' as *;

:host {
  display: block;
  height: 100%;

  .campaign-cards-container {
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  .title-container {
    display: flex;
    margin: 0px auto 10px;
    width: 600px;
  }

  .optimized-container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  .non-edit-title {
    display: flex;
  }
  .action-toolbar {
    margin: 0px auto 20px;
    max-width: 600px;
    width: 600px;
  }

  .editable-container {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 5px 10px;
    width: 800px;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  }

  .editable-input {
    border: none;
    outline: none;
    font-size: 16px;
    font-weight: bold;
    flex-grow: 1;
  }

  .button-container {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 8px;
  }

  .add-another-button {
    margin-bottom: 15px;
    background-color: $glxy-blue-700;
    color: white;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .add-another-icon {
    font-size: 18px;
  }

  .add-post-box {
    width: 100%;
    justify-content: center;
    display: flex;
  }
}
