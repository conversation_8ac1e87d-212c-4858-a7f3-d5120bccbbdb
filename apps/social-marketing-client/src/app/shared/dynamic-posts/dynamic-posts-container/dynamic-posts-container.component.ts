import { Component, Input, OnInit, ViewChildren, QueryList } from '@angular/core';
import { MediaType, Posts } from '../dynamic-posts-models';
import { DynamicPostCardComponent } from '../dynamic-post-card/dynamic-post-card.component';
import { SocialService } from '../../../composer/post';
import { MAX_ALLOWED_POSTS } from '../../../composer/constants';

@Component({
  selector: 'app-dynamic-posts-container',
  standalone: false,
  templateUrl: './dynamic-posts-container.component.html',
  styleUrl: './dynamic-posts-container.component.scss',
})
export class DynamicPostsContainerComponent implements OnInit {
  @Input() postType;
  @Input() services;
  @Input() posts: Posts[];
  @ViewChildren(DynamicPostCardComponent) postCards: QueryList<DynamicPostCardComponent>;
  campaignTitle = 'Glow Up: Hair Edition';
  isEditing = false;
  maxPost = MAX_ALLOWED_POSTS;

  toggleEdit() {
    this.isEditing = !this.isEditing;
  }

  ngOnInit() {
    this.addCampaignTimeLogic();
  }

  saveAll() {
    //Uncomment the below line to collect all the latest card data
    //const currentPosts = this.postCards.map((postCard) => postCard.postContent);
  }

  addPost() {
    const newPost = {
      postContent: [
        {
          socialNetwork: SocialService.INSTAGRAM,
          postText:
            'A fresh haircut isn’t just a style change—it’s a confidence boost! Whether you’re going for a bold transformation or a subtle trim, the right cut makes all the difference. What’s your go-to hairstyle? ✂️💇‍♀️',
          scheduleDate: this.getNextScheduleDate(),
          isEditing: false,
          medias: [
            {
              mediaUrl:
                'https://storage.googleapis.com/sm_media_upload_prod/AG-66MX2VK44N/media/images/c277a0b6-7345-4c5d-9cd1-8c43e3929446',
              mediaType: MediaType.IMAGE,
            },
          ],
        },
        {
          socialNetwork: SocialService.FACEBOOK,
          postText:
            'Nothing beats the relaxation of a luxurious hair spa treatment! From deep conditioning to scalp massages, it’s the ultimate self-care experience. When was the last time you treated yourself? 💆‍♀️✨',
          scheduleDate: this.getNextScheduleDate(),
          isEditing: false,
          medias: [
            {
              mediaUrl:
                'https://storage.googleapis.com/sm_media_upload_prod/AG-66MX2VK44N/media/images/c277a0b6-7345-4c5d-9cd1-8c43e3929446',
              mediaType: MediaType.IMAGE,
            },
          ],
        },
        {
          socialNetwork: SocialService.TWITTER,
          postText:
            'Your hair is your crown—wear it with pride! 👑 Whether it’s sleek and straight, voluminous curls, or a stylish fade, the right hairstyle speaks volumes. What’s your signature look? 💇‍♂️🔥',
          scheduleDate: this.getNextScheduleDate(),
          isEditing: false,
          medias: [
            {
              mediaUrl:
                'https://storage.googleapis.com/sm_media_upload_prod/AG-66MX2VK44N/media/images/c277a0b6-7345-4c5d-9cd1-8c43e3929446',
              mediaType: MediaType.IMAGE,
            },
          ],
        },
      ],
    };

    this.posts.push(newPost);
  }

  getNextScheduleDate(): Date {
    if (this.posts && this.posts.length > 0) {
      const lastPost = this.posts[this.posts.length - 1];
      if (lastPost.postContent && lastPost.postContent.length > 0) {
        const lastScheduledDate = new Date(lastPost.postContent[lastPost.postContent.length - 1].scheduleDate);
        lastScheduledDate.setDate(lastScheduledDate.getDate() + 1);
        return lastScheduledDate;
      }
    }
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 1);
    return defaultDate;
  }

  addCampaignTimeLogic() {
    const now = new Date();
    // Compute tomorrow's date with the current time
    const tomorrow = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() + 1,
      now.getHours(),
      now.getMinutes(),
      now.getSeconds(),
    );

    // Iterate over each campaign in postContent
    this.posts.forEach((campaign: any, campaignIndex: number) => {
      if (Array.isArray(campaign.postContent)) {
        campaign.postContent.forEach((post: any) => {
          // Calculate the schedule date for each post
          const newDate = new Date(tomorrow);
          newDate.setDate(tomorrow.getDate() + campaignIndex); // Add campaignIndex days to tomorrow
          post.scheduleDate = newDate;
        });
      }
    });
  }
}
