<div class="optimized-container">
  <div class="campaign-cards-container">
    <div class="title-container">
      <div class="editable-container" *ngIf="isEditing">
        <input type="text" [(ngModel)]="campaignTitle" class="editable-input" />
        <button mat-icon-button (click)="toggleEdit()">
          <mat-icon>done</mat-icon>
        </button>
      </div>
      <div class="non-edit-title" *ngIf="!isEditing">
        <h2>{{ campaignTitle }}</h2>
        <button mat-icon-button (click)="toggleEdit()">
          <mat-icon>edit</mat-icon>
        </button>
      </div>
    </div>

    <div>
      <app-dynamic-post-card
        *ngFor="let post of posts"
        [postContent]="post.postContent"
        [socialNetworks]="services"
      ></app-dynamic-post-card>
    </div>
    <div class="add-post-box" (click)="addPost()" *ngIf="posts?.length <= maxPost">
      <button class="add-another-button" mat-button color="primary">
        <mat-icon class="add-another-icon">add</mat-icon> {{ 'POSTS_CAMPAIGN.ADD_POST' | translate }}
      </button>
    </div>
  </div>
  <mat-card class="action-toolbar">
    <div class="button-container">
      <button mat-flat-button color="primary" (click)="saveAll()">{{ 'POSTS_CAMPAIGN.SAVE' | translate }}</button>
    </div>
  </mat-card>
</div>
