import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  signal,
  Signal,
  ViewChild,
  WritableSignal,
} from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { Location, PostType, SSIDPostType } from '@vendasta/social-posts';
import { auditTime, firstValueFrom, Observable, of, Subscription, take, takeUntil } from 'rxjs';
import { Customization } from '../../../core/post/customization';
import { ParsedPost, PreviewUploadedPosts, ValidationError } from '../../../shared/csv/csv-model';
import { CsvParserStore } from '../../../shared/csv/csv-parser.store';
import { ValidationService } from '../../../shared/csv/csv-validation.service';
import { SMComposerApiService } from '../../composer-api.service';
import { ComposerStoreService } from '../../composer-store.service';
import { BULK_UPLOAD_CSV_TEMPLATE, GMB_LABEL_ACTION_MAP, POSTHOG_KEYS } from '../../constants';
import { CustomizationValidatorService } from '../../customization-validator.service';
import { ComposerSettings, UploadedFile } from '../../interfaces';
import { GmbOptions } from '../../models/gmb-options';
import { SocialService } from '../../post';
import { BulkUploadPostsService } from './bulk-upload-posts.service';
import { FormatingRuleModalComponent } from './formating-rule-modal/formating-rule-modal.component';

import { MatStepper } from '@angular/material/stepper';
import { SMFeaturesService } from '../../../core/features.service';
import { MLSocialNetworkType } from '../../../shared/dynamic-posts/dynamic-posts-models';
import { Steps } from './bulk-upload.enum';
import { UntypedFormGroup } from '@angular/forms';

@Component({
  selector: 'app-bulk-upload-posts',
  standalone: false,
  templateUrl: './bulk-upload-posts.component.html',
  styleUrl: './bulk-upload-posts.component.scss',
})
export class BulkUploadPostsComponent implements OnInit, OnDestroy {
  @Input() visible = false;
  @Input() accountGroupId: string = null;
  @Input() brandId: string = null;
  @Input() showTopNavBar = false;
  @Input() showClose = true;
  @Input() showDraft = true;
  postService = SocialService;
  subscriptions: Subscription[] = [];
  uploadedPosts: Signal<ParsedPost[]> = signal([]);
  processing: WritableSignal<boolean> = signal(false);
  posts$: Observable<ParsedPost[]>;
  @Output() visibleEvent = new EventEmitter<boolean>();
  data: any;
  sampleCSV = BULK_UPLOAD_CSV_TEMPLATE;
  isML = false;
  networkType = MLSocialNetworkType;
  previewPosts: PreviewUploadedPosts;
  @ViewChild(MatStepper) stepper!: MatStepper;
  enableStepper$: Observable<boolean>;
  currentStep = Steps.UPLOAD;
  nxtBtnStatus = true;
  uploadForm: UntypedFormGroup;
  isPreviewPage$: Observable<boolean> = of(false);

  constructor(
    public composerStore: ComposerStoreService,
    private customizationValidatorService: CustomizationValidatorService,
    protected dialog: MatDialog,
    private csvParserStore: CsvParserStore,
    private bulkUploadPostsService: BulkUploadPostsService,
    private validationService: ValidationService,
    private composerApiService: SMComposerApiService,
    private snackService: SnackbarService,
    private productAnalyticsService: ProductAnalyticsService,
    private translateService: TranslateService,
    public featureFlagService: SMFeaturesService,
  ) {
    this.uploadForm = new UntypedFormGroup({
      editForm: new UntypedFormGroup({}),
    });
    this.posts$ = this.csvParserStore.posts$;
  }

  ngOnInit(): void {
    this.enableStepper$ = this.featureFlagService.enableStepperinBulkCSVML$;
    this.subscriptions.push(
      this.posts$.subscribe((posts) => {
        this.validatePosts(posts);
        this.uploadedPosts = signal(JSON.parse(JSON.stringify(posts)));
      }),
      this.composerStore.typeConnectionMlSelected$.pipe(takeUntil(this.isPreviewPage$)).subscribe(() => {
        this.validatePosts(this.uploadedPosts());
      }),
      this.csvParserStore.isUpdate$.subscribe((isUpdated) => {
        if (isUpdated) {
          this.processing.set(true);
        }
      }),
    );
  }

  async goToNextStep(): Promise<void> {
    const locationIds = await this.getLocationDetails();
    this.previewPosts = {
      posts: this.uploadedPosts(),
      selectedMLNetworks: this.getActiveNetworkTypes(locationIds as Location[]),
      isML: this.brandId ? true : false,
      locations: locationIds,
    };
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BULK_UPLOAD_PREVIEW, 'user', 'click', 0, {
      brandId: this.brandId,
    });
    this.currentStep = this.currentStep + 1;
    this.isPreviewPage$ = of(true);
    this.stepper.next();
  }

  prepareDownload() {
    fetch(this.sampleCSV)
      .then((response) => response.blob())
      .then((blob) => {
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = 'CSV Template.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      });
  }

  getNextStepEvent(step: any) {
    this.getBtnStatus();
    this.uploadForm.get('editForm').patchValue(step?.formGroupValue?.value);
  }

  getBtnStatus() {
    this.bulkUploadPostsService.emitStepper$.subscribe((step) => {
      if (step) {
        switch (this.currentStep) {
          case Steps.UPLOAD:
            this.nxtBtnStatus = !step?.uploadForm;
            break;
          case Steps.EDIT:
            this.nxtBtnStatus = !step?.previewForm;
            break;
        }
      }
    });
  }

  validatePosts(posts: ParsedPost[]): void {
    if (!posts || posts.length === 0) return;
    this.processing = signal(true);
    const processPost = async (index: number) => {
      if (index >= posts.length) {
        this.processing = signal(false);
        return;
      }
      const post = posts[index];
      const rootCustomization = await this.setRootCustomization(post);
      const validationErrors = await this.collectErrors(post, rootCustomization);
      if (validationErrors && validationErrors.length) {
        const error: ValidationError = {
          lineNumber: index + 2, // data starts from line 2 in CSV
          message: validationErrors,
          fileName: '',
        };
        this.validationService.appendError(error);
      }

      await processPost(index + 1);
    };

    processPost(0);
  }

  collectErrors(post: ParsedPost, customization: Customization): Promise<string[]> {
    return new Promise((resolve) => {
      if (!post) return;
      const brandValidators = this.customizationValidatorService.failingMultiLocationValidators$(customization);
      brandValidators.pipe(auditTime(500), take(1)).subscribe(async (errors) => {
        const resolvedErrors = await Promise.all(
          errors.map(async (error) => {
            const message = await firstValueFrom(error.message);
            return await firstValueFrom(this.translateService.stream(message));
          }),
        );
        resolve(resolvedErrors);
      });
    });
  }

  initialize(ssids?: string[]): void {
    if (this.accountGroupId) {
      this.composerStore.initializeStore(this.accountGroupId, ssids);
    }
  }

  goBack() {
    this.currentStep = this.currentStep - 1;
    if (this.currentStep === Steps.UPLOAD) {
      this.previewPosts = null;
      firstValueFrom(this.posts$).then((posts) => {
        this.validatePosts(posts);
        this.uploadedPosts = signal(JSON.parse(JSON.stringify(posts)));
      });
    }
    this.getBtnStatus();
  }

  public showComposer(composerSettings: ComposerSettings): void {
    if (composerSettings) {
      this.composerStore.applySettings(composerSettings);
    } else {
      this.composerStore.resetStore();
    }
    this.visible = true;
    this.visibleEvent.emit(true);
    // productAnalytics Service takes time to initialize
    // hence setTimeout is used to delay the event tracking
    setTimeout(() => {
      this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BULK_UPLOAD_CLICK, 'user', 'click', 0, {
        brandId: this.brandId,
      });
    }, 2000);
  }

  public hideComposer(): void {
    this.visible = false;
    this.visibleEvent.emit(false);
  }

  formatingRule(): void {
    this.dialog
      .open(FormatingRuleModalComponent, {
        autoFocus: false,
        width: '549px',
        height: 'auto',
        disableClose: true,
        enterAnimationDuration: 0,
      } as MatDialogConfig)
      .afterClosed()
      .subscribe();
  }

  setAgidOnStore(): void {
    this.composerStore.getAgidFromConfig();
  }

  async setRootCustomization(post: ParsedPost): Promise<Customization> {
    this.composerStore.rootCustomization.clear();
    this.composerStore.rootCustomization.updatePostText(post?.postText);
    this.composerStore.rootCustomization.updateScheduledDate(post?.scheduleDate);
    const postType = post.isIGStory ? PostType.POST_TYPE_STORIES : PostType.POST_TYPE_INVALID;
    this.composerStore.rootCustomization.updatePostType(postType);

    post?.Medias?.forEach((mediaEntry, index) => {
      const uploadedImage = new UploadedFile({
        url: mediaEntry.mediaUrl,
        fileType: mediaEntry.mediaType?.toLowerCase(),
      });
      this.composerStore.rootCustomization.updateMediaEntry(index, uploadedImage);
      const uploadedMediaObject = this.composerStore.rootCustomization.uploadedMediaObjects$$.getValue();
      this.composerStore.rootCustomization.updateUploadedMediaObjects([...uploadedMediaObject, uploadedImage]);
    });

    const gmbOptions = new GmbOptions();
    if (post.gmbCustomization) {
      if (post.gmbCustomization?.event) {
        gmbOptions.makeEvent = true;
        gmbOptions.eventOptions = {
          title: post.gmbCustomization.event.title,
          startDate: post.gmbCustomization.event.start,
          endDate: post.gmbCustomization.event.end,
        };
      }
      if (post.gmbCustomization?.action) {
        gmbOptions.addCta = true;
        gmbOptions.ctaOptions = {
          action: post.gmbCustomization.action.type,
          ctaUrl: post.gmbCustomization.action.linkUrl,
        };
        gmbOptions.ctaOptions.label = await firstValueFrom(
          this.translateService.stream(GMB_LABEL_ACTION_MAP[post.gmbCustomization.action.type]),
        );
      }
    }
    this.composerStore.rootCustomization.updateGmbOptions(gmbOptions);
    return this.composerStore.rootCustomization;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
  disableSubmit(): boolean {
    const selectedNetworks = this.composerStore.typeConnectionMlSelected$$.getValue();
    const networkSelected = Object.values(selectedNetworks).some((v) => v);
    return this.validationService.hasErrors() || this.processing() || !this.uploadedPosts().length || !networkSelected;
  }

  getSSIDPostType(mediaSelected, locations, igPostType): SSIDPostType[] {
    const groupedPosType = this.composerApiService.postTypeFromMediaEntries(mediaSelected);
    const ssidPostType = locations
      .flatMap((entry) => entry.socialServiceIds)
      .map((location: string) => ({
        socialServiceId: location,
        postType: location.toUpperCase().startsWith('IGU') ? igPostType : groupedPosType,
      }));

    return ssidPostType;
  }

  getActiveNetworkTypes(locations: Location[]): MLSocialNetworkType[] {
    const activeNetworkTypes = new Set<MLSocialNetworkType>();
    locations
      .flatMap((l) => l.socialServiceIds)
      .forEach((ssid) => {
        if (ssid.startsWith(this.networkType.FACEBOOK)) {
          activeNetworkTypes.add(this.networkType.FACEBOOK);
        } else if (ssid.startsWith(this.networkType.GMB)) {
          activeNetworkTypes.add(this.networkType.GMB);
        } else if (ssid.startsWith(this.networkType.INSTAGRAM)) {
          activeNetworkTypes.add(this.networkType.INSTAGRAM);
        }
      });

    return Array.from(activeNetworkTypes);
  }

  async submitPosts() {
    if (this.disableSubmit()) return;
    const locationIds = await this.getLocationDetails();
    this.uploadedPosts().forEach((p) => {
      const igPostType = this.getIgPostType(p?.isIGStory);
      const postTypes = this.getSSIDPostType(p.Medias, locationIds, igPostType);
      p.postTypes = postTypes;
    });
    this.subscriptions.push(
      this.bulkUploadPostsService.scheduleMultiplePosts(this.uploadedPosts(), locationIds).subscribe({
        next: (_) => {
          this.snackService.openSuccessSnack(this.translateService.instant('BULK_UPLOAD.SCHEDULE_SUCCESS'));
          this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BULK_UPLOAD_WORKFLOW_SUCCESS, 'user', 'click', 0, {
            brandId: this.brandId,
          });
          //<TO-DO> remove setTimeOut and add event to handle redirection in ML.
          setTimeout(() => {
            this.closeCSVComposer();
          }, 1000);
        },
        error: () => {
          this.snackService.openErrorSnack(this.translateService.instant('BULK_UPLOAD.SCHEDULE_FAIL'));
          this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BULK_UPLOAD_WORKFLOW_FAILED, 'user', 'click', 0, {
            brandId: this.brandId,
          });
        },
      }),
    );
  }

  getIgPostType(isStory: boolean) {
    if (isStory) {
      return PostType.POST_TYPE_STORIES;
    }
    return PostType.POST_TYPE_INVALID;
  }

  async getLocationDetails(): Promise<Location[]> {
    const brandagids = await firstValueFrom(this.composerStore.brandAgids$);
    const typeConnectionMlSelected = await firstValueFrom(this.composerStore.typeConnectionMlSelected$);
    const locationIds = this.composerStore.getMultiLocationSSIDs(brandagids, typeConnectionMlSelected);

    return locationIds as Location[];
  }

  closeCSVComposer(): void {
    top.postMessage('Composer Closed', '*');
    this.hideComposer();
    this.composerStore.resetStore();
  }

  onPostEdited(event) {
    this.uploadedPosts()[event.index].postText = event.post.postText;
    this.uploadedPosts()[event.index].scheduleDate = event.post.scheduleDate;
    this.uploadedPosts()[event.index].Medias = event.post?.Medias?.map((media) => ({
      mediaUrl: media.mediaUrl,
      mediaType: media.mediaType,
    }));
  }

  networkValidationEvent(event: boolean): void {
    if (event) {
      this.validatePosts(this.uploadedPosts());
    }
  }

  onPostDeleted(event: { index: number; isLastPost: boolean }) {
    this.uploadedPosts().splice(event.index, 1);
    if (event?.isLastPost) {
      this.goBack();
    }
  }
}
