@use 'design-tokens' as *;

.social-networks-container {
  display: flex;
  justify-content: flex-start;

  .network {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    border: 1px solid $light-gray;
    padding: 8px;
    background-color: $white;
    min-width: 225px;

    .network-name {
      font-weight: 500;
      margin-right: 4px;
    }

    .network-count {
      color: $blue;
      background-color: transparent;
      border: none;
    }

    .social-icon {
      width: 20px;
      height: 20px;
      border-radius: 100%;
      margin: 0 4px 0 4px;
    }
  }
}
