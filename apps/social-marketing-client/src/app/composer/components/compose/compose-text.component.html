<div class="compose-post-section">
  <div class="post-to-title p-b-06">
    <div class="composer-title">
      <mat-icon>short_text</mat-icon>
      {{ 'COMPOSER.CONTENT' | translate }}
    </div>
    <div *ngIf="hasPostableAccounts$ | async as hasPostableAccounts" (click)="customizeByAccountToggle()">
      <div *ngIf="!brandId && hasPostableAccounts.length > 1 && !longVideoWorkflow" class="customize-link">
        {{ customizeByAccountText$ | async }}
      </div>
    </div>
  </div>
  <div>
    <div class="yt-title" *ngIf="longVideoWorkflow">
      <form [formGroup]="youtubeSettingsForm">
        <glxy-form-row>
          <glxy-form-field class="m-0">
            <glxy-label>{{ 'COMPOSER.YOUTUBE_OPTIONS.VIDEO_TITLE' | translate }}</glxy-label>
            <input type="text" matInput formControlName="title" />
            <glxy-hint>{{ title?.value.length }}/{{ maxLengthTitle }}</glxy-hint>
          </glxy-form-field>
        </glxy-form-row>
        <glxy-form-row>
          <glxy-form-field class="m-0">
            <glxy-label>{{ 'COMPOSER.YOUTUBE_OPTIONS.PRIVACY_STATUS' | translate }}</glxy-label>
            <mat-select [hideSingleSelectionIndicator]="true" formControlName="privacyStatus">
              <mat-option *ngFor="let option of youtubePrivacyOptions" [value]="option.value">
                <p>
                  <strong>{{ option.label }}</strong>
                  &nbsp;&minus;&nbsp;{{ option.details | translate }}
                </p>
              </mat-option>
              <mat-select-trigger>
                {{ privacyStatus.value | titlecase }}
              </mat-select-trigger>
            </mat-select>
          </glxy-form-field>
        </glxy-form-row>
      </form>
    </div>
    <!--TEXT CONTAINER-->
    <p class="compose-yt-title" *ngIf="longVideoWorkflow">
      {{ 'COMPOSER.YOUTUBE_OPTIONS.VIDEO_DESCRIPTION' | translate }}
    </p>
    <app-post-text-actions [isMultiLocation]="!!brandId" [brandId]="brandId"></app-post-text-actions>
    <mat-progress-bar [mode]="'indeterminate'" *ngIf="previewLinkLoading$ | async"></mat-progress-bar>
    <div class="preview-link" *ngIf="previewLink$ | async">
      <span>Preview link</span>
      <mat-form-field class="preview-link-field">
        <input matInput disabled [value]="previewLink$ | async" />
        <i
          class="material-icons mdc-text-field__icon"
          tabindex="0"
          role="button"
          *ngIf="(requirePreviewLink$ | async) === false"
          (click)="clearPreviewLink()"
        >
          close
        </i>
      </mat-form-field>
    </div>

    <app-link-history-composer-warning
      [previewLinkHistory]="previewLinkHistory$ | async"
    ></app-link-history-composer-warning>

    <ng-template #mentionPrompt>
      <div class="mention-upgrade-container" [class.mention-upgrade-image]="isCtaMentionPrompt()">
        <div class="mention-upgrade-bg">
          <div>
            <span>&#64;</span>
          </div>
          <div>
            <span>
              {{ mentionPromptMessages[mentionPromptOption$$ | async] | translate }}
            </span>
          </div>
          <div *ngIf="isCtaMentionPrompt()">
            <a (mousedown)="upgradeLinkClicked(); $event.preventDefault()">
              {{ 'UPGRADE_CTA.SOCIAL_MENTIONS.ACTION_TEXT' | translate }}
            </a>
          </div>
          <div *ngIf="(mentionPromptOption$$ | async) === mentionPromptOptions.SELECT_SERVICE">
            <span>
              {{ 'COMPOSER.SOCIAL_MENTIONS.SELECT_SERVICE' | translate }}
            </span>
          </div>
        </div>
      </div>
    </ng-template>

    <ng-template #mentionTemplate let-item="item">
      <div class="mention-option-selector-row">
        <div class="mention-option-selector-avatar">
          <img
            appVaImage
            class="profile-image"
            [src]="item.profileImageUrl || item.defaultProfileImageUrl | ssl"
            [vaImageDefault]="item.defaultProfileImageUrl | ssl"
          />
          <img class="social-icon" [src]="item.serviceIcon | ssl" />
        </div>
        <div class="mention-option-selector-title">
          <mat-card-title class="title">
            {{ item.name }}
          </mat-card-title>
          <mat-card-subtitle class="subtitle">
            {{ item.formattedScreenName }}
          </mat-card-subtitle>
        </div>
      </div>
    </ng-template>
  </div>
  <span #textBox class="content-text-area-container">
    <mat-form-field appearance="outline" class="textarea" [ngClass]="{ 'show-popover': isVisible }">
      <textarea
        #textArea
        matInput
        [value]="postText$ | async"
        [placeholder]="customHint$ | async"
        [mention]="filteredOptions$ | async"
        [mentionConfig]="hashtagConfig"
        (searchTerm)="searchHashtags($event)"
        (input)="updatePostText($event.target.value)"
        [mentionListTemplate]="mentionTemplate"
        (itemSelected)="mentionItemSelected($event)"
        (mentionsTabSelected)="clickSocialMentionsTab($event)"
        [showMentionPrompt]="showMentionTemplate$ | async"
        [mentionPromptTemplate]="mentionPrompt"
        (opened)="openMentionsDropDown()"
        (closed)="closeMentionsDropDown()"
      ></textarea>
      <ng-container
        *ngIf="{
          proFlag: configService.proFlag$ | async,
          isLoading: generatingContent$$ | async,
          isProBrand: composerStore.isProBrand$ | async,
          showPopover: featuresService.instagramMultilocationEnabled$ | async,
          textValue: postText$ | async,
        } as data"
      >
        <div class="inline-char-hashtag-counter">
          <div [hidden]="(postText$ | async)?.length > 0">
            <app-ai-chatbot-button
              *ngIf="
                ((brandId ||
                  (configService.proFlag$ | async) === true ||
                  (configService.noUpgradePath$ | async) === false) &&
                  (featuresService.enableAiChatbot$ | async)) ||
                (featuresService.enableAiChatbotML$ | async)
              "
              [isLoading]="data.isLoading"
              [label]="getBtnLabel()"
              [isDisabled]="brandId && !data.isProBrand"
              [textValue]="data.textValue"
              [brandId]="brandId"
              [containerClassName]="aiChatBtnClassName"
              [placeholderLength]="(customHint$ | async)?.length"
              (chatbotTriggered)="aiChatbotSelected()"
            >
            </app-ai-chatbot-button>
          </div>
          <div class="section-title">
            <div
              class="post-length"
              *ngIf="twitterSelected$ | async"
              [ngClass]="{
                invalid: (tweetValid$ | async) === false,
              }"
              matTooltip="{{ 'COMPOSER.TWEET_LENGTH' | translate }}"
              matTooltipPosition="above"
            >
              {{ parseTweetLength(postText$ | async) }} /
              {{ 'COMPOSER.POST_VALIDATION.TWITTER_CHARS_LIMIT' | translate }}
            </div>

            <div
              class="post-length"
              *ngIf="
                (linkedinSelected$ | async) &&
                (twitterSelected$ | async) === false &&
                (gmbSelected$ | async) === false &&
                (instagramSelected$ | async) === false &&
                (tiktokSelected$ | async) === false
              "
              [ngClass]="{
                invalid: (linkedInValid$ | async) === false,
              }"
              matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'LinkedIn' } }}"
              matTooltipPosition="above"
            >
              {{ (postText$ | async).length }} /
              {{ 'COMPOSER.POST_VALIDATION.LINKEDIN_CHARS_LIMIT' | translate }}
            </div>

            <div
              class="post-length"
              *ngIf="(gmbSelected$ | async) && (twitterSelected$ | async) === false"
              [ngClass]="{
                invalid: (gmbValid$ | async) === false,
              }"
              matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'Google Business Profile' } }}"
              matTooltipPosition="above"
            >
              {{ (postText$ | async).length }} /
              {{ 'COMPOSER.POST_VALIDATION.GMB_CHARS_LIMIT' | translate }}
            </div>

            <div
              class="post-length"
              *ngIf="
                (instagramSelected$ | async) &&
                (tiktokSelected$ | async) === false &&
                (twitterSelected$ | async) === false &&
                (gmbSelected$ | async) === false
              "
              [ngClass]="{
                invalid: (instagramValid$ | async) === false,
              }"
              matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'Instagram' } }}"
              matTooltipPosition="above"
            >
              {{ (postText$ | async).length }} /
              {{ 'COMPOSER.POST_VALIDATION.INSTAGRAM_CHARS_LIMIT' | translate }}
            </div>

            <div
              class="post-length"
              *ngIf="instagramSelected$ | async"
              [ngClass]="{
                invalid: (igHashtagCountValid$ | async) === false,
              }"
              matTooltip="{{ 'COMPOSER.HASHTAG_COUNT' | translate }}"
              matTooltipPosition="above"
            >
              # {{ hashtagCount$ | async }}
            </div>

            <div
              class="post-length"
              *ngIf="
                (tiktokSelected$ | async) && (twitterSelected$ | async) === false && (gmbSelected$ | async) === false
              "
              [ngClass]="{
                invalid: (tiktokValid$ | async) === false,
              }"
              matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'TikTok' } }}"
              matTooltipPosition="above"
            >
              {{ (postText$ | async).length }} /
              {{ 'COMPOSER.POST_VALIDATION.TIKTOK_CHARS_LIMIT' | translate }}
            </div>
          </div>
        </div>
      </ng-container>
    </mat-form-field>
  </span>

  <button
    mat-flat-button
    color="primary"
    class="shorten-links"
    *ngIf="showShortenLinks$ | async"
    (click)="shortenLinks()"
  >
    <mat-icon>link</mat-icon>
    Shorten links
  </button>
  <app-disable-message
    class="disable-message-bar"
    [postValidation]="postTextValidator$ | async"
    [highlightError]="highlightErrors"
  ></app-disable-message>
</div>
