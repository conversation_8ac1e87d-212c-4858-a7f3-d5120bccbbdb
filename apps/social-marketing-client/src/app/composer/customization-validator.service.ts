import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable, combineLatest, of } from 'rxjs';
import { map, startWith, switchMap } from 'rxjs/operators';
import { SocialService } from './post';
import * as CONSTANTS from './constants';
import { FileType, UploadedFile } from './interfaces';
import { Customization } from '../core/post/customization';
import { ValidatorFailure } from './composer-validator.service';
import * as twitterText from 'twitter-text';
import { GmbOptions, GmbOptionsValidity } from './models/gmb-options';
import {
  dynamicContentInfoDoesNotExist,
  firstInvalidMessage,
  hasInvalidMedia,
  multipleMediaClickableBio,
  validateImagesUsedPreviously,
  validateImageTypes,
  validateMultipleGifs,
  validateMultipleVideos,
} from './shared-validators';
import UploadedImage from './models/uploaded-image';
import { ComposerStoreService } from './composer-store.service';
import { BrandsService } from './components/brands/brands.service';
import { MAX_LENGTH_TITLE, YoutubePostCustomization } from './models/youtube-options';
import { ConfigService } from '../core';
import { PostType } from '@vendasta/social-posts';
import { PostModeEnum } from './components/post-type/config';

@Injectable()
export class CustomizationValidatorService {
  private isProContext$ = combineLatest([
    this.configService.proFlag$.pipe(startWith(false)),
    this.composerStore.isProBrand$,
  ]).pipe(map(([proFlag, isProBrand]) => proFlag || isProBrand));

  VALIDATOR_FUNCS = {
    'missing-post-text': {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      func: ([serviceTypes, postText, isProContext, postType]) => {
        if (
          postText?.length == 0 &&
          (serviceTypes?.length === 0 ||
            serviceTypes.some(
              (network) => network !== SocialService.YOUTUBE_CHANNEL && network !== SocialService.TIKTOK_ACCOUNT,
            ))
        ) {
          if (
            serviceTypes.every((network) => network == SocialService.INSTAGRAM || network == SocialService.FACEBOOK)
          ) {
            return false;
          }
          return true;
        }
        return false;
      },
      message: ([isProContext]) =>
        isProContext
          ? this.translateService.stream('COMPOSER.POST_VALIDATION.NO_TEXT')
          : this.translateService.stream('COMPOSER.POST_VALIDATION.NO_TEXT_STANDARD_ACCOUNT'),
      class: 'post-text-error',
    },
    'missing-post-text-ml': {
      // Check to allow without text for Instagram Stories in multilocation
      func: ([serviceTypes, postText]) => {
        if (postText?.length == 0) {
          if (
            serviceTypes.every((network) => network == SocialService.INSTAGRAM || network == SocialService.FACEBOOK)
          ) {
            return false;
          }
          return true;
        }
        return false;
      },
      message: () =>
        this.isProContext$.pipe(
          switchMap((enabled) =>
            enabled
              ? this.translateService.stream('COMPOSER.POST_VALIDATION.NO_TEXT')
              : this.translateService.stream('COMPOSER.POST_VALIDATION.NO_TEXT_STANDARD_ACCOUNT'),
          ),
        ),
      class: 'post-text-error',
    },
    'twitter-invalid-post-text': {
      func: ([serviceTypes, tweetValid, tweetLength]) => {
        return serviceTypes.indexOf(SocialService.TWITTER) >= 0 && !tweetValid && tweetLength > 0;
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.TWITTER_LENGTH'),
      class: 'post-text-error',
    },
    'instagram-missing-image': {
      func: ([serviceTypes, uploadedMedia]) => {
        return serviceTypes.indexOf(SocialService.INSTAGRAM) >= 0 && uploadedMedia.length <= 0;
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.INSTAGRAM_IMAGE_NEEDED'),
      class: 'media-upload-error',
    },
    'multiple-media-clickable-bio': {
      func: ([serviceTypes, uploadedMedia, instagramOptions, postType]) => {
        return multipleMediaClickableBio(serviceTypes, uploadedMedia, instagramOptions, postType);
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.INSTAGRAM_CLICKABLE_BIO'),
      class: 'media-upload-error',
    },
    'facebook-media-validator': {
      func: ([serviceTypes, uploadedMedia, postText]) => {
        return (
          serviceTypes.indexOf(SocialService.FACEBOOK) >= 0 &&
          uploadedMedia.length <= 0 &&
          (!postText || postText.length === 0)
        );
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.FACEBOOK_IMAGE_NEEDED'),
      class: 'media-upload-error',
    },

    'linkedin-invalid-post-text': {
      func: ([serviceTypes, linkedinValid, linkedinLength]) => {
        return (
          (serviceTypes.indexOf(SocialService.LINKEDIN_COMPANY) >= 0 ||
            serviceTypes.indexOf(SocialService.LINKEDIN) >= 0) &&
          !linkedinValid &&
          linkedinLength > 0
        );
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.SERVICE_LENGTH', { serviceType: 'LinkedIn' }),
      class: 'post-text-error',
    },
    'instagram-invalid-post-text': {
      func: ([serviceTypes, instagramValid, instagramLength]) => {
        return serviceTypes.indexOf(SocialService.INSTAGRAM) >= 0 && !instagramValid && instagramLength > 0;
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.SERVICE_LENGTH', { serviceType: 'Instagram' }),
      class: 'post-text-error',
    },
    'instagram-hashtag-limit': {
      func: ([serviceTypes, igHashtagCountValid]) => {
        return serviceTypes.indexOf(SocialService.INSTAGRAM) >= 0 && !igHashtagCountValid;
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.HASHTAG_LIMIT', {
          hashtagLimit: CONSTANTS.SERVICE_MAX_HASHTAGS.IG_USER,
        }),
      class: 'post-text-error',
    },
    'gmb-invalid-post-text': {
      func: ([serviceTypes, gmbValid, gmbLength]) => {
        return (
          (serviceTypes.indexOf(SocialService.GOOGLE_MY_BUSINESS) >= 0 ||
            serviceTypes.indexOf(SocialService.GMB) >= 0) &&
          !gmbValid &&
          gmbLength > 0
        );
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.SERVICE_LENGTH', { serviceType: 'Google My Business' }),
      class: 'post-text-error',
    },
    'gmb-invalid-options': {
      func: ([serviceTypes, gmbOptions, gmbOptionsValidity]) => {
        return (
          (serviceTypes.indexOf(SocialService.GOOGLE_MY_BUSINESS) >= 0 ||
            serviceTypes.indexOf(SocialService.GMB) >= 0) &&
          gmbOptions &&
          gmbOptions.makeEvent &&
          !gmbOptionsValidity.eventValid
        );
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.INVALID_GMB_OPTIONS'),
      class: 'gmb-option-error',
    },
    'gmb-invalid-cta': {
      func: ([serviceTypes, gmbOptions, gmbOptionsValidity]) => {
        return (
          (serviceTypes.indexOf(SocialService.GOOGLE_MY_BUSINESS) >= 0 ||
            serviceTypes.indexOf(SocialService.GMB) >= 0) &&
          gmbOptions &&
          gmbOptions.addCta &&
          !gmbOptionsValidity.ctaValid
        );
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.INVALID_GMB_CTA'),
      class: 'gmb-option-error',
    },
    'media-multiple-file-types': {
      func: ([uploadedMediaObjects, serviceTypes]) => validateImageTypes(uploadedMediaObjects, serviceTypes),
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.MULTIPLE_IMAGE_TYPES'),
      class: 'media-upload-error',
    },
    'media-multiple-gifs': {
      func: ([uploadedMediaObjects]) => validateMultipleGifs(uploadedMediaObjects),
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.MULTIPLE_GIFS'),
      class: 'media-upload-error',
    },
    'media-gifs-on-instagram': {
      func: ([uploadedMediaObjects, serviceTypes]) => {
        return (
          serviceTypes.indexOf(SocialService.INSTAGRAM) >= 0 &&
          uploadedMediaObjects.filter((media) => media.fileType === FileType.GIF).length > 0
        );
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.GIFS_ON_INSTAGRAM'),
      class: 'instagram-gifs-warning',
    },
    'media-multiple-videos': {
      func: ([uploadedMediaObjects, serviceTypes, postType]) =>
        validateMultipleVideos(uploadedMediaObjects, serviceTypes, postType),
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.MULTIPLE_VIDEOS'),
      class: 'media-upload-error',
    },
    'media-single-image-services': {
      func: ([uploadedMediaObjects, serviceTypes, postType]) =>
        this.validateMultiImagePostService(uploadedMediaObjects, serviceTypes, postType),
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.SINGLE_IMAGE_SERVICES'),
      class: 'media-upload-error',
    },
    'media-above-twitter-limit': {
      func: ([uploadedMediaObjects, serviceTypes]) => {
        return (
          serviceTypes.indexOf(SocialService.TWITTER) >= 0 &&
          uploadedMediaObjects.length > CONSTANTS.TWITTER_IMAGE_LIMIT
        );
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.ABOVE_IMAGE_LIMIT', {
          imageCount: CONSTANTS.TWITTER_IMAGE_LIMIT,
          serviceType: 'Twitter',
        }),
      class: 'media-upload-error',
    },
    'media-above-linkedin-limit': {
      func: ([uploadedMediaObjects, serviceTypes]) => {
        return (
          (serviceTypes.indexOf(SocialService.LINKEDIN) >= 0 ||
            serviceTypes.indexOf(SocialService.LINKEDIN_COMPANY) >= 0) &&
          uploadedMediaObjects.length > CONSTANTS.LINKEDIN_IMAGE_LIMIT
        );
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.ABOVE_IMAGE_LIMIT', {
          imageCount: CONSTANTS.LINKEDIN_IMAGE_LIMIT,
          serviceType: 'LinkedIn',
        }),
      class: 'media-upload-error',
    },
    'media-images-previously-used': {
      func: ([uploadedMediaObjects]) => validateImagesUsedPreviously(uploadedMediaObjects),
      message: (uploadedMediaObjects) => this.imagesUsedPreviouslyMessage(uploadedMediaObjects),
      class: 'images-used-previously-warning',
    },
    'invalid-story-image': {
      func: ([uploadedMediaObjects, postType]) => {
        if (postType === PostType.POST_TYPE_STORIES) {
          return !uploadedMediaObjects.every((um) => {
            return (
              um.naturalWidth === CONSTANTS.STORY_MAX_IMAGE_WIDTH.IG_USER &&
              um.naturalHeight === CONSTANTS.STORY_MAX_IMAGE_HIGHT.IG_USER
            );
          });
        } else {
          return false;
        }
      },
      message: () => this.translateService.stream('COMPOSER.UPLOAD_MEDIA.INVALID_STORY_IMAGE'),
      class: 'invalid-story-image-warning',
    },
    'invalid-image': {
      // Validator that colllects the errors from validate-image and validate-video
      func: ([uploadedMediaObjects, serviceTypes, postType]) => {
        const services = this.filterServicebyPostType(serviceTypes, postType);
        return hasInvalidMedia(uploadedMediaObjects, services, FileType.IMAGE);
      },
      message: (mediaObjects, serviceTypes) => {
        const mediaLimits = firstInvalidMessage(mediaObjects, serviceTypes, FileType.IMAGE);
        return mediaLimits ? this.translateService.stream(mediaLimits.message, mediaLimits.values) : of('');
      },
      class: 'media-upload-error',
    },
    'invalid-video': {
      func: ([uploadedMediaObjects, serviceTypes]) => {
        return hasInvalidMedia(uploadedMediaObjects, serviceTypes, FileType.VIDEO);
      },
      message: (mediaObjects, serviceTypes) => {
        const mediaLimits = firstInvalidMessage(mediaObjects, serviceTypes, FileType.VIDEO);
        return mediaLimits ? this.translateService.stream(mediaLimits.message, mediaLimits.values) : of('');
      },
      class: 'media-upload-error',
    },
    'video-unsupported': {
      func: ([serviceTypes, mediaObjects]) => {
        return (
          serviceTypes.findIndex((st) => st === SocialService.GMB || st === SocialService.GOOGLE_MY_BUSINESS) >= 0 &&
          mediaObjects.filter((media) => media.fileType === FileType.VIDEO).length > 0
        );
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.VIDEO_POSTING_UNSUPPORTED'),
      class: 'service-selector-error',
    },
    'facebook-video-unsupported': {
      func: ([serviceTypes, mediaObjects]) => {
        return (
          serviceTypes.findIndex((st) => st !== SocialService.FACEBOOK) >= 0 &&
          mediaObjects.filter((media) => media.fileType === FileType.VIDEO).length > 0
        );
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.POST_VIDEO_OTHER_THAN_FACEBOOK'),
      class: 'service-selector-error',
    },
    'carousel-above-instagram-limit': {
      func: ([uploadedMediaObjects, serviceTypes]) => {
        return (
          serviceTypes.indexOf(SocialService.INSTAGRAM) >= 0 &&
          uploadedMediaObjects.length > CONSTANTS.INTAGRAM_CAROUSEL_LIMIT
        );
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.ABOVE_IMAGE_LIMIT', {
          imageCount: CONSTANTS.INTAGRAM_CAROUSEL_LIMIT,
          serviceType: 'Instagram',
        }),
      class: 'media-upload-error',
    },
    'media-above-limit': {
      func: ([uploadedMediaObjects]) => {
        return uploadedMediaObjects.length > CONSTANTS.DEFAULT_MULTI_IMAGE_LIMIT;
      },
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.ABOVE_MEDIA_LIMIT', {
          imageCount: CONSTANTS.DEFAULT_MULTI_IMAGE_LIMIT,
        }),
      class: 'media-upload-error',
    },
    //Multi-location checks
    'service-minimum-not-reached': {
      func: ([numServicesSelected]) => {
        return numServicesSelected === 0;
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.BRANDS_SERVICE_LIMIT_MIN'),
      class: 'brands-service-limit-error',
    },
    'dynamic-content-does-not-exist': {
      func: ([postText, theMap]) => {
        return dynamicContentInfoDoesNotExist(postText, theMap);
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.DYNAMIC_CONTENT_DOES_NOT_EXIST'),
      class: 'post-text-error',
    },
    // Post structure checks
    'empty-date': {
      func: ([scheduledDate, postmode]) => postmode === PostModeEnum.SCHEDULED && scheduledDate === null,
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.EMPTY_DATE'),
      class: 'scheduler-error',
    },
    'empty-time': {
      func: ([scheduleTime, postmode]) => postmode === PostModeEnum.SCHEDULED && scheduleTime === null,
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.EMPTY_TIME'),
      class: 'scheduler-error',
    },
    'invalid-time': {
      func: ([scheduledDate]) => scheduledDate && scheduledDate < new Date(),
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.INVALID_TIME'),
      class: 'scheduler-error',
    },
    'youtube-invalid-title': {
      func: ([serviceTypes, title]) =>
        (title?.length <= 0 || title?.length > MAX_LENGTH_TITLE) &&
        serviceTypes.indexOf(SocialService.YOUTUBE_CHANNEL) >= 0,
      message: () =>
        this.translateService.stream('COMPOSER.POST_VALIDATION.INVALID_YOUTUBE_TITLE', {
          maxLengthTitle: MAX_LENGTH_TITLE,
        }),
      class: 'youtube-title-error',
    },
    'youtube-require-one-video': {
      func: ([serviceTypes, mediaObjects]) =>
        mediaObjects?.filter((media) => media.fileType === FileType.VIDEO).length <= 0 &&
        serviceTypes.indexOf(SocialService.YOUTUBE_CHANNEL) >= 0,
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.ONE_VIDEO_REQUIRED_YOUTUBE'),
      class: 'media-upload-error',
    },
    'tiktok-require-one-video': {
      func: ([serviceTypes, mediaObjects]) =>
        mediaObjects?.filter((media) => media.fileType === FileType.VIDEO).length <= 0 &&
        serviceTypes.indexOf(SocialService.TIKTOK_ACCOUNT) >= 0,
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.ONE_VIDEO_REQUIRED_TIKTOK'),
      class: 'media-upload-error',
    },
    'tiktok-invalid-post-text': {
      func: ([serviceTypes, tiktokValid, tiktokLength]) => {
        return serviceTypes.indexOf(SocialService.TIKTOK_ACCOUNT) >= 0 && !tiktokValid && tiktokLength > 0;
      },
      message: () => this.translateService.stream('COMPOSER.POST_VALIDATION.SERVICE_LENGTH', { serviceType: 'TikTok' }),
      class: 'post-text-error',
    },
  };

  //Get the arguments for the validator functions
  private argumentMappings: { [key: string]: (values: any) => any[] } = {
    'missing-post-text': (values) => [values.serviceTypes, values.postText, values.isProContext, values.postType],
    'missing-post-text-ml': (values) => [values.serviceTypes, values.postText],
    'twitter-invalid-post-text': (values) => [values.serviceTypes, values.tweet.valid, values.tweet.weightedLength],
    'instagram-missing-image': (values) => [values.serviceTypes, values.uploadedMediaObjects],
    'facebook-media-validator': (values) => [values.serviceTypes, values.uploadedMediaObjects, values.postText],
    'multiple-media-clickable-bio': (values) => [
      values.serviceTypes,
      values.uploadedMediaObjects,
      values.instagramOptions,
      values.postType,
    ],
    'linkedin-invalid-post-text': (values) => [values.serviceTypes, values.linkedInValid, values.postText.length],
    'instagram-invalid-post-text': (values) => [values.serviceTypes, values.instagramValid, values.postText.length],
    'instagram-hashtag-limit': (values) => [values.serviceTypes, values.igHashtagCountValid],
    'gmb-invalid-post-text': (values) => [values.serviceTypes, values.gmbValid, values.postText.length],
    'gmb-invalid-options': (values) => [values.serviceTypes, values.gmbOptions, values.gmbOptionsValidity],
    'gmb-invalid-cta': (values) => [values.serviceTypes, values.gmbOptions, values.gmbOptionsValidity],
    'media-multiple-file-types': (values) => [values.uploadedMediaObjects, values.serviceTypes],
    'media-multiple-gifs': (values) => [values.uploadedMediaObjects],
    'media-gifs-on-instagram': (values) => [values.uploadedMediaObjects, values.serviceTypes],
    'media-multiple-videos': (values) => [values.uploadedMediaObjects, values.serviceTypes, values.postType],
    'media-single-image-services': (values) => [values.uploadedMediaObjects, values.serviceTypes, values.postType],
    'media-above-twitter-limit': (values) => [values.uploadedMediaObjects, values.serviceTypes],
    'media-above-linkedin-limit': (values) => [values.uploadedMediaObjects, values.serviceTypes],
    'media-images-previously-used': (values) => [values.uploadedMediaObjects],
    'invalid-story-image': (values) => [values.uploadedMediaObjects, values.postType],
    'invalid-image': (values) => [values.uploadedMediaObjects, values.serviceTypes, values.postType],
    'invalid-video': (values) => [values.uploadedMediaObjects, values.serviceTypes],
    'video-unsupported': (values) => [values.serviceTypes, values.uploadedMediaObjects],
    'facebook-video-unsupported': (values) => [values.serviceTypes, values.uploadedMediaObjects],
    'carousel-above-instagram-limit': (values) => [values.uploadedMediaObjects, values.serviceTypes],
    'media-above-limit': (values) => [values.uploadedMediaObjects],
    'service-minimum-not-reached': (values) => [values.numServicesSelected],
    'dynamic-content-does-not-exist': (values) => [values.postText, values.brandAgids],
    'invalid-time': (values) => [values.scheduledDate],
    'empty-date': (values) => [values.scheduledDate, values.postMode],
    'empty-time': (values) => [values.scheduleTime, values.postMode],
  };

  constructor(
    private translateService: TranslateService,
    private composerStore: ComposerStoreService,
    private brandsService: BrandsService,
    private configService: ConfigService,
  ) {}

  //TODO convert to the same approach from failingMultiLocationValidators$
  failingValidators$(customization: Customization): Observable<ValidatorFailure[]> {
    return combineLatest([
      customization.serviceTypes$,
      customization.postText$,
      customization.postText$.pipe(map((postText) => twitterText.parseTweet(postText))),
      customization.linkedInValid$,
      customization.gmbValid$,
      customization.instagramValid$,
      customization.igHashtagCountValid$,
      customization.uploadedMediaObjects$,
      customization.gmbOptions$,
      customization.gmbOptionsValidity$,
      customization.youtubePostCustomization$,
      customization.postType$,
      customization.tiktokValid$,
      this.isProContext$,
      customization.instagramOptions$,
    ]).pipe(
      map(
        ([
          serviceTypes,
          postText,
          tweet,
          linkedInValid,
          gmbValid,
          instagramValid,
          igHashtagCountValid,
          uploadedMediaObjects,
          gmbOptions,
          gmbOptionsValidity,
          youtubePostCustomization,
          postType,
          tiktokValid,
          isProContext,
          instagramOptions,
        ]: [
          string[],
          string,
          { valid: boolean; weightedLength: number },
          boolean,
          boolean,
          boolean,
          boolean,
          UploadedFile[],
          GmbOptions,
          GmbOptionsValidity,
          YoutubePostCustomization,
          PostType,
          boolean,
          boolean,
          string,
        ]) => {
          const failingValidators: ValidatorFailure[] = [];
          if (this.VALIDATOR_FUNCS['missing-post-text'].func([serviceTypes, postText, isProContext, postType])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['missing-post-text'].message([isProContext]),
              type: 'error',
              class: this.VALIDATOR_FUNCS['missing-post-text'].class,
            });
          }
          if (
            this.VALIDATOR_FUNCS['twitter-invalid-post-text'].func([serviceTypes, tweet.valid, tweet.weightedLength])
          ) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['twitter-invalid-post-text'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['twitter-invalid-post-text'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['tiktok-invalid-post-text'].func([serviceTypes, tiktokValid, postText.length])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['tiktok-invalid-post-text'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['tiktok-invalid-post-text'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['instagram-missing-image'].func([serviceTypes, uploadedMediaObjects])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['instagram-missing-image'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['instagram-missing-image'].class,
            });
          }
          if (
            this.VALIDATOR_FUNCS['facebook-media-validator'].func([serviceTypes, uploadedMediaObjects, postText.length])
          ) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['facebook-media-validator'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['facebook-media-validator'].class,
            });
          }
          if (
            this.VALIDATOR_FUNCS['multiple-media-clickable-bio'].func([
              serviceTypes,
              uploadedMediaObjects,
              instagramOptions,
              postType,
            ])
          ) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['multiple-media-clickable-bio'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['multiple-media-clickable-bio'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['linkedin-invalid-post-text'].func([serviceTypes, linkedInValid, postText.length])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['linkedin-invalid-post-text'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['linkedin-invalid-post-text'].class,
            });
          }
          if (
            this.VALIDATOR_FUNCS['instagram-invalid-post-text'].func([serviceTypes, instagramValid, postText.length])
          ) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['instagram-invalid-post-text'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['instagram-invalid-post-text'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['instagram-hashtag-limit'].func([serviceTypes, igHashtagCountValid])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['instagram-hashtag-limit'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['instagram-hashtag-limit'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['gmb-invalid-post-text'].func([serviceTypes, gmbValid, postText.length])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['gmb-invalid-post-text'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['gmb-invalid-post-text'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['gmb-invalid-options'].func([serviceTypes, gmbOptions, gmbOptionsValidity])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['gmb-invalid-options'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['gmb-invalid-options'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['gmb-invalid-cta'].func([serviceTypes, gmbOptions, gmbOptionsValidity])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['gmb-invalid-cta'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['gmb-invalid-cta'].class,
            });
          }
          //Multi-type error
          if (this.VALIDATOR_FUNCS['media-multiple-file-types'].func([uploadedMediaObjects, serviceTypes])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-multiple-file-types'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['media-multiple-file-types'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['media-multiple-gifs'].func([uploadedMediaObjects])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-multiple-gifs'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['media-multiple-gifs'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['media-gifs-on-instagram'].func([uploadedMediaObjects, serviceTypes])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-gifs-on-instagram'].message(),
              type: 'warning',
              class: this.VALIDATOR_FUNCS['media-gifs-on-instagram'].class,
            });
          }
          //Multiples Videos flag
          if (this.VALIDATOR_FUNCS['media-multiple-videos'].func([uploadedMediaObjects, serviceTypes, postType])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-multiple-videos'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['media-multiple-videos'].class,
            });
          }
          if (
            this.VALIDATOR_FUNCS['media-single-image-services'].func([uploadedMediaObjects, serviceTypes, postType])
          ) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-single-image-services'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['media-single-image-services'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['media-above-twitter-limit'].func([uploadedMediaObjects, serviceTypes])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-above-twitter-limit'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['media-above-twitter-limit'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['media-above-linkedin-limit'].func([uploadedMediaObjects, serviceTypes])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-above-linkedin-limit'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['media-above-linkedin-limit'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['carousel-above-instagram-limit'].func([uploadedMediaObjects, serviceTypes])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['carousel-above-instagram-limit'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['carousel-above-instagram-limit'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['media-above-limit'].func([uploadedMediaObjects])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-above-limit'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['media-above-limit'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['media-images-previously-used'].func([uploadedMediaObjects])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['media-images-previously-used'].message(uploadedMediaObjects),
              type: 'warning',
              class: this.VALIDATOR_FUNCS['media-images-previously-used'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['invalid-story-image'].func([uploadedMediaObjects, postType])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['invalid-story-image'].message(),
              type: 'warning',
              class: this.VALIDATOR_FUNCS['invalid-story-image'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['invalid-image'].func([uploadedMediaObjects, serviceTypes, postType])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['invalid-image'].message(uploadedMediaObjects, serviceTypes),
              type: 'error',
              class: this.VALIDATOR_FUNCS['invalid-image'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['invalid-video'].func([uploadedMediaObjects, serviceTypes])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['invalid-video'].message(uploadedMediaObjects, serviceTypes),
              type: 'error',
              class: this.VALIDATOR_FUNCS['invalid-video'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['video-unsupported'].func([serviceTypes, uploadedMediaObjects])) {
            failingValidators.push({
              message: this.translateService.stream('COMPOSER.POST_VALIDATION.VIDEO_POSTING_FLAG_ON'),
              type: 'error',
              class: this.VALIDATOR_FUNCS['video-unsupported'].class,
            });
          }
          // Youtube Validations
          if (this.VALIDATOR_FUNCS['youtube-invalid-title'].func([serviceTypes, youtubePostCustomization?.title])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['youtube-invalid-title'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['youtube-invalid-title'].class,
            });
          }
          if (this.VALIDATOR_FUNCS['youtube-require-one-video'].func([serviceTypes, uploadedMediaObjects])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['youtube-require-one-video'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['youtube-require-one-video'].class,
            });
          }
          // TikTok
          if (this.VALIDATOR_FUNCS['tiktok-require-one-video'].func([serviceTypes, uploadedMediaObjects])) {
            failingValidators.push({
              message: this.VALIDATOR_FUNCS['tiktok-require-one-video'].message(),
              type: 'error',
              class: this.VALIDATOR_FUNCS['tiktok-require-one-video'].class,
            });
          }
          return failingValidators;
        },
      ),
      startWith([]),
    );
  }

  failingMultiLocationValidators$(customization: Customization): Observable<ValidatorFailure[]> {
    const numAvailableProfilesAcrossNetworks$: Observable<number> = combineLatest([
      this.brandsService.numFBPages$,
      this.brandsService.numIGPages$,
      this.brandsService.numGMBPages$,
      this.brandsService.numLIPages$,
    ]).pipe(
      map(([numFBPages, numIGPages, numGMBPages, numLIPages]) => numFBPages + numIGPages + numGMBPages + numLIPages),
    );
    return combineLatest([
      numAvailableProfilesAcrossNetworks$,
      customization.postText$,
      customization.uploadedMediaObjects$,
      this.composerStore.rootCustomization.scheduledDate$,
      this.composerStore.brandAgids$,
      this.composerStore.mlServiceTypes$,
      customization.gmbValid$,
      customization.gmbOptions$,
      customization.gmbOptionsValidity$,
      customization.instagramValid$,
      customization.igHashtagCountValid$,
      customization.postType$,
      customization.linkedInValid$,
      this.composerStore.postModeSelected$,
      this.composerStore.rootCustomization.scheduledDate$,
    ]).pipe(
      map(
        ([
          numServicesSelected,
          postText,
          uploadedMediaObjects,
          scheduledDate,
          brandAgids,
          mlServiceTypes,
          gmbValid,
          gmbOptions,
          gmbOptionsValidity,
          instagramValid,
          igHashtagCountValid,
          postType,
          linkedInValid,
          postMode,
          scheduleTime,
        ]) => {
          const failingValidators: ValidatorFailure[] = [];

          const objectValidator = {
            numServicesSelected,
            postText,
            uploadedMediaObjects,
            scheduledDate,
            brandAgids,
            serviceTypes: mlServiceTypes || [],
            gmbValid,
            gmbOptions,
            gmbOptionsValidity,
            instagramValid,
            igHashtagCountValid,
            postType,
            linkedInValid,
            postMode,
            scheduleTime,
          };

          const validationKeys = [
            'service-minimum-not-reached',
            'empty-date',
            'empty-time',
            'missing-post-text-ml',
            'invalid-time',
            'media-multiple-file-types',
            'media-multiple-gifs',
            'media-multiple-videos',
            'invalid-image',
            'invalid-video',
            'dynamic-content-does-not-exist',
            'media-above-limit',
            'gmb-invalid-post-text',
            'gmb-invalid-options',
            'gmb-invalid-cta',
            'media-single-image-services',
            'instagram-missing-image',
            'facebook-media-validator',
            'multiple-media-clickable-bio',
            'instagram-invalid-post-text',
            'instagram-hashtag-limit',
            'media-gifs-on-instagram',
            'carousel-above-instagram-limit',
            'media-above-linkedin-limit',
            'linkedin-invalid-post-text',
          ];

          validationKeys.forEach((key) => {
            const validatorFunc = this.VALIDATOR_FUNCS[key];
            const args = [this.argumentMappings[key](objectValidator)];

            if (validatorFunc.func(...args)) {
              failingValidators.push({
                message: validatorFunc.message(...args),
                type: validatorFunc.class.includes('warning') ? 'warning' : 'error',
                class: validatorFunc.class,
              });
            }
          });

          return failingValidators;
        },
      ),
      startWith([]),
    );
  }

  validateMultiImagePostService(mediaObjects: UploadedFile[], serviceTypes: SocialService[], postType): boolean {
    if (mediaObjects.length <= 1) {
      return false;
    }
    return !!serviceTypes.find((service) => {
      const services = [
        SocialService.GOOGLE_MY_BUSINESS,
        SocialService.GMB,
        SocialService.PINTEREST,
        SocialService.TIKTOK_ACCOUNT,
        postType === PostType.POST_TYPE_STORIES ? SocialService.INSTAGRAM : null,
      ];
      return services.indexOf(service) >= 0;
    });
  }

  filterServicebyPostType(serviceTypes: SocialService[], postType: PostType): SocialService[] {
    const posTypeFilter = {
      [PostType.POST_TYPE_STORIES]: [SocialService.INSTAGRAM],
    };
    return serviceTypes.filter((service) => {
      const serviceFilter = posTypeFilter[postType] || [];
      return serviceFilter?.indexOf(service) === -1;
    });
  }

  imagesUsedPreviouslyMessage(mediaObjects): Observable<string> {
    return this.translateService.stream('COMPOSER.UPLOAD_MEDIA').pipe(
      map((translationDict) => {
        const images = mediaObjects.filter((object) => object.fileType === FileType.IMAGE);
        if (!images.length) {
          return '';
        }
        const plural = images.filter((image) => !!image.lastPostedDate).length > 1;
        let string = translationDict['YOUR'];

        const indices = images.map((image, i) => (image.lastPostedDate ? i : -1)).filter((i) => i >= 0);
        switch (indices.toString()) {
          case '0':
            string += ` ${translationDict['FIRST']} `;
            break;
          case '1':
            string += ` ${translationDict['SECOND']} `;
            break;
          case '2':
            string += ` ${translationDict['THIRD']} `;
            break;
          case '0,1':
            string += ` ${translationDict['FIRST']} ${translationDict['AND']} ${translationDict['SECOND']} `;
            break;
          case '0,2':
            string += ` ${translationDict['FIRST']} ${translationDict['AND']} ${translationDict['THIRD']} `;
            break;
          case '1,2':
            string += ` ${translationDict['SECOND']} ${translationDict['AND']} ${translationDict['THIRD']} `;
            break;
          case '0,1,2':
            string += ` ${translationDict['FIRST']}, ${translationDict['SECOND']}`;
            string += ` ${translationDict['AND']} ${translationDict['THIRD']} `;
            break;
        }

        string += plural ? translationDict['WERE_USED'] : translationDict['WAS_USED'];
        const uploadedImages = images.map((i) => new UploadedImage(i));
        switch (indices.toString()) {
          case '0':
            string += ` ${uploadedImages[0].localizedLastPostedDate()}`;
            break;
          case '1':
            string += ` ${uploadedImages[1].localizedLastPostedDate()}`;
            break;
          case '2':
            string += ` ${uploadedImages[2].localizedLastPostedDate()}`;
            break;
          case '0,1':
            string += ` ${uploadedImages[0].localizedLastPostedDate()} ${translationDict['AND']}`;
            string += ` ${uploadedImages[1].localizedLastPostedDate()} `;
            break;
          case '0,2':
            string += ` ${uploadedImages[0].localizedLastPostedDate()} ${translationDict['AND']}`;
            string += ` ${uploadedImages[2].localizedLastPostedDate()} `;
            break;
          case '1,2':
            string += ` ${uploadedImages[1].localizedLastPostedDate()} ${translationDict['AND']}`;
            string += ` ${uploadedImages[2].localizedLastPostedDate()} `;
            break;
          case '0,1,2':
            string += ` ${uploadedImages[0].localizedLastPostedDate()}, ${uploadedImages[1].localizedLastPostedDate()}`;
            string += ` ${translationDict['AND']} ${uploadedImages[2].localizedLastPostedDate()} `;
            break;
        }
        if (plural) {
          string += translationDict['RESPECTIVELY'];
        }
        string += '. ' + translationDict['DIFFERENT_IMAGES'];
        return string;
      }),
    );
  }
}
