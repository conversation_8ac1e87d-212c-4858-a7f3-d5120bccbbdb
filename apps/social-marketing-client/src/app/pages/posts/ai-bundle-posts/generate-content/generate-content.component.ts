import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { MessageLength } from '@vendasta/social-posts';
import { ToneMenu } from '../../../../composer/components/ai-menu/ai-menu-settings';
import { POSTHOG_KEYS } from '../../../../composer/constants';
import { ConfigService } from '../../../../core';
import { GenerateContentSetup, SocialConnection } from '../model';
import { GenerateType } from '../../../../composer/components/suggestions/generate-type';
import { customTopicValidator } from '../../../../composer/shared-validators';
import { MatDialog } from '@angular/material/dialog';
import { CommonInstructionsEditModalComponent } from '../common-instructions-edit-modal/common-instructions-edit-modal.component';
import { CustomInstructionsService } from '../../../../core/ai/custom-instructions.service';
import { Subject, take } from 'rxjs';
import { SnackBarService } from '../../../../shared/snack-bar/snack-bar.service';
import { MatSnackBarConfig } from '@angular/material/snack-bar';
import { takeUntil } from 'rxjs/operators';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';

@Component({
  selector: 'app-generate-content',
  imports: [
    CommonModule,
    MatCardModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    TranslateModule,
    RouterModule,
    GalaxyAlertModule,
  ],
  templateUrl: './generate-content.component.html',
  styleUrls: ['./generate-content.component.scss'],
})
export class GenerateContentComponent implements OnInit, OnDestroy {
  fb: FormBuilder = inject(FormBuilder);
  translateService: TranslateService = inject(TranslateService);
  configService: ConfigService = inject(ConfigService);
  aiInstructionService = inject(CustomInstructionsService);
  snackbarService = inject(SnackBarService);
  cInstructions = '';
  blogInstructions = '';
  private destroy$ = new Subject<void>();
  private snackConfig: MatSnackBarConfig = { horizontalPosition: 'center', duration: 5000 };

  @Input({ required: true }) networks: SocialConnection[] = [];
  @Output() connectAccountEvent = new EventEmitter();
  @Input() setup: GenerateContentSetup;
  @Output() next: EventEmitter<GenerateContentSetup> = new EventEmitter<GenerateContentSetup>();
  toneOptions = [{ value: '', label: this.translateService.instant('COMPOSER.SUGGESTIONS.DEFAULT') }].concat(
    ToneMenu.map((tone) => ({
      value: tone.name,
      label: `${tone.icon} ${this.translateService.instant(tone.translationKey)}`,
    })),
  );
  contentLengthOptions = [
    { value: MessageLength.SHORT_FORM, label: this.translateService.instant('COMPOSER.SUGGESTIONS.SHORT-FORM') },
    { value: MessageLength.LONG_FORM, label: this.translateService.instant('COMPOSER.SUGGESTIONS.LONG-FORM') },
  ];
  generateTypeOptions = [
    {
      value: GenerateType.CONTENT_AND_IMAGES,
      label: this.translateService.instant('COMPOSER.SUGGESTIONS.GENERATE_TEXT_AND_IMAGES'),
    },
    {
      value: GenerateType.CONTENT,
      label: this.translateService.instant('COMPOSER.SUGGESTIONS.GENERATE_TEXT'),
    },
  ];
  numberOfPostsOptions = [3, 6, 9, 12];
  formGroup: FormGroup;
  showImageTopic = false;

  constructor(
    private productAnalyticsService: ProductAnalyticsService,
    private dialog: MatDialog,
    private router: Router,
  ) {}

  get selectedNetworks(): SocialConnection[] {
    return this.formGroup.value.networks;
  }

  onSubmit() {
    if (this.formGroup.valid) {
      this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BUNDLE_AI_SAVE_GENERATE, 'user', 'click', 0);
      this.next.emit({
        topic: this.formGroup.value.topic,
        imageTopic: this.formGroup.value.imageTopic,
        useAiInstructions: true, //Common instructions are always used
        networks: this.formGroup.value.networks,
        numberOfPosts: this.formGroup.value.numberOfPosts,
        tone: this.formGroup.value.tone,
        contentLength: this.formGroup.value.contentLength,
        contentType: this.formGroup.value.contentType,
      } as GenerateContentSetup);
    }
  }

  ngOnInit(): void {
    this.formGroup = this.fb.group({
      topic: this.fb.control<string>(this.setup.topic || '', [Validators.required, customTopicValidator()]),
      imageTopic: this.fb.control<string>(this.setup.imageTopic || '', [customTopicValidator()]),

      useAiInstructions: true, //Common instructions are always used
      networks: this.fb.control<SocialConnection[]>(this.setup.networks || [], [Validators.required]),
      numberOfPosts: this.fb.control<number>(this.setup.numberOfPosts || 3),
      tone: this.fb.control<string>(this.setup.tone),
      contentLength: this.fb.control<number>(this.setup.contentLength || this.contentLengthOptions[0].value),
      contentType: this.fb.control<string>(this.setup.contentType || GenerateType.CONTENT_AND_IMAGES),
    });

    this.setupConditionalValidators();

    this.aiInstructionService.customInstructions$.pipe(takeUntil(this.destroy$)).subscribe({
      next: (instructions) => {
        this.cInstructions = instructions;
      },
      error: () =>
        this.snackbarService.errorSnack(
          this.translateService.instant('SETTINGS.AI_SETTINGS.COMMON_INSTRUCTIONS_LOAD_ERROR'),
          null,
          this.snackConfig,
        ),
    });

    this.aiInstructionService.blogInstructions$.pipe(takeUntil(this.destroy$)).subscribe({
      next: (instructions) => {
        this.blogInstructions = instructions;
      },
    });
  }

  commonInstructionsDialog(event: Event) {
    event.preventDefault();
    const dialogRef = this.dialog.open(CommonInstructionsEditModalComponent, {
      data: this.cInstructions,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.aiInstructionService
          .saveInstructions(result.instructions, this.blogInstructions)
          .pipe(take(1))
          .subscribe({
            next: () => {
              this.snackbarService.successSnack(
                this.translateService.instant('SETTINGS.AI_SETTINGS.COMMON_INSTRUCTIONS_SAVE_SUCCESS'),
                null,
                this.snackConfig,
              ),
                (this.cInstructions = result.instructions);
            },
            error: () =>
              this.snackbarService.errorSnack(
                this.translateService.instant('SETTINGS.AI_SETTINGS.COMMON_INSTRUCTIONS_SAVE_ERROR'),
                null,
                this.snackConfig,
              ),
          });
      }
    });
  }

  setupConditionalValidators() {
    this.formGroup.get('contentType').valueChanges.subscribe((selectedValue) => {
      this.applyValidatorsBasedOnSelectValue(selectedValue);
    });

    this.applyValidatorsBasedOnSelectValue(this.formGroup.get('contentType').value);
  }

  applyValidatorsBasedOnSelectValue(selectedValue: string) {
    this.showImageTopic = selectedValue === GenerateType.CONTENT_AND_IMAGES;
    const inputControl = this.formGroup.get('imageTopic');
    if (selectedValue === GenerateType.CONTENT_AND_IMAGES) {
      inputControl.setValidators([Validators.required]);
    } else {
      inputControl.clearValidators();
    }
    inputControl.updateValueAndValidity();
  }
  connectAccount() {
    const url = this.router.createUrlTree(['/account', this.configService.accountGroupId, 'settings']);
    this.router.navigateByUrl(url);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  get topic(): UntypedFormControl {
    return this.formGroup.get('topic') as UntypedFormControl;
  }
}
