import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { MAX_ALLOWED_POSTS, POSTHOG_KEYS } from '../../../../composer/constants';
import { SnackBarService } from '../../../../shared/snack-bar/snack-bar.service';
import {
  AI_BUNDLE_POSTS_STEPS,
  BundleSocialPosts,
  ErrorCode,
  GenerateContentSetup,
  TechnicalPromptData,
} from '../model';
import { IndividualPostComponent } from './individual-post/individual-post.component';
import { MatIconModule } from '@angular/material/icon';
import { AiBundlePostsStoreService } from '../ai-bundle-posts-store.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { take } from 'rxjs';
import { SMFeaturesService } from '../../../../../app/core/features.service';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';

@Component({
  selector: 'app-select-ai-bundle',
  templateUrl: './select-ai-bundle.component.html',
  styleUrls: ['./select-ai-bundle-component.scss'],
  imports: [
    CommonModule,
    MatCheckboxModule,
    GalaxyFormFieldModule,
    IndividualPostComponent,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    GalaxyButtonLoadingIndicatorModule,
    GalaxyStickyFooterModule,
    GalaxyPageModule,
    TranslateModule,
    GalaxyAlertModule,
  ],
})
export class SelectAiBundleComponent {
  @Input() mode: 'content' | 'content_and_images';
  @Input() bundlePosts: BundleSocialPosts[];
  @Input() technicalPromptData: TechnicalPromptData;
  @Output() editPost = new EventEmitter<BundleSocialPosts>();
  @Output() regenerateContent = new EventEmitter<TechnicalPromptData>();
  @Output() next = new EventEmitter<BundleSocialPosts[]>();
  @Output() back = new EventEmitter<number>();
  errorMessageDelay = 10000;
  isLoading = false;
  maxPost = MAX_ALLOWED_POSTS;
  generateContentData!: GenerateContentSetup;

  errorCodeMapping = {
    [ErrorCode.IG_USER_NO_MEDIA]: 'AI_BUNDLE.SELECT_PAGE.INSTAGRAM_ERROR',
    [ErrorCode.TW_USER_TEXT_TOO_LONG]: 'AI_BUNDLE.SELECT_PAGE.TWITTER_ERROR',
  };

  constructor(
    public confirmationModal: OpenConfirmationModalService,
    private aiBundlePostsStore: AiBundlePostsStoreService,
    private translateService: TranslateService,
    private snackBarService: SnackBarService,
    public featuresService: SMFeaturesService,
    private productAnalyticsService: ProductAnalyticsService,
  ) {}

  get selectedPosts(): BundleSocialPosts[] {
    return this.bundlePosts.filter((post) => post.selected);
  }

  addAnotherPost() {
    this.isLoading = true;
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BUNDLE_AI_ADD_ANOTHER, 'user', 'click', 0);
    this.aiBundlePostsStore.generateContent$.pipe(take(1)).subscribe((prevPayload) => {
      if (prevPayload) {
        const updatedPayload = { ...prevPayload, numberOfPosts: 1 };
        this.aiBundlePostsStore.generateContent(updatedPayload, false, true);
        this.aiBundlePostsStore.generationComplete$.pipe(take(1)).subscribe(() => {
          this.isLoading = false;
        });
      }
    });
  }

  handleNextClick() {
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BUNDLE_AI_SAVE_SELECT, 'user', 'click', 0);
    this.next.emit(this.selectedPosts);
  }

  openBackWarningModal() {
    this.confirmationModal
      .openModal({
        type: 'confirm',
        title: this.translateService.instant('AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_TITLE'),
        message: this.translateService.instant('AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_CONTENT'),
        hideCancel: false,
        confirmButtonText: this.translateService.instant('AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_GO_BACK'),
        cancelButtonText: this.translateService.instant('AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_CANCEL'),
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .subscribe((userDidAction: boolean) => {
        if (userDidAction) {
          this.back.emit(AI_BUNDLE_POSTS_STEPS.GenerateContent);
        }
      });
  }

  editBundlePost(bundlePost: BundleSocialPosts) {
    this.editPost.emit(bundlePost);
  }

  showValidationMessage(event: ErrorCode) {
    const message = this.translateService.instant(this.errorCodeMapping[event]);
    this.snackBarService.errorSnack(message, null, { duration: this.errorMessageDelay, horizontalPosition: 'center' });
  }
}
