<div class="header">
  <div class="header__subtitle">
    {{ 'AI_BUNDLE.SELECT_PAGE.SUB_HEADING' | translate }}
  </div>
</div>
<div class="card-container">
  <app-individual-post
    *ngFor="let selectSocialPost of bundlePosts"
    [mode]="mode"
    [bundleSocialPost]="selectSocialPost"
    (invalidPost)="showValidationMessage($event)"
    (editPost)="editBundlePost($event)"
  ></app-individual-post>

  @if (bundlePosts?.length <= maxPost && (featuresService.enableAddAnotherPost$ | async)) {
    <div class="add-post-box">
      <button mat-flat-button color="primary" (click)="addAnotherPost()" [disabled]="isLoading">
        <glxy-button-loading-indicator [isLoading]="isLoading">
          <span class="button-content">
            <mat-icon class="add-another-icon">add</mat-icon>
            {{ 'POSTS_CAMPAIGN.ADD_POST' | translate }}
          </span>
        </glxy-button-loading-indicator>
      </button>
    </div>
  }

  <mat-card class="action">
    <mat-card-content>
      <div class="right-container">
        <button mat-stroked-button (click)="openBackWarningModal()">
          {{ 'AI_BUNDLE.SELECT_PAGE.BACK' | translate }}
        </button>
        <button
          mat-raised-button
          color="primary"
          [disabled]="this.selectedPosts.length === 0"
          (click)="handleNextClick()"
        >
          {{ 'AI_BUNDLE.SELECT_PAGE.NEXT' | translate }}
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>
