@use 'design-tokens' as *;

$secondary-button: #9e9e9e;

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-2 0;

  .header__title {
    font-size: $font-preset-2-size;
  }

  .header__subtitle {
    color: $secondary-text-color;
  }
}

.card-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 10px 0;
}

.action {
  margin-top: $spacing-2;

  mat-card-content {
    justify-content: end;
    display: flex;
    align-items: center;
    width: 100%;
    padding: $spacing-2;

    .left-container {
      display: flex;
      align-items: center;

      button {
        color: $secondary-button;
      }
    }

    .right-container {
      display: flex;
      align-items: center;
      gap: $spacing-2;
    }
  }
}

::ng-deep .cdk-overlay-container {
  z-index: 10000000000;
}

.add-another-button {
  margin-bottom: 15px;
  background-color: $glxy-blue-700;
  color: white;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.add-another-icon {
  margin-top: 6px;
  font-size: 18px;
  align-items: center;
}

.add-post-box {
  width: 100%;
  justify-content: center;
  display: flex;
}

.button-content {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
