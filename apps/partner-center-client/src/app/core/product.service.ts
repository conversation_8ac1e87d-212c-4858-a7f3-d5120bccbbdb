import { HttpErrorResponse } from '@angular/common/http';
import { Inject, Injectable, OnDestroy, signal } from '@angular/core';
import {
  BehaviorSubject,
  combineLatest,
  EMPTY,
  Observable,
  of as observableOf,
  of,
  ReplaySubject,
  Subject,
} from 'rxjs';
import { catchError, expand, filter, map, reduce, switchMap, take, takeUntil } from 'rxjs/operators';

import {
  ListProductFilter,
  ListProductsResponse,
  MarketplacePackagesApiService,
  ProductService as ProductsService,
} from '@vendasta/marketplace-packages';
import { MarketsService } from './markets/markets.service';
import { Product } from './product/product';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { AccountsService } from '@vendasta/accounts/legacy';
import { ListAppSettingsResponse } from '@vendasta/marketplace-apps';
import { IdentityProviderApiService } from '@vendasta/sso';
import { pluckUndefined } from '../arrays/pluck';
import { MarketplaceAppService } from './marketplace-app.service';
import { mergeUpdatedArrays } from './utils';

@Injectable({ providedIn: 'root' })
export class ProductService implements OnDestroy {
  constructor(
    private marketsService: MarketsService,
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    private productsService: ProductsService,
    private accountsService: AccountsService,
    private marketplaceAppsService: MarketplaceAppService,
    private packagesService: MarketplacePackagesApiService,
    private appPartnerService: AppPartnerService,
    private identityProviderApiService: IdentityProviderApiService,
  ) {
    const currentMarket$ = this.marketsService.currentMarket$;

    combineLatest([this.marketplaceAllProducts$])
      .pipe(takeUntil(this._destroyed$$))
      .subscribe(([productsData]) => {
        const existingProducts = this.products$$.getValue();
        const products = productsData.products.map(this.productFromProductData);
        const newestProducts = this.mergeProducts(existingProducts, products);
        this.products$$.next(newestProducts);
      });

    combineLatest([this.marketplaceAllApprovedProducts$])
      .pipe(takeUntil(this._destroyed$$))
      .subscribe(([approvedProductsData]) => {
        const approvedProducts = approvedProductsData.products.map(this.productFromProductData);
        this.approvedProducts$$.next(approvedProducts);
      });

    // TODO: If products have a market, and we know we want all products preloaded, could be a frontend filter on all products instead?
    this._productsForMarket$ = combineLatest([this.partnerId$, currentMarket$]).pipe(
      switchMap(([partnerId, market]) => this.getAllProducts(partnerId, null, market.market_id)),
      filter((resp) => !!resp),
      map((resp) => resp.products.map(this.productFromProductData)),
    );

    this.loadAllProducts();
    this.loadApprovedProducts();
  }

  get productsForMarket(): Observable<Product[]> {
    return this._productsForMarket$;
  }

  get products(): Observable<Product[]> {
    return this._products$;
  }

  private get approvedProducts(): Observable<Product[]> {
    return this._approvedProducts$;
  }

  get approvedProductsForSelectedMarket(): Observable<Product[]> {
    return this.approvedProducts.pipe(
      map((approvedProducts) => approvedProducts.filter((product) => !product.suspendedDate)),
    );
  }

  private products$$: BehaviorSubject<Product[]> = new BehaviorSubject<Product[]>([]);
  private approvedProducts$$: ReplaySubject<Product[]> = new ReplaySubject<Product[]>();
  private _products$: Observable<Product[]> = this.products$$.asObservable();
  private _approvedProducts$: Observable<Product[]> = this.approvedProducts$$.asObservable();
  private _productsForMarket$: Observable<Product[]>;

  private marketplaceAllProducts$$: ReplaySubject<ListProductsResponse> = new ReplaySubject<ListProductsResponse>();
  private marketplaceAllProducts$: Observable<ListProductsResponse> = this.marketplaceAllProducts$$.asObservable();
  private marketplaceAllApprovedProducts$$: ReplaySubject<ListProductsResponse> =
    new ReplaySubject<ListProductsResponse>();
  private marketplaceAllApprovedProducts$: Observable<ListProductsResponse> =
    this.marketplaceAllApprovedProducts$$.asObservable();

  private readonly _destroyed$$: Subject<void> = new Subject<void>();

  private readonly _isProductLoading = signal<boolean>(true);
  readonly isProductLoading = this._isProductLoading.asReadonly();

  static validateAndFillNewProductWithOtherOldProduct(newProduct: Product, oldProduct: Product): Product {
    if (!newProduct) {
      return null;
    }

    if (!oldProduct) {
      return newProduct;
    }
    const oldProductClone = { ...oldProduct };
    const newProductClone = pluckUndefined<Product>({ ...newProduct } as Product);

    return Object.assign(oldProductClone, newProductClone);
  }

  appWithId(productId: string): Observable<Product> {
    return this.marketplaceAppsService.getAppWithId(productId).pipe(map((app) => this.productFromProductData(app)));
  }

  getProducts(productIds: string[], marketIdObservable: Observable<string> = null): Observable<Product[]> {
    if (!productIds || !productIds.length) {
      return of([]);
    }

    if (marketIdObservable === null) {
      marketIdObservable = this.marketsService.currentMarket$.pipe(map((market) => market.market_id));
    }

    return combineLatest([this.partnerId$, marketIdObservable]).pipe(
      switchMap(([partnerId, marketId]) => {
        return this.packagesService.getMultiProducts({
          partnerId: partnerId,
          marketId: marketId,
          productIds: productIds,
        });
      }),
      map((resp) => {
        return resp.products.map((productContainer) => this.productFromProductData(productContainer.product));
      }),
      take(1),
    );
  }

  loadAllProducts(): void {
    this._isProductLoading.set(true);
    this.partnerId$
      .pipe(
        switchMap((partnerId) => this.getAllProducts(partnerId, null, null)),
        filter((resp) => !!resp),
        takeUntil(this._destroyed$$),
      )
      .subscribe((resp) => {
        this.marketplaceAllProducts$$.next(resp);
        this._isProductLoading.set(false);
      });
  }

  loadApprovedProducts(): void {
    this.partnerId$
      .pipe(
        switchMap((partnerId) => {
          const listFilter = new ListProductFilter({ approved: true });
          return this.getAllProducts(partnerId, listFilter, null);
        }),
        filter((resp) => !!resp),
        takeUntil(this._destroyed$$),
      )
      .subscribe((resp) => this.marketplaceAllApprovedProducts$$.next(resp));
  }

  /**
   * Retrieves all products for a given partner ID and filter by paging over the API until hasMore is false
   */
  getAllProducts(
    partnerId: string,
    listFilter: ListProductFilter | null,
    marketId: string | null,
  ): Observable<ListProductsResponse> {
    const pageSize = 500;
    const loadFunc = (cursor?: string): Observable<ListProductsResponse> => {
      return this.productsService.loadAllProducts(partnerId, pageSize, cursor, marketId, listFilter);
    };
    return loadFunc().pipe(
      expand((resp) => (resp.hasMore ? loadFunc(resp.nextCursor) : EMPTY)),
      reduce(
        (acc, res) => {
          if (!!res.products && res.products.length > 0) {
            acc.products.push(...res.products);
          }
          return acc;
        },
        new ListProductsResponse({
          products: [],
          hasMore: false,
          nextCursor: null,
        }),
      ),
    );
  }

  private productFromProductData(productData: any): Product {
    productData.enabled = true;
    productData.product_id = productData.appId;
    return new Product(productData);
  }

  /**
   * Given two lists of products, merge them into one, containing only the newest unique items from
   * each.
   */
  mergeProducts(products1: Product[], products2: Product[]): Product[] {
    if (products1.length > 0 && products2.length > 0) {
      return mergeUpdatedArrays(products1, products2, 'product_id');
    }
    return products1.length > 0 ? products1 : products2.length > 0 ? products2 : [];
  }

  canActivateProducts(appIds: string[], accountGroupId?: string): Observable<boolean> {
    // TODO: Remove catch if 404 after accounts is redeployed
    return this.checkAppRequirements(appIds, accountGroupId).pipe(
      catchError((err: HttpErrorResponse) => {
        if (err.status === 404) {
          return observableOf(true);
        }
      }),
    );
  }

  checkAppRequirements(appIds: string[], accountGroupId: string): Observable<boolean> {
    const response$ = this.accountsService.canActivate(appIds, accountGroupId);
    return response$.pipe(
      map((r) => {
        if (r.invalidAppIds.length > 0) {
          console.log(`Error: apps ${r.invalidAppIds} are not eligible to be activated on this account.`);
          return false;
        }
        return true;
      }),
    );
  }

  listAppSettings$(partnerId: string, marketId: string): Observable<ListAppSettingsResponse> {
    return this.appPartnerService.listAppSettings(partnerId, marketId, '', 1000);
  }

  getEntryUrl(serviceProviderId: string): Observable<string> {
    return this.identityProviderApiService.getEntryUrl({ serviceProviderId }).pipe(
      map((resp) => resp.entryUrl),
      take(1),
    );
  }

  ngOnDestroy(): void {
    this._destroyed$$.next();
    this._destroyed$$.complete();
  }
}
