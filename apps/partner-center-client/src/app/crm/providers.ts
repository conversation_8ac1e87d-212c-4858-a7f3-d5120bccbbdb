import { ComponentPortal } from '@angular/cdk/portal';
import { InjectionToken } from '@angular/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { SubjectParticipant } from '@galaxy/conversation/core';
import {
  CrmDependencies,
  CRMFilterOverride,
  CRMRowObject,
  InitialFilters,
  PlatformExtensionFieldIds,
  StandardIds,
  SystemFieldIds,
  TeamMemberFilterChipComponent,
  TeamMemberFilterInputComponent,
} from '@galaxy/crm/static';
import { ConversationChannel, GlobalParticipantType } from '@vendasta/conversation';
import { OpportunityDetailsComponent } from '@vendasta/sales-ui';
import { Feature } from 'marketplace-ui';
import { combineLatest, firstValueFrom, Observable, of, startWith } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import {
  CompanyDependencies,
  configFactory,
  ContactDependencies,
  ListsDependencies,
  Services,
  servicesFactory,
  TaskDependencies,
} from './providers-helper';
import { FeatureFlags } from '../core/features';
import { CustomObjectDependencies } from './providers-helper/custom-object-dependencies';

// Reexport factory functions
export { opportunityServiceConfigFactory, userPipelinesFactory } from './providers-helper';

export const CrmInjectionToken = new InjectionToken<CrmDependencies>('dependencies given to crm library', {
  factory: (): CrmDependencies => {
    const services = servicesFactory();
    const config = configFactory(services);

    return {
      appID: 'partner-center-client',
      routePrefix$: of('/crm'),
      namespace$: config.partnerId$,
      parentNamespace$: of(''),
      viewProfileFlag$: of(true),
      hasCrmAssociationModalFeatureFlag$: config.partnerId$.pipe(
        switchMap((namespace) => {
          return services.featureFlagService.batchGetStatus(namespace, '', ['crm_association_modal']);
        }),
        map((featureFlagStatus) => featureFlagStatus['crm_association_modal'] ?? false),
        startWith(false),
      ),
      customObjectFeatureFlag: 'pcc_crm_custom_objects',
      hasTimelineActivitiesFeatureFlag$: config.partnerId$.pipe(
        switchMap((namespace) => {
          return services.featureFlagService.batchGetStatus(namespace, '', ['crm_timeline_activities']);
        }),
        map((featureFlagStatus) => featureFlagStatus['crm_timeline_activities'] ?? false),
        shareReplay({ refCount: true, bufferSize: 1 }),
      ),
      showLeadProspector$: of(true),
      currentUserId$: config.currentUserId$,
      locale$: config.locale$,
      activity: {
        filterInputOverrides$: activityFilterInputOverrides$(services),
        initialFilters$: activityInitialFilters$(),
      },
      contact: ContactDependencies.build(services, config),
      company: CompanyDependencies.build(services, config),
      task: TaskDependencies.build(services),
      lists: ListsDependencies.build(config),
      customobject: CustomObjectDependencies.build(services),
      customActions: {
        navigateToFulfillmentProjects: navigateToFulfillmentProjects(services),
        openOpportunitiesDetails: openOpportunityDetails(services),
      },
      showBackToAccountsButton: true,
      services: {
        userService: services.crmUserService,
        accessService: services.crmAccessService,
        opportunityService: services.partnerCRMOpportunityService,
      },
      hasInboxAccess$: services.crmAccessService.canAccessInbox$,
      canShowInboxForContact$: config.featureFlags$[FeatureFlags.INBOX_SMS],
      openContactConversation: async (_: string, __: string, contactId: string, channel: ConversationChannel) => {
        // TODO(WARP): allow more channels when available
        if (channel !== ConversationChannel.CONVERSATION_CHANNEL_SMS) {
          services.snackbar.openErrorSnack('CRM.UNABLE_TO_SEND_MESSAGE');
          return;
        }
        const partnerId = await firstValueFrom(config.partnerId$);
        const participants = [
          new SubjectParticipant({
            internalParticipantId: partnerId,
            participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
          }),
          new SubjectParticipant({
            internalParticipantId: contactId,
            participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
          }),
        ];
        await services.inboxNavigationService.gotoConversationChannel(participants, channel);
      },
      openAccountGroupConversation: async (accountGroupId: string) => {
        const partnerId = await firstValueFrom(config.partnerId$);
        services.inboxNavigationService.gotoConversationChannel(
          [
            new SubjectParticipant({
              internalParticipantId: partnerId,
              participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
            }),
            new SubjectParticipant({
              internalParticipantId: accountGroupId,
              participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
            }),
          ],
          ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
        );
      },
      hasAccessToFeature$: (featureString: string) => {
        const feature = featureString as Feature;
        return services.featureAccessService.hasAccessToFeature(feature);
      },
      openRestrictedDialog: (featureId) => {
        services.featureAccessService.openRestrictedDialog(featureId);
      },

      meetingAction: (contactId) => {
        services.meetingEventService.openMeetingEventListDialog(
          contactId,
          config.currentUserId$,
          config.partnerId$,
          of(''),
        );
      },
      isMeetingIconNeeded$: combineLatest([config.isMeetingSchedulerConfigured$, config.hasMeetingFeatureFlag$]).pipe(
        map(
          ([isMeetingSchedulerConfigured, hasMeetingFeatureFlag]) =>
            isMeetingSchedulerConfigured && hasMeetingFeatureFlag,
        ),
      ),
    };
  },
});

function activityFilterInputOverrides$(services: Services): Observable<CRMFilterOverride[]> {
  return of([
    {
      title: services.translationService.instant('CRM.CUSTOM_FILTERS.OWNER'),
      fieldId: SystemFieldIds.ActivityOwnerID,
      filterInput: TeamMemberFilterInputComponent,
      filterChip: TeamMemberFilterChipComponent,
    },
  ] as CRMFilterOverride[]);
}

function activityInitialFilters$(): Observable<InitialFilters[]> {
  return of([{ fieldId: SystemFieldIds.ActivityOwnerID, filterId: 'filterByOwnerId' }] as InitialFilters[]);
}

function navigateToFulfillmentProjects(services: Services): (row: CRMRowObject) => void {
  return (row: CRMRowObject): void => {
    const pid$ = services.partnerService.getPartnerId();
    const crmObject$ = pid$.pipe(
      switchMap((pid) => services.companyService.getMultiObject({ namespace: pid, crmObjectIds: [row.objectId] })),
      map((companies) => companies?.crmObjects?.[0]),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    const agid$ = crmObject$.pipe(
      map((o) => o?.fields?.find((f) => f.fieldId === PlatformExtensionFieldIds.AccountGroupID)?.stringValue || ''),
    );
    const name$ = crmObject$.pipe(
      map((o) => o?.fields?.find((f) => f.fieldId === StandardIds.CompanyName)?.stringValue || ''),
    );
    const companyInfo = firstValueFrom(combineLatest([agid$, name$]));
    companyInfo.then(([accountGroupId, name]) => {
      const url = 'task-manager/reporting';
      const queryParams = { 'project-report': JSON.stringify({ account: { accountGroupId, name } }) };
      services.router.navigate([url], { queryParams });
    });
  };
}

function openOpportunityDetails(services: Services): (opportunityId: string, accountGroupId: string) => void {
  return (opportunityId: string, accountGroupId: string): void => {
    services.opportunityService.loadOpportunityAndSetSelected(opportunityId, accountGroupId);
    const c = new ComponentPortal(OpportunityDetailsComponent);
    services.sidePanelContentService.setContent(c);
  };
}
