import { CrmFulfillmentProjectsAssociationCardComponent, ExportSelectAllRowAction } from '@galaxy/crm/dynamic';
import {
  AutomationMultiRowAction,
  AutomationSelectAllRowAction,
  AutomationSingleRowAction,
} from '@galaxy/crm/integrations/automation';
import {
  ActionButton,
  CompanyCustomColumns,
  CompanyCustomFilterChipComponent,
  CompanyCustomFilterInputComponent,
  CompanyToCustomObjectAssociationsCardComponent,
  CreateAssociationFields,
  CRMFilterOverride,
  CrmObjectDependencies,
  FormCustomInput,
  FormDefaultInputValue,
  HiddenTextFormInputComponent,
  HiddenStringValuesFormInputComponent,
  InitialFilters,
  MultiRowAction,
  ParentChildCompanyAssociationsCardComponent,
  PlatformExtensionFieldIds,
  ProfileCard,
  SelectAllAction,
  SingleRowAction,
  StandardExternalIds,
  StandardIds,
  SystemFieldIds,
  TableCustomCell,
} from '@galaxy/crm/static';
import { Row } from '@vendasta/galaxy/table';
import { combineLatest, Observable, of, take } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { FeatureFlags } from '../../core/features';
import { AssociatedAccountComponent } from '../company-details/association-panel/associated-account/associated-account.component';
import { AutomationsComponent } from '../company-details/association-panel/automations/automations.component';
import { OpportunitiesComponent } from '../company-details/association-panel/opportunities/opportunities.component';
import { PartnerInfoComponent } from '../company-details/association-panel/partner-info/partner-info.component';
import { ReportsComponent } from '../company-details/association-panel/reports/reports.component';
import { SalesOrdersComponent } from '../company-details/association-panel/sales-orders/sales-orders.component';
import { ActionButtonComponent } from '../company-details/open-business-app/open-business-app.component';
import { SnapshotSidepanelComponentInputs } from '../company-table/snapshot-sidepanel/snapshot-sidepanel.service';
import { AdditionalSalespeopleCellComponent } from '../custom-cells/additional-salespeople/additional-salespeople.component';
import { SalespersonCellComponent } from '../custom-cells/salesperson/salesperson.component';
import { AdditionalSalespeopleFilterInputComponent } from '../custom-filters/additional-salespeople/additional-salespeople-filter-input.component';
import { MarketFilterChipComponent, MarketFilterInputComponent } from '../custom-filters/market';
import { SalespersonFilterChipComponent } from '../custom-filters/salesperson/salesperson-filter-chip.component';
import { SalespersonFilterInputComponent } from '../custom-filters/salesperson/salesperson-filter-input.component';
import { AdditionalSalespeopleFormInputComponent } from '../custom-form-inputs/additional-salespeople/additional-salespeople-form-input.component';
import { SalespersonFormInputComponent } from '../custom-form-inputs/salesperson/salesperson-form-input.component';
import { PccCompanyToContactAssociationsCardComponent } from '../profile-cards/pcc-company-to-contact-associations-card/pcc-company-to-contact-associations-card.component';
import { Config, Services } from './interface';
import { CrmObjectInterface, GetMultiCrmObjectResponse } from '@vendasta/crm';
import { GroupIdFormInputComponent } from '../custom-form-inputs/group-id/group-id-form-input.component';
import { CRMMarketContext } from '../salesperson-market.service';
import { ImpersonateUserDialogComponent } from '../dialogs/impersonate-user-dialog/impersonate-user-dialog.component';
import { GetMultiFieldSchemaRequestInterface } from '@vendasta/crm/lib/_internal/interfaces';
import { SupportTicketComponent } from '../company-details/association-panel/support-ticket/support-ticket.component';
import { CrmMarketplaceProductsAssociationCardComponent } from '../company-details/association-panel/marketplace-products/marketplace-products.component';
import {
  AddToListMultiRowAction,
  AddToListSelectAllAction,
  AddToListSingleRowAction,
} from '@galaxy/crm/integrations/dynamic-lists-actions';
import { LeadScoreBadgeComponent } from '@galaxy/crm/components/badge';
import { CategoriesFormInputComponent } from '../custom-form-inputs/categories-form-input/categories-form-input.component';
import { CategoriesCellComponent } from '../custom-cells/categories/categories.component';
import { CategoriesFilterInputComponent } from '../custom-filters/categories/categories-filter-input.component';
import { CategoriesFilterChipComponent } from '../custom-filters/categories/categories-filter-chip.component';
import { objectViewPresets$ } from './factory';
import { ConfigApplierDialogComponent } from '@vendasta/config-manager';

export class CompanyDependencies {
  constructor(
    private readonly services: Services,
    private readonly config: Config,
  ) {}

  build(): CrmObjectDependencies {
    return {
      selectAllTableActions$: this.selectAllTableActions$(),
      singleRowTableActions$: this.singleRowTableActions$(),
      multiRowTableActions$: this.multiRowTableActions$(),
      actionButtons$: this.actionButtons$(),
      profileCards$: this.profileCards$(),
      profileHeader$: this.profileHeaders$(),
      filterInputOverrides$: this.filterInputOverrides$(),
      initialFilters$: this.initialFilters$(),
      tableCustomCells: this.tableCustomCells(),
      formCustomInputs: this.formCustomInput(),
      formDefaultOnCreateInputValues$: this.formDefaultOnCreateInputValues$(),
      baseColumnIds: this.baseColumnIds(),
      presetFilters$: objectViewPresets$(
        this.services.translationService,
        this.services.translateForObjectService,
        this.config.salespersonId$,
        'Company',
        this.services.crmFieldService.getFieldId(StandardIds.CompanyLastEngagementDate),
        this.services.crmFieldService.getFieldId(StandardIds.CompanyPrimarySalespersonID),
      ),
      syncIds$: this.syncIds(),
      accountId$: this.getAccountId(),
      onProfilePageInit: (object: CrmObjectInterface) => {
        this.services.marketsService.selectMarketByID(CRMMarketContext.EditContext, object?.groupId);
      },
      createAssociationFields: this.createAssociationFields(),
    };
  }

  static build(services: Services, config: Config): CrmObjectDependencies {
    const instance = new CompanyDependencies(services, config);
    return instance.build();
  }

  private createAssociationFields(): CreateAssociationFields {
    return {
      objectTypes: ['Contact'],
      required: false,
    };
  }

  private profileCards$(): Observable<ProfileCard[]> {
    return combineLatest([
      this.config.partnerId$,
      this.config.featureFlags$[FeatureFlags.CRM_COMPANY_COMPANY_ASSOCIATION],
      this.services.crmAccessService.canAccessSales$,
      this.services.crmAccessService.canAccessAutomations$,
      this.services.crmAccessService.canAccessTaskManager$,
      this.config.featureFlags$[FeatureFlags.PCC_CUSTOM_OBJECTS],
    ]).pipe(
      map(
        ([
          partnerId,
          companyCompanyAssociationFlag,
          canAccessSales,
          canAccessAutomations,
          canAccessTaskManager,
          canAccessCustomObjects,
        ]) => {
          const profileCards: ProfileCard[] = [];
          if (partnerId === 'VMF') {
            profileCards.push({ component: PartnerInfoComponent });
            profileCards.push({ component: SupportTicketComponent });
          }
          profileCards.push({ component: PccCompanyToContactAssociationsCardComponent });
          profileCards.push({ component: AssociatedAccountComponent });
          profileCards.push({ component: CrmMarketplaceProductsAssociationCardComponent });
          if (canAccessSales) {
            profileCards.push({ component: OpportunitiesComponent });
          }
          if (canAccessAutomations) {
            profileCards.push({ component: AutomationsComponent });
          }
          profileCards.push({ component: ReportsComponent });

          profileCards.push({ component: SalesOrdersComponent });
          if (canAccessTaskManager) {
            profileCards.push({ component: CrmFulfillmentProjectsAssociationCardComponent });
          }
          if (companyCompanyAssociationFlag) {
            profileCards.push({ component: ParentChildCompanyAssociationsCardComponent });
          }
          if (canAccessCustomObjects) {
            profileCards.push({ component: CompanyToCustomObjectAssociationsCardComponent });
          }
          return profileCards;
        },
      ),
    );
  }

  private actionButtons$(): Observable<ActionButton[]> {
    return of([{ component: ActionButtonComponent }] as ActionButton[]);
  }

  private selectAllTableActions$(): Observable<SelectAllAction[]> {
    return combineLatest([
      this.config.canAddToList$,
      this.services.partnerService.getPartnerId(),
      this.config.canStartManualAutomation$,
    ]).pipe(
      map(([canAddToList, partnerId, canStartManualAutomation]) => {
        const actions: SelectAllAction[] = [];
        if (canAddToList) {
          actions.push(
            AddToListSelectAllAction(partnerId, this.services.listActionsService, 'Company', 'CRM.ADD_TO_STATIC_LIST'),
          );
        }
        if (canStartManualAutomation) {
          actions.push(
            AutomationSelectAllRowAction(
              partnerId,
              this.services.automationActionsService,
              'Company',
              this.services.translationService.instant('COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION'),
            ),
          );
        }
        actions.push(
          ExportSelectAllRowAction(
            partnerId,
            this.services.exportActionsService,
            'Company',
            'ACTIONS.CRM.EXPORT.LABEL',
          ),
        );
        return actions;
      }),
    );
  }

  private singleRowTableActions$(): Observable<SingleRowAction[]> {
    const vbcEntryInfo$ = this.services.partnerService
      .getPartnerId()
      .pipe(switchMap((partnerId) => this.services.businessCenterService.getVBCEntryUrl(partnerId)));
    return combineLatest([
      vbcEntryInfo$,
      this.services.partnerService.getPartnerId(),
      this.config.canStartManualAutomation$,
      this.config.canAddToList$,
      this.config.isUserSuperAdmin$,
      this.config.featureFlags$[FeatureFlags.ACCOUNT_CONFIGURATIONS],
    ]).pipe(
      map(([vbcEntryInfo, partnerId, canStartManualAutomation, canAddToList, isUserSuperAdmin, configFlag]) => {
        const actions: SingleRowAction[] = [];

        if (canAddToList) {
          actions.push(
            AddToListSingleRowAction(partnerId, this.services.listActionsService, 'Company', 'CRM.ADD_TO_STATIC_LIST'),
          );
        }

        actions.push({
          label: this.services.whitelabelTranslationService.instant('SNAPSHOT_REPORT.COMMON.SNAPSHOT_NAME'),
          callback: (row: Row) => {
            const sidepanelInputs: SnapshotSidepanelComponentInputs = {
              companyID: row?.data?.[SystemFieldIds.CompanyID]?.value as string,
              partnerID: partnerId,
              accountGroupID: row?.data?.[PlatformExtensionFieldIds.AccountGroupID]?.value as string,
              trackingCategory: 'list-company-table',
              snapshotName: this.services.whitelabelTranslationService.instant('SNAPSHOT_REPORT.COMMON.SNAPSHOT_NAME'),
            };
            this.services.snapshotSidepanelService.openSidepanel(sidepanelInputs);
          },
        });
        actions.push({
          label: 'COMPANY.LIST_OBJECTS_TABLE.ACTIONS.IMPERSONATE',
          visible: (row: Row) => {
            const accountGroupId = row?.data?.[PlatformExtensionFieldIds.AccountGroupID]?.value as string;
            return !!accountGroupId;
          },
          callback: (row: Row) => {
            const accountGroupId = row?.data?.[PlatformExtensionFieldIds.AccountGroupID]?.value as string;
            const companyName = row?.data?.[StandardIds.CompanyName]?.value as string;
            this.services.dialogService
              .open(ImpersonateUserDialogComponent, {
                width: '620px',
                autoFocus: false,
                data: {
                  accountGroupId: accountGroupId,
                  companyName: companyName,
                },
              })
              .afterClosed()
              .pipe(take(1))
              .subscribe();
          },
        });
        actions.push({
          label:
            this.services.translationService.instant('COMPANY.LIST_OBJECTS_TABLE.ACTIONS.OPEN_BUSINESS_APP') +
            ' ' +
            vbcEntryInfo.name,
          visible: (row: Row) => {
            const accountGroupId = row?.data?.[PlatformExtensionFieldIds.AccountGroupID]?.value as string;
            return !!accountGroupId;
          },
          callback: (row: Row) => {
            this.services.businessCenterService
              .getVBCEntryUrl(row?.data?.[PlatformExtensionFieldIds.AccountGroupID]?.value as string)
              .pipe(take(1))
              .subscribe((vbcEntryInfo) => {
                window.open(vbcEntryInfo.url, '_blank');
              });
          },
        });
        if (canStartManualAutomation) {
          actions.push(
            AutomationSingleRowAction(
              partnerId,
              this.services.automationActionsService,
              'Company',
              this.services.translationService.instant('COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION'),
            ),
          );
        }
        if (isUserSuperAdmin && configFlag) {
          actions.push({
            label: this.services.translationService.instant('COMPANY.LIST_OBJECTS_TABLE.ACTIONS.APPLY_CONFIGURATION'),
            callback: (row) => {
              const targetContainers = row.data[PlatformExtensionFieldIds.AccountGroupID]?.value
                ? [
                    {
                      id: row.data[PlatformExtensionFieldIds.AccountGroupID].value,
                      name: row.data[StandardIds.CompanyName].value,
                    },
                  ]
                : [];
              this.services.dialogService.open(ConfigApplierDialogComponent, {
                data: { targetContainers: targetContainers },
              });
            },
          });
        }
        return actions;
      }),
      shareReplay(1),
    );
  }

  private multiRowTableActions$(): Observable<MultiRowAction[]> {
    return combineLatest([
      this.config.canAddToList$,
      this.config.canStartManualAutomation$,
      this.services.partnerService.getPartnerId(),
      this.config.isUserSuperAdmin$,
      this.config.featureFlags$[FeatureFlags.ACCOUNT_CONFIGURATIONS],
    ]).pipe(
      map(([canAddToList, canStartManualAutomation, partnerId, isUserSuperAdmin, configFlag]) => {
        const actions: MultiRowAction[] = [];
        if (canAddToList) {
          actions.push(
            AddToListMultiRowAction(
              partnerId,
              this.services.listActionsService,
              'Company',
              this.services.translationService.instant('CRM.ADD_TO_STATIC_LIST'),
            ),
          );
        }
        if (canStartManualAutomation) {
          actions.push(
            AutomationMultiRowAction(
              partnerId,
              this.services.automationActionsService,
              'Company',
              this.services.translationService.instant('COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION'),
            ),
          );
        }
        if (isUserSuperAdmin && configFlag) {
          actions.push({
            label: this.services.translationService.instant('COMPANY.LIST_OBJECTS_TABLE.ACTIONS.APPLY_CONFIGURATION'),
            callback: (rows) => {
              this.services.dialogService.open(ConfigApplierDialogComponent, {
                data: {
                  targetContainers: rows
                    .filter((row) => row.data[PlatformExtensionFieldIds.AccountGroupID]?.value)
                    .map((row) => ({
                      id: row.data[PlatformExtensionFieldIds.AccountGroupID]?.value,
                      name: row.data[StandardIds.CompanyName].value,
                    })),
                },
              });
            },
          });
        }
        return actions;
      }),
    );
  }

  private filterInputOverrides$(): Observable<CRMFilterOverride[]> {
    return of([
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.PARENT_COMPANY'),
        fieldId: StandardIds.CompanyParentCompanyID,
        filterInput: CompanyCustomFilterInputComponent,
        filterChip: CompanyCustomFilterChipComponent,
      },
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.SALESPERSON'),
        fieldId: StandardIds.CompanyPrimarySalespersonID,
        filterInput: SalespersonFilterInputComponent,
        filterChip: SalespersonFilterChipComponent,
      },
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.ADDITIONAL_SALESPEOPLE'),
        fieldId: StandardIds.CompanyAdditionalSalespersonIDs,
        filterInput: AdditionalSalespeopleFilterInputComponent,
        filterChip: SalespersonFilterChipComponent,
      },
      {
        title: this.services.translationService.instant('COMMON.MARKET_SELECTOR.LABEL'),
        fieldId: SystemFieldIds.CompanyGroupID,
        filterInput: MarketFilterInputComponent,
        filterChip: MarketFilterChipComponent,
      },
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.CATEGORIES'),
        fieldId: StandardIds.CompanyCategoryIDs,
        filterInput: CategoriesFilterInputComponent,
        filterChip: CategoriesFilterChipComponent,
      },
    ] as CRMFilterOverride[]);
  }

  private initialFilters$(): Observable<InitialFilters[]> {
    return this.config.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.services.crmFieldSchemaApiService.getMultiFieldSchema({
          namespace: partnerId,
          crmObjectType: 'Company',
          fieldId: [
            StandardIds.CompanyName,
            StandardIds.CompanyPrimarySalespersonID,
            SystemFieldIds.CompanyCreated,
            SystemFieldIds.CompanyLastActivityDate,
            StandardIds.CompanyLifecycleStage,
          ],
        } as GetMultiFieldSchemaRequestInterface);
      }),
      map((response) => {
        return response.fieldSchemas
          .filter((schema) => !!schema && schema.fieldId)
          .map((schema) => {
            return {
              fieldId: schema.fieldId,
              filterId: 'filterCompanyBy' + schema.fieldName,
            };
          });
      }),
      shareReplay(1),
    );
  }

  private tableCustomCells(): TableCustomCell[] {
    return [
      {
        fieldIds: [StandardIds.CompanyPrimarySalespersonID],
        columnDefinition: {
          id: 'customSalespersonColumn',
          title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.SALESPERSON'),
        },
        customCellComponent: SalespersonCellComponent,
      },
      {
        fieldIds: [StandardIds.CompanyAdditionalSalespersonIDs],
        columnDefinition: {
          id: 'customAdditionalSalespeopleColumn',
          title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.ADDITIONAL_SALESPEOPLE'),
        },
        customCellComponent: AdditionalSalespeopleCellComponent,
      },
      {
        fieldIds: [StandardIds.CompanyCategoryIDs],
        columnDefinition: {
          id: 'customCategoriesColumn',
          title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.CATEGORIES'),
        },
        customCellComponent: CategoriesCellComponent,
      },
    ] as TableCustomCell[];
  }

  private formCustomInput(): FormCustomInput[] {
    return [
      {
        fieldId: SystemFieldIds.CompanyGroupID,
        component: GroupIdFormInputComponent,
      },
      {
        fieldId: StandardIds.CompanyPrimarySalespersonID,
        component: SalespersonFormInputComponent,
      },
      {
        fieldId: StandardIds.CompanyAdditionalSalespersonIDs,
        component: AdditionalSalespeopleFormInputComponent,
      },
      {
        fieldId: SystemFieldIds.CompanyOwnerID,
        component: HiddenTextFormInputComponent,
      },
      {
        fieldId: StandardIds.CompanyAdditionalTeamMemberIDs,
        component: HiddenStringValuesFormInputComponent,
      },
      {
        fieldId: StandardIds.CompanyCategoryIDs,
        component: CategoriesFormInputComponent,
      },
    ] as FormCustomInput[];
  }

  private syncIds(): Observable<string[]> {
    // Ids of fields to render the sync icon for
    return of([
      StandardIds.CompanyName,
      StandardIds.CompanyPhoneNumber,
      StandardIds.CompanyPrimaryAddressLine1,
      StandardIds.CompanyPrimaryAddressLine2,
      StandardIds.CompanyPrimaryAddressCity,
      StandardIds.CompanyPrimaryAddressState,
      StandardIds.CompanyPrimaryAddressPostalCode,
      StandardIds.CompanyPrimaryAddressCountry,
      StandardIds.CompanyTags,
      StandardIds.CompanyPrimarySalespersonID,
      StandardIds.CompanyAdditionalSalespersonIDs,
      StandardIds.CompanyLinkedInURL,
      StandardIds.CompanyFacebookURL,
      StandardIds.CompanyXURL,
      StandardIds.CompanyInstagramURL,
      StandardIds.CompanyPinterestURL,
      StandardIds.CompanyCategoryIDs,
      StandardIds.CompanyWebsite,
    ]);
  }

  private getAccountId() {
    return (crmObject: Observable<GetMultiCrmObjectResponse>) =>
      crmObject.pipe(
        map((response: GetMultiCrmObjectResponse) => {
          let field;
          if (response.crmObjects[0].fields) {
            field = response.crmObjects[0].fields.find(
              (field) => field.fieldId === PlatformExtensionFieldIds.AccountGroupID,
            );
          }
          return field?.stringValue;
        }),
      );
  }

  private formDefaultOnCreateInputValues$(): Observable<FormDefaultInputValue[]> {
    return combineLatest([
      this.config.salespersonId$,
      this.services.appConfigService.config$,
      this.services.marketsService.userAccessibleMarkets$,
    ]).pipe(
      map(([salespersonId, appConfig, markets]) => {
        const defaults = [
          { fieldId: StandardIds.CompanyLifecycleStage, value: 'Lead' },
          { fieldId: SystemFieldIds.CompanyOwnerID, value: appConfig.unifiedUserId },
          { fieldId: StandardIds.CompanySourceName, value: 'CRM UI' },
          { fieldId: StandardIds.CompanyRecordSourceDrill1, value: window.location.href },
          { fieldId: StandardIds.CompanyOriginalSource, value: 'CRM UI' },
          { fieldId: StandardIds.CompanyOriginalSourceDrill1, value: window.location.href },
        ];
        if (!!markets && markets.length === 1 && markets[0].market_id) {
          this.services.marketsService.selectMarket(CRMMarketContext.EditContext, markets[0]);
          defaults.push({
            fieldId: SystemFieldIds.CompanyGroupID,
            value: markets[0].market_id,
          });
        }
        if (salespersonId) {
          defaults.push({
            fieldId: StandardIds.CompanyPrimarySalespersonID,
            value: salespersonId,
          });
        }
        return defaults;
      }),
    );
  }

  private baseColumnIds(): string[] {
    return [
      StandardIds.CompanyName,
      StandardIds.CompanyPhoneNumber,
      StandardExternalIds.Email,
      StandardIds.CompanyLeadQuality,
      StandardIds.CompanyLeadScore,
      CompanyCustomColumns.FullAddress,
      'customSalespersonColumn',
      StandardIds.CompanyTags,
    ];
  }

  private profileHeaders$() {
    return of([{ component: LeadScoreBadgeComponent }]);
  }
}
