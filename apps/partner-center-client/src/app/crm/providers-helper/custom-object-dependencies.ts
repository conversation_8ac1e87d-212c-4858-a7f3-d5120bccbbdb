import {
  CreateAssociationFields,
  CRMFilterOverride,
  CrmObjectDependencies,
  CustomObjectAssociationsComponent,
  CustomObjectToCompanyAssociationsPanelComponent,
  CustomObjectToContactAssociationsPanelComponent,
  FormCustomInput,
  FormDefaultInputValue,
  ProfileCard,
  StandardIds,
  SystemFieldIds,
  TableCustomCell,
} from '@galaxy/crm/static';
import { Services } from './interface';
import { Observable, of } from 'rxjs';
import { CrmObjectInterface } from '@vendasta/crm';
import {
  CrmUserColumnCellComponent,
  CrmUserFilterChipComponent,
  CrmUserFilterInputComponent,
  CrmUserInputComponent,
} from '@galaxy/crm/components/user';
import { map } from 'rxjs/operators';

export class CustomObjectDependencies {
  constructor(private readonly services: Services) {}

  build(): CrmObjectDependencies {
    return {
      baseColumnIds: this.baseColumnIds(),
      getDisplayName: (crmObject: CrmObjectInterface) => {
        return this.services.crmObjectDisplayService.customObjectDisplayName(crmObject);
      },
      createAssociationFields: this.createAssociationFields(),
      profileCards$: this.profileCards$(),
      filterInputOverrides$: this.filterInputOverrides$(),
      tableCustomCells: this.tableCustomCells(),
      formCustomInputs: this.formCustomInput(),
      formDefaultOnCreateInputValues$: this.formDefaultOnCreateInputValues$(),
    };
  }

  static build(services: Services): CrmObjectDependencies {
    const instance = new CustomObjectDependencies(services);
    return instance.build();
  }

  private baseColumnIds(): string[] {
    return [StandardIds.CustomObjectName];
  }

  private profileCards$(): Observable<ProfileCard[]> {
    const profileCards = [
      { component: CustomObjectToContactAssociationsPanelComponent },
      { component: CustomObjectToCompanyAssociationsPanelComponent },
      { component: CustomObjectAssociationsComponent },
    ] as ProfileCard[];
    return of(profileCards);
  }

  private createAssociationFields(): CreateAssociationFields {
    return {
      objectTypes: ['Contact', 'Company', 'Opportunity', 'CustomObject'],
      required: false,
    };
  }

  private filterInputOverrides$(): Observable<CRMFilterOverride[]> {
    return of([
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.OWNER'),
        fieldId: SystemFieldIds.CustomObjectOwnerID,
        filterInput: CrmUserFilterInputComponent,
        filterChip: CrmUserFilterChipComponent,
      },
    ] as CRMFilterOverride[]);
  }

  private tableCustomCells(): TableCustomCell[] {
    return [
      {
        fieldIds: [SystemFieldIds.CustomObjectOwnerID],
        columnDefinition: {
          id: SystemFieldIds.CustomObjectOwnerID,
        },
        customCellComponent: CrmUserColumnCellComponent,
      },
    ] as TableCustomCell[];
  }

  private formCustomInput(): FormCustomInput[] {
    return [
      {
        fieldId: SystemFieldIds.CustomObjectOwnerID,
        component: CrmUserInputComponent,
      },
    ] as FormCustomInput[];
  }

  private formDefaultOnCreateInputValues$(): Observable<FormDefaultInputValue[]> {
    return this.services.appConfigService.config$.pipe(
      map((appConfig) => {
        return [{ fieldId: SystemFieldIds.CustomObjectOwnerID, value: appConfig.unifiedUserId }];
      }),
    );
  }
}
