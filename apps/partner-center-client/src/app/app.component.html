<!-- Nav visible -->
<ng-container *ngIf="(pageLoading$ | async) === false; else loading">
  <ng-container *ngIf="(navItems$ | async) && (isOnboardingUrl$ | async) === false; else pageContent">
    <atlas-navbar [dropdownItems]="menuItems" [hideBottomBorder]="useDarkNavHeader$ | async">
      <div class="atlas-navbar-space">
        <atlas-item>
          <button mat-icon-button (click)="nav.toggle()">
            <mat-icon>menu</mat-icon>
          </button>
        </atlas-item>
        <div class="atlas-title">
          <atlas-title
            icon="location_city"
            interactable="{{ showAtlasPidSelector$ | async }}"
            (click)="showAtlasPidSelector()"
          >
            {{ atlasTitle$ | async }}
          </atlas-title>
        </div>
        <div class="atlas-spacer"></div>
        <div class="atlas-actions">
          <ng-container *ngIf="isMobile$ | async; else desktop">
            <atlas-item icon="arrow_drop_down" interactable [matTooltip]="'Others'">
              <atlas-menu>
                <inbox-ai-button
                  *ngIf="(inboxAIFeatureFlag$ | async) && (hasViewAccessForInboxAI$ | async)"
                  [isMobile]="true"
                ></inbox-ai-button>
              </atlas-menu>
            </atlas-item>
            <atlas-item
              interactable
              icon="help"
              [matTooltip]="'NAVIGATION.HELP' | translate"
              [customClass]="helpDrawerSelected$ | async"
              (click)="showHelpCenter(helpCenterTemplateId)"
            ></atlas-item>
            <inbox-button
              *ngIf="(hasViewAccessForInboxFeature$ | async) && (fullscreenInboxViewFeatureFlag$ | async) === false"
              (click)="showHelpCenter()"
            ></inbox-button>
          </ng-container>

          <ng-template #desktop>
            <atlas-item
              *ngIf="hasViewAccessForNewReleases$ | async"
              data-canny-changelog
              interactable
              icon="new_releases"
              [matTooltip]="'NAVIGATION.NEW_RELEASES' | translate"
            ></atlas-item>
            <atlas-item
              *ngIf="hasViewAccessForHelpCenter$ | async"
              interactable
              icon="help"
              [matTooltip]="'NAVIGATION.HELP' | translate"
              [customClass]="helpDrawerSelected$ | async"
              (click)="showHelpCenter(helpCenterTemplateId)"
            ></atlas-item>
            <atlas-item
              *ngIf="recallFeatureFlag$ | async"
              interactable
              icon="event"
              [matTooltip]="'NAVIGATION.JOIN_MEETING' | translate"
            >
            </atlas-item>
            <atlas-menu id="atlas-navbar__meetings-join">
              <meeting-analysis-schedule-bot></meeting-analysis-schedule-bot>
            </atlas-menu>

            <inbox-ai-button
              *ngIf="(inboxAIFeatureFlag$ | async) && (hasViewAccessForInboxAI$ | async)"
              [isMobile]="false"
              (click)="showHelpCenter()"
            ></inbox-ai-button>
            <inbox-button
              *ngIf="(hasViewAccessForInboxFeature$ | async) && (fullscreenInboxViewFeatureFlag$ | async) === false"
              (click)="showHelpCenter()"
            ></inbox-button>
          </ng-template>
        </div>
      </div>
    </atlas-navbar>
    <inbox-inbox [modeType]="viewMode$ | async" [disabled]="disableInboxFeature$ | async">
      <glxy-nav #nav [appName]="'partner-center'" [ngClass]="vaNavTheme$ | async">
        <glxy-nav-panel [multi]="false">
          <glxy-nav-header [ngClass]="{ 'dark-header': (useDarkNavHeader$ | async) }">
            <app-logo></app-logo>
            <div>
              <app-level-badge *ngIf="(isSuperAdminRoute$ | async) === false"></app-level-badge>
            </div>
            <app-trial-badge *ngIf="(isSuperAdminRoute$ | async) === false" class="trial-badge"></app-trial-badge>
          </glxy-nav-header>

          <!-- PCC nav items are set in the partner microservice -->
          <!-- You can modify them here: https://github.com/vendasta/partner/blob/master/internal/partner_center_navigation/service.go -->
          <app-nav-item
            *ngFor="let menuItem of navItems$ | async"
            [activeMenuId]="activeMenuId$ | async"
            [item]="menuItem"
          ></app-nav-item>

          <glxy-nav-footer>
            <app-assigned-salesperson-nav-footer-card
              *ngIf="hasViewAccessForSalesContactCard$ | async"
            ></app-assigned-salesperson-nav-footer-card>
          </glxy-nav-footer>
        </glxy-nav-panel>

        <!-- Page Content -->
        <app-help-side-drawer-container [modeType]="viewMode$ | async">
          <ng-container *ngTemplateOutlet="pageContent"></ng-container>
          <router-outlet name="action"></router-outlet>
        </app-help-side-drawer-container>
      </glxy-nav>
    </inbox-inbox>
  </ng-container>
</ng-container>

<ng-template #loading>
  <div id="loading-spinner">
    <mat-spinner [diameter]="50"></mat-spinner>
  </div>
</ng-template>

<ng-template #pageContent>
  <router-outlet>
    <glxy-loading-spinner *ngIf="loadingLazyModule" [fullHeight]="true"></glxy-loading-spinner>
  </router-outlet>
</ng-template>

<ng-template #helpCenter>
  <app-help-center [partnerId]="partnerId" [subscriptionTier]="subscriptionTier"></app-help-center>
</ng-template>
