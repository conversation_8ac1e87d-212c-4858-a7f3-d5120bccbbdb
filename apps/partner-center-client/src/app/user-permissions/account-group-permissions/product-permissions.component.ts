import { ChangeDetectorRef, Component, EventEmitter, Inject, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { AccountGroup } from '@galaxy/account-group';
import { FeatureFlagService, WhitelabelService } from '@galaxy/partner';
import { combineLatest, Observable, ReplaySubject } from 'rxjs';
import { finalize, first, map, shareReplay, switchMap, takeUntil, tap } from 'rxjs/operators';
import { AccessService, Feature, StubAccessService } from '../../core/access';
import { ProductService } from '../../core/product.service';
import { Product } from '../../core/product/product';
import { CustomizeBusinessAppService } from '../../customize-business-app/customize-business-app.service';
import { AccountGroupAssociationsService } from '../account-group-associations.service';

import {
  BusinessCenterTab,
  businessCenterTabList,
  businessNavTabList,
  CUSTOMER_LIST_TAB,
  EXECUTIVE_REPORT_TAB,
  FILES_TAB,
  G<PERSON>DES_TAB,
  HOME_TAB,
  INVOICES_TAB,
  MEETING_SCHEDULER_TAB,
  MY_PRODUCTS_TAB,
  NEW_INBOX_TAB,
  ORDERS_TAB,
  PROJECTS_TAB,
  RECOMMENDATIONS_TAB,
  STORE_TAB,
} from './business-center-tab';

@Component({
  selector: 'app-product-permissions',
  styleUrls: ['./product-permissions.component.scss'],
  templateUrl: 'product-permissions.component.html',
  standalone: false,
})
export class ProductPermissionsComponent implements OnInit, OnDestroy {
  @Input()
  accountGroup: AccountGroup;
  @Input()
  userId: string;
  @Input()
  tabs: string[];
  @Input()
  permissions: string[];
  @Output()
  productPermissionsUpdated: EventEmitter<{
    accountGroupId: string;
    permissions: string[];
  }> = new EventEmitter();
  @Output()
  tabPermissionsUpdated: EventEmitter<{
    accountGroupId: string;
    tabIds: string[];
  }> = new EventEmitter();
  private _tabs: string[];
  myProductsTabId = MY_PRODUCTS_TAB;
  businessCenterTabs: BusinessCenterTab[];
  products: Product[];

  private changingProductPermissions: string[] = [];
  private productPermissions: string[] = [];

  loadingProducts = this.productService.isProductLoading;
  loadingBusinessCenterTabs = true;
  error = false;
  hideProducts = false;
  private destroyed$: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private productService: ProductService,
    public associationsService: AccountGroupAssociationsService,
    private ref: ChangeDetectorRef,
    private customizeBusinessApp: CustomizeBusinessAppService,
    private featureFlagService: FeatureFlagService,
    private whiteLabelService: WhitelabelService,
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    @Inject(StubAccessService) private accessService: AccessService,
  ) {}

  ngOnInit(): void {
    this._tabs = this.tabs ? this.tabs.slice() : businessCenterTabList.map((tab) => tab.id);

    const whitelabelConfig$ = this.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.whiteLabelService.getConfiguration(partnerId, this.accountGroup.externalIdentifiers.marketId);
      }),
      map((config) => {
        return config.businessCenterConfiguration;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const hasAccessToGuides$ = this.accessService.hasAccessToFeature(Feature.contentLibrary);
    const hasAccessToLocalMarketingIndex$ = this.accessService.hasAccessToFeature(Feature.localMarketingIndex);
    const hasAccessToExecutiveReport$ = this.accessService.hasAccessToFeature(Feature.executiveReport);
    const hasAccessToFeatureFlags$ = this.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.featureFlagService.batchGetStatus(partnerId, this.accountGroup.externalIdentifiers.marketId, [
          'vbc_dashboard',
          'business_navigation',
          'bc_new_inbox',
          'meeting_scheduler_business_app',
        ]);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    combineLatest([
      this.partnerId$,
      whitelabelConfig$,
      hasAccessToGuides$,
      hasAccessToLocalMarketingIndex$,
      hasAccessToExecutiveReport$,
      hasAccessToFeatureFlags$,
    ])
      .pipe(
        first(),
        takeUntil(this.destroyed$),
        map(
          ([
            partnerId,
            wlConfig,
            hasAccessToGuides,
            hasAccessToLocalMarketingIndex,
            hasAccessToExecutiveReport,
            accessToFeatureFlags,
          ]) => {
            const exclude: string[] = [];
            if (!hasAccessToLocalMarketingIndex) {
              exclude.push(RECOMMENDATIONS_TAB);
            }
            if (!accessToFeatureFlags.business_navigation) {
              exclude.push(...businessNavTabList.map((t) => t.id));
            }
            if (!accessToFeatureFlags.vbc_dashboard || !wlConfig.showDashboard) {
              exclude.push(HOME_TAB);
            }
            if (!accessToFeatureFlags.bc_new_inbox || !wlConfig.showInboxMessage) {
              exclude.push(NEW_INBOX_TAB);
            }
            if (!wlConfig.showCustomers) {
              exclude.push(CUSTOMER_LIST_TAB);
            }
            if (!hasAccessToExecutiveReport || !wlConfig.showExecutiveReport) {
              exclude.push(EXECUTIVE_REPORT_TAB);
            }
            if (!wlConfig.showMyProducts) {
              exclude.push(MY_PRODUCTS_TAB);
              this.hideProducts = true;
            }
            if (!wlConfig.showStore) {
              exclude.push(STORE_TAB);
            }
            if (!accessToFeatureFlags.meeting_scheduler_business_app || !wlConfig.meetingSchedulerBusinessApp) {
              exclude.push(MEETING_SCHEDULER_TAB);
            }
            if (!hasAccessToGuides || !wlConfig.showContentLibrary) {
              exclude.push(RECOMMENDATIONS_TAB);
              exclude.push(GUIDES_TAB);
            }
            if (!wlConfig.showFiles) {
              exclude.push(FILES_TAB);
            }
            if (!wlConfig.showOrderPage) {
              exclude.push(ORDERS_TAB);
            }
            if (!wlConfig.showFulfillment) {
              exclude.push(PROJECTS_TAB);
            }
            if (!wlConfig.showInvoices) {
              exclude.push(INVOICES_TAB);
            }
            // TODO: Remove this when we are wanting to release reccomendations to everyone. Currently only ABC can view it.
            if (partnerId !== 'ABC') {
              exclude.push(RECOMMENDATIONS_TAB);
            }
            return businessCenterTabList.filter((tab) => exclude.indexOf(tab.id) === -1);
          },
        ),
        finalize(() => (this.loadingBusinessCenterTabs = false)),
      )
      .subscribe(
        (tabs) => {
          this.businessCenterTabs = tabs;
          this.ref.markForCheck();
        },
        () => (this.error = true),
      );

    combineLatest([this.productService.products, whitelabelConfig$])
      .pipe(
        takeUntil(this.destroyed$),
        map(([products, wlConfig]) => {
          if (!wlConfig.showMyProducts || !products) {
            return [];
          } else {
            return products.filter(
              (product) => !!this.accountGroup?.accounts?.find((a) => a.marketplaceAppId === product.product_id),
            );
          }
        }),
      )
      .subscribe(
        (p) => {
          this.productPermissions = this.setAccess(p, this.permissions);
          this.products = p || [];
          this.ref.markForCheck();
        },
        () => (this.error = true),
      );
  }

  isDemo(productId: string): boolean {
    const account = this.accountGroup?.accounts?.find((a) => a.marketplaceAppId === productId);
    if (!account) {
      return false;
    }
    return !!account.isTrial;
  }

  tabEnabled(tabId: string): boolean {
    return this._tabs.indexOf(tabId) >= 0;
  }

  hasProductPermission(product: any): boolean {
    return this.productPermissions.indexOf(product.product_id) >= 0;
  }

  disableToggle(): boolean {
    return this.changingProductPermissions.length > 0;
  }

  private startChangingPermision(productId: string): void {
    if (this.changingProductPermissions.indexOf(productId) === -1) {
      this.changingProductPermissions.push(productId);
    }
  }

  private stopChangingPermission(productId: string): void {
    if (this.changingProductPermissions.indexOf(productId) >= 0) {
      this.changingProductPermissions.splice(this.changingProductPermissions.indexOf(productId), 1);
    }
  }

  private applyTabAccess$(permissions: string[], _tabId: string): Observable<void> {
    return this.associationsService
      .applyTabAccess$(
        this.accountGroup.accountGroupId,
        this.userId,
        permissions,
        businessCenterTabList.map((tab) => tab.id),
      )
      .pipe(
        first(),
        tap((tabs) => {
          this._tabs = permissions;
          this.tabPermissionsUpdated.emit({
            accountGroupId: this.accountGroup.accountGroupId,
            tabIds: tabs,
          });
        }),
      );
  }

  private applyProductPermission$(permissions: string[], _product: Product): Observable<void> {
    return this.associationsService
      .applyAccountAccess$(
        this.accountGroup.accountGroupId,
        this.userId,
        permissions,
        this.products.map((p) => p.product_id),
      )
      .pipe(
        first(),
        tap((p) => {
          this.productPermissions = permissions;
          this.productPermissionsUpdated.emit({
            accountGroupId: this.accountGroup.accountGroupId,
            permissions: p,
          });
          this.ref.markForCheck();
        }),
      );
  }

  changeProductPermission(state: boolean, product: Product): void {
    this.startChangingPermision(product.product_id);
    // Build the permission set we want to send, but it's only temporary right now because it could fail.
    const tempPermissions = this.productPermissions.slice();
    if (state && tempPermissions.indexOf(product.product_id) === -1) {
      tempPermissions.push(product.product_id);
    } else if (!state && tempPermissions.indexOf(product.product_id) >= 0) {
      tempPermissions.splice(tempPermissions.indexOf(product.product_id), 1);
    }
    this.applyProductPermission$(tempPermissions, product)
      .pipe(
        takeUntil(this.destroyed$),
        finalize(() => this.stopChangingPermission(product.product_id)),
      )
      .subscribe();
  }

  changeTabPermission(state: boolean, tabId: string): void {
    this.startChangingPermision(tabId);
    const tempPermissions = this._tabs.slice();
    if (state && tempPermissions.indexOf(tabId) === -1) {
      tempPermissions.push(tabId);
    } else if (!state && tempPermissions.indexOf(tabId) >= 0) {
      tempPermissions.splice(tempPermissions.indexOf(tabId), 1);
    }

    if (!state && tabId === MY_PRODUCTS_TAB && this.productPermissions.length > 0) {
      this.applyProductPermission$([], null)
        .pipe(
          takeUntil(this.destroyed$),
          switchMap(() => this.applyTabAccess$(tempPermissions, tabId)),
          finalize(() => this.stopChangingPermission(tabId)),
        )
        .subscribe();
    } else {
      this.applyTabAccess$(tempPermissions, tabId)
        .pipe(
          takeUntil(this.destroyed$),
          finalize(() => this.stopChangingPermission(tabId)),
        )
        .subscribe();
    }
  }

  private setAccess(products: Product[], access: string[]): string[] {
    if (access) {
      // This user has limit access already
      return access.slice();
    }
    // They have access to everything
    return products.map((p) => p.product_id);
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.complete();
  }
}
