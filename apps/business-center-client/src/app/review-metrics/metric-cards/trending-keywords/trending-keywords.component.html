<mat-card>
  <mat-card-header>
    <mat-card-title>{{ cardTitle }}</mat-card-title>
    <mat-icon
      [glxyTooltip]="'BRANDS.SENTIMENT_ANALYSIS.SENTIMENT_OVER_TIME.CARD.TOOLTIP' | translate"
      [highContrast]="false"
      class="info-icon"
    >
      info_outline
    </mat-icon>
  </mat-card-header>
  <mat-card-content>
    @if (trendingKeywords.length === 0) {
      <glxy-empty-state>
        <glxy-empty-state-hero><mat-icon>feedback</mat-icon></glxy-empty-state-hero>
        <glxy-empty-state-title>{{
          'PERFORMANCE.MULTI_LOCATION.REVIEWS.NO_KEYWORDS' | translate
        }}</glxy-empty-state-title>
      </glxy-empty-state>
    } @else {
      <div class="trending-keywords">
        @for (keyword of trendingKeywords; track keyword) {
          <glxy-badge
            [color]="
              keyword.sentiment === sentiment.Positive
                ? 'green'
                : keyword.sentiment === sentiment.Negative
                  ? 'red'
                  : 'light-grey'
            "
            class="chip"
          >
            <span class="text">{{ keyword.keyword }}</span>
            <span class="count">{{ keyword.totalCount }}</span>
          </glxy-badge>
        }
      </div>
    }
  </mat-card-content>
</mat-card>
