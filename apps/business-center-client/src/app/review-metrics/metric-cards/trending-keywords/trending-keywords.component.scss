@use 'design-tokens' as *;

:host {
  display: block;
}

mat-card {
  height: 100%;

  .mat-mdc-card-header {
    border: 0;
    justify-content: space-between;
  }

  mat-card-content {
    margin-top: 0;
    height: 100%;
    width: 100%;
  }
}

.info-icon {
  color: $icon-color;
  font-size: $font-preset-3-size;
  height: $spacing-3;
  width: $spacing-3;
  vertical-align: text-bottom;
  float: right;
}

.trending-keywords {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  .chip {
    margin-top: $spacing-2;
    margin-right: $spacing-2;
  }

  .text {
    color: $darker-gray;
  }

  .count {
    padding-left: $spacing-1;
    color: $dark-gray;
    font-weight: 500;
  }
}
