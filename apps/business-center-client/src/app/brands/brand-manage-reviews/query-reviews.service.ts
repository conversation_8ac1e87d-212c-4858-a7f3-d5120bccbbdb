import { Injectable } from '@angular/core';
import {
  AnalyticsApiService,
  CompositeFilter,
  CompositeFilterOperator,
  FieldFilter,
  FieldFilterOperator,
  FieldFilterOperatorFunction,
  Filter,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  MetricResult,
  Order,
  OrderBy,
  OrderByOrderColumn,
  PropertyType,
  QueryMetricsRequest,
  QueryMetricsResponse,
  TypedValue,
  UnaryFilter,
  UnaryFilterOperator,
} from '@vendasta/multi-location-analytics';
import {
  GetMultiThirdPartyReviewPublishSettingsRequest,
  GetMultiThirdPartyReviewPublishSettingsResponse,
  GetReviewsRequest,
  GetReviewsResponse,
  Review,
  ReviewApiService,
} from '@vendasta/reputation';
import {
  BehaviorSubject,
  Observable,
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  firstValueFrom,
  forkJoin,
  map,
  of,
  shareReplay,
  switchMap,
  take,
} from 'rxjs';
import { partnerId } from '../../../globals';
import { BrandContext, QueryService, inclusiveExclusive } from '../../metrics/query.service';
import {
  ManageReviewsFiltersService,
  ReviewFilters,
} from './manage-reviews/manage-reviews-filters/manage-reviews-filters.service';
import { ExportReviewsQuery } from '@vendasta/business-center';

export class ReviewIdentifier {
  constructor(
    public reviewId: string,
    public accountGroupId: string,
  ) {}
}

export interface ThirdPartyPublishSettings {
  widgetAllReviewsEnabled: boolean;
  thirdPartyMinimumPublishRating: number;
}

export interface ReviewsWithPublishSettings {
  reviews: Review[];
  publishSettings: Map<string, ThirdPartyPublishSettings>;
}

export interface Iter<T> {
  itemCount$: Observable<number>;
  loading$: Observable<boolean>;
  hasMore$: Observable<boolean>;
  next(): Observable<T>;
}

export class ReviewIter implements Iter<ReviewsWithPublishSettings> {
  private request: QueryMetricsRequest;
  private cursor$$ = new BehaviorSubject('');
  private allReviewIds = new Array<ReviewIdentifier>();
  private readonly hasMore$$ = new BehaviorSubject(true);
  private readonly loading$$ = new BehaviorSubject(false);
  hasMore$: Observable<boolean> = this.hasMore$$.asObservable();
  loading$: Observable<boolean> = this.loading$$.asObservable();
  cursor$: Observable<string> = this.cursor$$.asObservable();

  private loadedCount = 0;

  public readonly itemCount$: Observable<number>;

  private pageSize = 10;
  private isAllTimeRange = true;

  constructor(
    private qrs: QueryReviewsService,
    private brandContext: BrandContext,
    private reviewFilters: ReviewFilters,
    private pageSizeSpecified?: number,
    private isAllTimeRangeSpecified?: boolean,
  ) {
    this.pageSize = this.pageSizeSpecified || this.pageSize;
    this.isAllTimeRange = !!this.isAllTimeRangeSpecified;

    // Store the request for the brand context and filters, it's cursor will be updated as it's consumed
    this.request = this.qrs.buildReviewRequest(this.brandContext, this.reviewFilters, this.pageSize);
    this.itemCount$ = this.qrs.analyticsService
      .queryMetrics(this.qrs.buildCountRequest(this.brandContext, this.reviewFilters))
      .pipe(
        take(1),
        map((resp) => {
          return Number(resp.metricResults[0].metrics.metrics[0].measures[0]);
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

    this.itemCount$.subscribe();
  }

  next(): Observable<ReviewsWithPublishSettings> {
    return this.itemCount$.pipe(
      switchMap((totalCount) => {
        return this.loading$.pipe(
          filter((loading) => !loading),
          take(1),
          switchMap(() => combineLatest([this.hasMore$, this.cursor$])),
          take(1),
          switchMap(([hasMore, cursor]) => {
            this.loading$$.next(true);
            if (!hasMore) {
              return of(new Array<Review>());
            }
            if (cursor) {
              this.request.cursor = cursor;
            }
            return this.qrs.analyticsService.queryMetrics(this.request);
          }),
          map((r: QueryMetricsResponse): ReviewIdentifier[] => {
            if (!r) {
              this.hasMore$$.next(false);
              return null;
            }

            const loadedMetrics: MetricResult[] | null = r?.metricResults?.[0]?.metrics?.metrics;
            if (!loadedMetrics) {
              this.hasMore$$.next(false);
              this.cursor$$.next('');
              return [];
            }

            this.cursor$$.next(r?.metricResults?.[0]?.cursor);

            this.loadedCount += loadedMetrics.length;
            if (this.loadedCount >= totalCount || loadedMetrics.length === 0) {
              this.hasMore$$.next(false);
            }

            // Parse Review Identifiers to hydrate
            return loadedMetrics.map((value: MetricResult) => {
              return new ReviewIdentifier(value.measures[0], value.measures[1]);
            });
          }),
          switchMap((reviewIds: ReviewIdentifier[]) => {
            if (!reviewIds || reviewIds.length <= 0) {
              return of([null, null]);
            }
            this.allReviewIds.push(...reviewIds);
            const reviews$: Observable<GetReviewsResponse> = this.qrs.reviewService
              .getReviews(
                new GetReviewsRequest({
                  reviewIds: this.allReviewIds,
                  includeComments: true,
                }),
              )
              .pipe(catchError(() => of(null)));

            const publishSettings$: Observable<GetMultiThirdPartyReviewPublishSettingsResponse> = this.qrs.reviewService
              .getMultiThirdPartyReviewPublishSettings(
                new GetMultiThirdPartyReviewPublishSettingsRequest({
                  accountGroupIds: this.allReviewIds.map((id) => id.accountGroupId),
                }),
              )
              .pipe(catchError(() => of(null)));

            return forkJoin([reviews$, publishSettings$]);
          }),
          map(
            ([reviewResponse, publishSettingsResponse]: [
              GetReviewsResponse,
              GetMultiThirdPartyReviewPublishSettingsResponse,
            ]) => {
              this.loading$$.next(false);
              if (!reviewResponse) {
                return null;
              }
              if (reviewResponse && reviewResponse.reviews) {
                const publishSettingsMap = new Map<string, ThirdPartyPublishSettings>();
                if (publishSettingsResponse && publishSettingsResponse.thirdPartyReviewPublishSettings) {
                  for (const key in publishSettingsResponse.thirdPartyReviewPublishSettings) {
                    const value = publishSettingsResponse.thirdPartyReviewPublishSettings[key];
                    publishSettingsMap.set(key, {
                      widgetAllReviewsEnabled: value.widgetAllReviewsEnabled,
                      thirdPartyMinimumPublishRating: value.minimumPublishRating,
                    });
                  }
                }
                return {
                  reviews: reviewResponse.reviews,
                  publishSettings: publishSettingsMap,
                };
              }
              return {
                reviews: [],
                publishSettings: new Map<string, ThirdPartyPublishSettings>(),
              };
            },
          ),
          take(1),
          finalize(() => this.loading$$.next(false)),
        ) as Observable<ReviewsWithPublishSettings>;
      }),
    );
  }
}

@Injectable({ providedIn: 'root' })
export class QueryReviewsService {
  public iter$: Observable<Iter<ReviewsWithPublishSettings>>;
  private pageSize$$: BehaviorSubject<number> = new BehaviorSubject<number>(10);
  public pageSize$: Observable<number> = this.pageSize$$.asObservable();

  private isAllTimeRange$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  public isAllTimeRange$: Observable<boolean> = this.isAllTimeRange$$.asObservable();

  constructor(
    public analyticsService: AnalyticsApiService,
    public reviewService: ReviewApiService,
    public queryService: QueryService,
    private manageReviewsFiltersService: ManageReviewsFiltersService,
  ) {
    // Whenever the brand's context changes (topbar), or our filters change (sidebar), we produce a fresh iter
    this.iter$ = combineLatest([
      this.queryService.buildBrandContext$([
        'partnerId',
        'path',
        'dateRange',
        'timeRange',
        'activeSources',
        'selectedSourceIds',
        'filters',
      ]),
      this.manageReviewsFiltersService.reviewFilter$,
      this.pageSize$$,
    ]).pipe(
      debounceTime(10),
      distinctUntilChanged(),
      map(([brandContext, filters, pageSize]) => new ReviewIter(this, brandContext, filters, pageSize)),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  setPageSize(n: number): void {
    this.pageSize$$.next(n);
  }

  setIsAllTimeRange(isAllTimeRange: boolean): void {
    this.isAllTimeRange$$.next(isAllTimeRange);
  }

  async getExportReviewRequestQuery(): Promise<ExportReviewsQuery> {
    const brandContext = await firstValueFrom(this.queryService.brandsContext$);
    const filters = await firstValueFrom(this.manageReviewsFiltersService.reviewFilter$);
    return buildExportReviewsQuery(partnerId, brandContext, filters);
  }

  buildReviewRequest(brandContext: BrandContext, reviewFilters: ReviewFilters, pageSize: number): QueryMetricsRequest {
    const filters: Filter[] = [];
    filters.push(brandContext.buildActiveSourcesFilter());

    if (reviewFilters.starFilters && reviewFilters.starFilters.length > 0) {
      filters.push(
        new Filter({
          compositeFilter: new CompositeFilter({
            op: CompositeFilterOperator.OR,
            filters: reviewFilters.starFilters.map((star: string): Filter => {
              return new Filter({
                fieldFilter: new FieldFilter({
                  dimension: 'rating',
                  operator: FieldFilterOperator.EQUAL,
                  value: new TypedValue({
                    value: star,
                    valueType: PropertyType.PROPERTY_TYPE_STRING,
                  }),
                }),
              });
            }),
          }),
        }),
      );
    }

    if (reviewFilters.statusFilters && reviewFilters.statusFilters.length > 0) {
      filters.push(
        new Filter({
          compositeFilter: new CompositeFilter({
            op: CompositeFilterOperator.OR,
            filters: reviewFilters.statusFilters.map((status: string): Filter => {
              return new Filter({
                fieldFilter: new FieldFilter({
                  dimension: 'action_status',
                  operator: FieldFilterOperator.EQUAL,
                  value: new TypedValue({
                    value: status,
                    valueType: PropertyType.PROPERTY_TYPE_STRING,
                  }),
                }),
              });
            }),
          }),
        }),
      );
    }

    const nonDeletedReviews = new Filter({
      compositeFilter: new CompositeFilter({
        op: CompositeFilterOperator.OR,
        filters: [
          // The deleted_status field can be null or an empty string.
          new Filter({
            unaryFilter: new UnaryFilter({
              dimension: 'deleted_status',
              op: UnaryFilterOperator.IS_NULL,
            }),
          }),
          new Filter({
            fieldFilter: new FieldFilter({
              dimension: 'deleted_status',
              operator: FieldFilterOperator.EQUAL,
              operatorFunction: FieldFilterOperatorFunction.ANY,
              value: new TypedValue({
                value: ['user-kept', ''],
                valueType: PropertyType.PROPERTY_TYPE_STRING,
              }),
            }),
          }),
        ],
      }),
    });
    if (reviewFilters.changesFilters && reviewFilters.changesFilters.length > 0) {
      let changesFilters: Filter[] = [];
      const editedReviews = new Filter({
        compositeFilter: new CompositeFilter({
          op: CompositeFilterOperator.AND,
          filters: [
            // TODO - switch to use an unary filter with IS_NOT_NULL when updated to @vendasta/multi-location-analytics.
            new Filter({
              fieldFilter: new FieldFilter({
                dimension: 'edited',
                operator: FieldFilterOperator.GREATER_THAN_OR_EQUAL,
                value: new TypedValue({
                  value: new Date(0),
                  valueType: PropertyType.PROPERTY_TYPE_TIMESTAMP,
                }),
              }),
            }),
            nonDeletedReviews,
          ],
        }),
      });
      const deletedReviews = new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'deleted_status',
          operator: FieldFilterOperator.EQUAL,
          operatorFunction: FieldFilterOperatorFunction.ANY,
          value: new TypedValue({
            value: ['unspecified', 'user-removed'],
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      });

      if (reviewFilters.changesFilters.find((change) => change === 'edited-reviews')) {
        changesFilters = changesFilters.concat(editedReviews);
      }
      if (reviewFilters.changesFilters.find((change) => change === 'deleted-reviews')) {
        changesFilters = changesFilters.concat(deletedReviews);
      }
      filters.push(
        new Filter({
          compositeFilter: new CompositeFilter({
            op: CompositeFilterOperator.OR,
            filters: changesFilters,
          }),
        }),
      );
    } else {
      filters.push(nonDeletedReviews);
    }

    const [startDate, endDate] = brandContext.ReviewDateRange(true, true);

    return new QueryMetricsRequest({
      partnerId,
      metricName: 'reviews',
      resourceIds: brandContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          measure: 'review_id',
        }),
        new Measure({
          measure: 'account_group_id',
        }),
        new Measure({
          measure: 'published',
        }),
      ],
      orderBy: new OrderBy({
        orderBy: [
          new OrderByOrderColumn({
            column: 'published',
            order: Order.ORDER_DESC,
          }),
          new OrderByOrderColumn({
            column: 'review_id',
            order: Order.ORDER_DESC,
          }),
        ],
      }),
      filter: brandContext.buildFilter(filters),
      limit: pageSize,
    });
  }

  buildCountRequest(brandContext: BrandContext, reviewFilters: ReviewFilters): QueryMetricsRequest {
    const filters: Filter[] = [];

    // When side filters contains source, do not use brand's active sources
    if (reviewFilters.sourceIDsFilters && reviewFilters.sourceIDsFilters.length > 0) {
      filters.push(
        new Filter({
          compositeFilter: new CompositeFilter({
            op: CompositeFilterOperator.OR,
            filters: reviewFilters.sourceIDsFilters.map((sourceID: number): Filter => {
              return new Filter({
                fieldFilter: new FieldFilter({
                  dimension: 'source_id',
                  operator: FieldFilterOperator.EQUAL,
                  value: new TypedValue({
                    value: String(sourceID),
                    valueType: PropertyType.PROPERTY_TYPE_STRING,
                  }),
                }),
              });
            }),
          }),
        }),
      );
    } else {
      filters.push(brandContext.buildActiveSourcesFilter());
    }

    if (reviewFilters.starFilters && reviewFilters.starFilters.length > 0) {
      filters.push(
        new Filter({
          compositeFilter: new CompositeFilter({
            op: CompositeFilterOperator.OR,
            filters: reviewFilters.starFilters.map((star: string): Filter => {
              return new Filter({
                fieldFilter: new FieldFilter({
                  dimension: 'rating',
                  operator: FieldFilterOperator.EQUAL,
                  value: new TypedValue({
                    value: star,
                    valueType: PropertyType.PROPERTY_TYPE_STRING,
                  }),
                }),
              });
            }),
          }),
        }),
      );
    }

    if (reviewFilters.statusFilters && reviewFilters.statusFilters.length > 0) {
      filters.push(
        new Filter({
          compositeFilter: new CompositeFilter({
            op: CompositeFilterOperator.OR,
            filters: reviewFilters.statusFilters.map((status: string): Filter => {
              return new Filter({
                fieldFilter: new FieldFilter({
                  dimension: 'action_status',
                  operator: FieldFilterOperator.EQUAL,
                  value: new TypedValue({
                    value: status,
                    valueType: PropertyType.PROPERTY_TYPE_STRING,
                  }),
                }),
              });
            }),
          }),
        }),
      );
    }

    const [startDate, endDate] = brandContext.ReviewDateRange(true, true);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'reviews',
      resourceIds: brandContext.resourceIds,
      timeRange: inclusiveExclusive(startDate, endDate),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'review_id',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      orderBy: new OrderBy({
        orderBy: [
          new OrderByOrderColumn({
            column: 'published',
            order: Order.ORDER_DESC,
          }),
        ],
      }),
      filter: brandContext.buildFilter(filters),
    });
  }
}

export function buildExportReviewsQuery(
  partnerId: string,
  brandContext: BrandContext,
  filters: ReviewFilters,
): ExportReviewsQuery {
  const selectedSources =
    brandContext.selectedSourceIds.length > 0 ? brandContext.selectedSourceIds : Array.from(brandContext.sourceIdsSet);

  const [startAt, endAt] = brandContext.ReviewDateRange(true, true);

  const geographyFilters = brandContext.filters.find((f) =>
    f.compositeFilter?.filters?.find((f) =>
      ['account_group__city', 'account_group__country', 'account_group__state'].includes(f.fieldFilter?.dimension),
    ),
  )?.compositeFilter?.filters;

  const cityFilters = geographyFilters
    ?.filter((f) => f.fieldFilter?.dimension === 'account_group__city')
    .map((f) => f.fieldFilter?.value?.value);

  const stateFilters = geographyFilters
    ?.filter((f) => f.fieldFilter?.dimension === 'account_group__state')
    .map((f) => f.fieldFilter?.value?.value);

  const countryFilters = geographyFilters
    ?.filter((f) => f.fieldFilter?.dimension === 'account_group__country')
    .map((f) => f.fieldFilter?.value?.value);

  return {
    partnerId: partnerId,
    resourceIds: brandContext.resourceIds.flatMap((id) => id.groupId?.groupPathNodes || id.businessId?.businessId),
    sourceIds: selectedSources.map((source) => String(source)),
    ratingFilters: filters.starFilters,
    statusFilters: filters.statusFilters,
    changesFilters: filters.changesFilters,
    cityFilters: cityFilters,
    stateProvinceFilters: stateFilters,
    countryFilters: countryFilters,
    taxonomyFilters: brandContext.filters.find((f) => f.fieldFilter?.dimension === 'account_group__tax_ids')
      ?.fieldFilter.value.value,
    locationFilters: brandContext.filters.find((f) => f.fieldFilter?.dimension === 'account_group__account_group_id')
      ?.fieldFilter.value.value,
    startAt: startAt,
    endAt: endAt,
  } as ExportReviewsQuery;
}
