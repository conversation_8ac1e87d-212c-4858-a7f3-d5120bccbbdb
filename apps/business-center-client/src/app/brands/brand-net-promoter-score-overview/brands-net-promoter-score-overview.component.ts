import { AfterViewInit, Component, HostListener, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  filter,
  map,
  Observable,
  of,
  shareReplay,
  startWith,
} from 'rxjs';
import { AccountGroup, getLocation } from '../../account-group/account-group';
import { LocationsService } from '../../locations';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { BrandsService } from '../brands.service';
import { MultiLocationService } from '../multi-location.service';
import { BrandRow, MeasureValueMap, MetricColumn } from '../table/table.service';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { NavTabsComponent } from '@vendasta/business-nav';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TimeRangePickerComponent } from '../../shared/time-range-picker.component';
import { NetPromoterScoreService } from '../brands-net-promoter-score/net-promoter-score.service';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { BrandFilterContainerModule } from '../brand-filter-container/brand-filter-container.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { TableModule } from '../table/table.module';
import { MatInputModule } from '@angular/material/input';
import {
  NPSOverallScoreChartOptions,
  NpsRollingAverageComponent,
  NPSRollingAverageDataPoints,
  NPSScoreOverallData,
  NpsScoreService,
  StatisticDeltaComponent,
} from '@vendasta/reviews';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import { BrandNpsSidebarComponent } from './brand-nps-sidebar/brand-nps-sidebar.component';
import { BrandSidebarHeaderService } from '../brand-sidebar-header/brand-sidebar-header.service';
import { FeatureFlagService } from '@galaxy/partner';
import { catchError, distinctUntilChanged, take } from 'rxjs/operators';
import { partnerId } from '../../../globals';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { ChartComponent, NgApexchartsModule } from 'ng-apexcharts';
import { GalaxyPopoverModule, PopoverPositions } from '@vendasta/galaxy/popover';
import { MatCardModule, MatCardTitle } from '@angular/material/card';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

//Column keys
const NPS_SCORE = 'nps_score';
const NPS_BREAKDOWN = 'nps_breakdown';
const NPS_VOLUME = 'nps_volume';
const PROMOTERS = 'promoters';
const PASSIVES = 'passives';
const DETRACTORS = 'detractors';

interface NPSScoreData {
  chartData: NPSScoreOverallData;
  currentScore: number;
  difference: number;
  totalNPSRatings: number;
}

@Component({
  imports: [
    CommonModule,
    GalaxyPageModule,
    NavTabsComponent,
    TranslateModule,
    NpsRollingAverageComponent,
    BrandFilterContainerModule,
    GalaxyInfiniteScrollTriggerModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
    MatIconModule,
    MatButtonModule,
    TimeRangePickerComponent,
    CommonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTabsModule,
    ReactiveFormsModule,
    BrandFilterContainerModule,
    TimeRangePickerComponent,
    TableModule,
    ChartComponent,
    GalaxyPopoverModule,
    MatCardModule,
    MatCardTitle,
    MatButtonModule,
    MatIconModule,
    NgApexchartsModule,
    GalaxyPopoverModule,
    StatisticDeltaComponent,
  ],
  selector: 'bc-brand-nps-overview',
  templateUrl: './brands-net-promoter-score-overview.component.html',
  styleUrls: ['./brands-net-promoter-score-overview.component.scss', './../brands-common.component.scss'],
})
export class BrandsNetPromoterScoreOverviewComponent implements OnInit, OnDestroy, AfterViewInit {
  npsRollingAvgData$: Observable<NPSRollingAverageDataPoints[]>;
  ChartData$: Observable<NPSScoreData>;
  displayNPSExecutiveReport: boolean;

  public chartOptions$: Observable<NPSOverallScoreChartOptions>;

  showPopover = false;
  currentScore = 0;
  previousScore = 0;
  difference = 0;
  isChartDataAvailable = false; // Boolean to track if data is valid
  totalNPSRatings = 0; // Store total ratings
  PopoverPositions = PopoverPositions;
  private colors: string[] = ['#388E3C', '#F9A825', '#D32F2F'];

  activeChartIndex = 0; // Tracks which chart is visible when collapsed

  private npsService = inject(NetPromoterScoreService);

  multiLocationService = inject(MultiLocationService);
  private brandsService = inject(BrandsService);
  private accountGroupService = inject(AccountGroupMetricService);
  private fb = inject(FormBuilder);
  private locationsService = inject(LocationsService);
  private translateService = inject(TranslateService);
  private npsScoreService = inject(NpsScoreService);
  private sidepanelService = inject(SidepanelService);
  private brandSidebarHeaderService = inject(BrandSidebarHeaderService);
  private featureFlagService = inject(FeatureFlagService);
  private breakpointObserver = inject(BreakpointObserver);
  private readonly productAnalyticsService = inject(ProductAnalyticsService);

  mapLocations$: Observable<BrandRow[]>;
  tableData$: Observable<BrandRow[]>;

  form: FormGroup;

  search$: Observable<string>;

  gradesMeasure = NPS_SCORE;

  tableColumns: MetricColumn[] = [
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_SCORE',
      measureKey: NPS_SCORE,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_SCORE',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_BREAKDOWN',
      thermometerMetadata: {
        seriesKey: NPS_BREAKDOWN,
        colors: ['#D33030', '#FFC108', '#2F7E33'],
        tooltips: ['Detractors', 'Passives', 'Promoters'],
      },
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_VOLUME',
      measureKey: NPS_VOLUME,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_VOLUME',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PROMOTERS',
      measureKey: PROMOTERS,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PROMOTERS',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PASSIVES',
      measureKey: PASSIVES,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PASSIVES',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.DETRACTORS',
      measureKey: DETRACTORS,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.DETRACTORS',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'mean',
    },
  ];

  private windowWidth$$: BehaviorSubject<number> = new BehaviorSubject(window.innerWidth);
  private windowWidth$: Observable<number> = this.windowWidth$$.asObservable().pipe(distinctUntilChanged());
  chartDataWithRollingAvg$: Observable<{ chartData: NPSScoreData; rollingAvgData: NPSRollingAverageDataPoints[] }>;

  constructor() {
    this.productAnalyticsService.trackEvent('ml-reputation-nps-button-clicked', '', 'click');
    this.initializeFeatureFlags();
    this.initializeMapLocations();
    this.mountForm();
  }

  initializeFeatureFlags() {
    this.featureFlagService
      .batchGetStatus(partnerId, '', ['ml_nps_executive_report'])
      .pipe(
        map((resp) => resp['ml_nps_executive_report']),
        catchError(() => of(false)),
        take(1),
      )
      .subscribe((showExecutiveReport) => {
        this.displayNPSExecutiveReport = showExecutiveReport;
      });
  }

  readonly shouldCollapseCards$ = combineLatest([
    this.sidepanelService.visible$,
    this.windowWidth$.pipe(distinctUntilChanged()),
    this.breakpointObserver.observe([Breakpoints.XSmall, Breakpoints.Small]).pipe(map((bp) => bp.matches)),
  ]).pipe(
    map(([sidepanelVisible, windowWidth, isSmallScreen]) => {
      if (isSmallScreen) return true;

      // Calculate available space
      const sidePanelOffset = sidepanelVisible ? this.sidepanelService.getPixels() : 0;
      return windowWidth - 275 - sidePanelOffset < 940;
    }),
  );

  toggleChart(index: number): void {
    this.activeChartIndex = index;
  }

  @HostListener('window:resize')
  onResize(): void {
    this.windowWidth$$.next(window.innerWidth);
  }

  openNPSDetailsDrawer(accountGroupId: string): void {
    this.brandSidebarHeaderService.setResource(accountGroupId);
    this.sidepanelService.open();
  }

  ngOnInit() {
    // Get the data for the chart
    this.ChartData$ = this.npsService.getMetricsData().pipe(
      map((chartData) => {
        this.calculateScores(chartData);
        return {
          chartData: chartData,
          currentScore: this.currentScore,
          difference: this.difference,
          totalNPSRatings: this.totalNPSRatings,
        };
      }),
      shareReplay(1),
    );
    this.npsRollingAvgData$ = this.npsService.getMetricsDataRollingAvg().pipe(
      map((dataPoints) =>
        dataPoints.sort((a, b) => a.netPromoterScoreDate.getTime() - b.netPromoterScoreDate.getTime()),
      ),
      shareReplay(1),
    );

    // Combine both observables into a single one
    this.chartDataWithRollingAvg$ = combineLatest([this.ChartData$, this.npsRollingAvgData$]).pipe(
      map(([chartData, rollingAvgData]) => ({ chartData, rollingAvgData })),
    );

    this.prepareTableData();
    this.chartOptions$ = this.initializeChartOptions();
  }

  ngAfterViewInit(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.SIDE, BrandNpsSidebarComponent);
  }

  ngOnDestroy(): void {
    this.sidepanelService.clearView();
    this.sidepanelService.close();
  }

  mountForm(): void {
    this.form = this.fb.group({
      comparison: new FormControl<string>('TABLE.NODE_LOCATION', Validators.required),
      search: new FormControl<string>(''),
    });

    this.search$ = this.form.valueChanges.pipe(
      map((changes) => changes['search']),
      startWith(''),
    );
  }

  prepareTableData(): void {
    this.tableData$ = combineLatest([this.mapLocations$, this.search$]).pipe(
      map(([tableData, search]) => {
        return tableData.filter((row) => {
          if (row == null) {
            return true;
          }
          if (row.subtitle) {
            return (
              row.title.toUpperCase().includes(search.toUpperCase()) ||
              row.subtitle.toUpperCase().includes(search.toUpperCase())
            );
          }
          return row.title.toUpperCase().includes(search.toUpperCase());
        });
      }),
      map((tableData) => {
        return tableData.map((row) => {
          if (!row) {
            return;
          }
          const npsClassifiedRating = [
            row.measureMap[DETRACTORS].value, // Negative
            row.measureMap[PASSIVES].value, // Neutral
            row.measureMap[PROMOTERS].value, // Positive
          ];
          const npsClassifiedRatingSum = npsClassifiedRating.reduce((a, b) => a + b, 0);
          const npsNormalizedRating = npsClassifiedRating.map((value) => (value / npsClassifiedRatingSum) * 100);
          row.seriesMap = {
            nps_breakdown: {
              values: npsClassifiedRating,
              normalized: npsNormalizedRating,
            },
          };
          return row;
        });
      }),
    );
  }

  private initializeChartOptions(): Observable<NPSOverallScoreChartOptions> {
    const translationStream$ = this.translateService.stream([
      'NPS_OVERALL_CHART.NPS_PROMOTER',
      'NPS_OVERALL_CHART.NPS_PASSIVE',
      'NPS_OVERALL_CHART.NPS_DETRACTOR',
    ]);

    return combineLatest([translationStream$, this.ChartData$]).pipe(
      map(([translations, overallData]) => {
        const promoters = overallData.chartData.current.promoters;
        const passives = overallData.chartData.current.passives;
        const detractors = overallData.chartData.current.detractors;
        const total = promoters + passives + detractors;
        const percentages = [
          total ? Math.round((promoters / total) * 100) : 0,
          total ? Math.round((passives / total) * 100) : 0,
          total ? Math.round((detractors / total) * 100) : 0,
        ];
        const individualScore = [promoters, passives, detractors];
        return this.getOverallScoreChartOptions(percentages, individualScore, translations);
      }),
      shareReplay(1),
    );
  }

  private getOverallScoreChartOptions(
    percentages: number[],
    individualScore: number[],
    translations,
  ): NPSOverallScoreChartOptions {
    return {
      series: [
        {
          name: 'Ratings',
          data: percentages,
        },
      ],
      chart: {
        height: 100,
        width: '100%',
        type: 'bar',
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
      },
      legend: {
        show: true,
        position: 'right',
        itemMargin: {
          vertical: 5,
        },
        formatter: (seriesName: string, opts) => {
          const percentage = percentages[opts.seriesIndex];
          const score = individualScore[opts.seriesIndex];
          return `
        <div style="display: flex; align-items: center; justify-content: space-between;gap: 6px">
          <span style="font-size: 12px;font-weight:400;color: #616161;min-width: 22px;text-align: right">${score}</span>
          <span style="width: 2px; height: 12px; background-color: #E0E0E0FF;"></span>
          <span style="font-size: 12px;font-weight:400;color: #616161">${percentage}%</span>
        </div>
      `;
        },
        onItemHover: {
          highlightDataSeries: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: true,
          columnWidth: '100%',
          barHeight: '16px',
          distributed: true,
          colors: {
            backgroundBarColors: ['#F5F5F5FF'],
            backgroundBarOpacity: 0.8,
          },
        },
      },
      tooltip: {
        enabled: false,
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: 'smooth',
      },
      grid: {
        padding: {
          top: -25,
        },
        xaxis: {
          lines: {
            show: false,
          },
        },
        yaxis: {
          lines: {
            show: false,
          },
        },
      },
      yaxis: {
        labels: {
          show: true,
          style: {
            colors: ['#616161FF'],
            fontSize: '14px',
          },
        },
        axisTicks: {
          show: false,
        },
        axisBorder: {
          show: false,
        },
      },
      xaxis: {
        categories: [
          translations['NPS_OVERALL_CHART.NPS_PROMOTER'],
          translations['NPS_OVERALL_CHART.NPS_PASSIVE'],
          translations['NPS_OVERALL_CHART.NPS_DETRACTOR'],
        ],
        labels: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        axisBorder: {
          show: false,
        },
        min: 0,
        max: 100,
      },
      color: this.colors,
    };
  }

  calculateScores(chartData: NPSScoreOverallData) {
    const hasPreviousNPS = chartData.previous.promoters + chartData.previous.passives + chartData.previous.detractors;
    this.currentScore = this.npsScoreService.calculateCategoryAndOverallScore(chartData.current);
    this.previousScore = this.npsScoreService.calculateCategoryAndOverallScore(chartData.previous);
    this.difference = hasPreviousNPS ? this.currentScore - this.previousScore : 0; // Calculate the difference

    // Set the boolean flag to true if the condition is satisfied
    this.isChartDataAvailable =
      chartData.current.promoters > 0 || chartData.current.passives > 0 || chartData.current.detractors > 0;

    // Calculate total ratings and store in totalRatings variable
    this.totalNPSRatings = chartData.current.promoters + chartData.current.passives + chartData.current.detractors;
  }

  showPopovers(): void {
    this.showPopover = true;
  }

  hidePopovers(): void {
    this.showPopover = false;
  }

  private initializeMapLocations() {
    // Build measure value map for nps
    const locationMeasureMap$ = combineLatest([
      this.locationsService.currentAccountGroupIds$,
      this.npsService.currentByLocation$,
      this.npsService.previousByLocation$,
    ]).pipe(
      debounceTime(10),
      map(([accountGroupIds, currentNpsData, previousNpsData]) => {
        const measureMap = {};
        if (accountGroupIds == null || currentNpsData == null) {
          return measureMap;
        }
        accountGroupIds.forEach((accountGroupId) => {
          // Default value all locations
          measureMap[accountGroupId] = createEmptyMetricMap();
        });
        currentNpsData.forEach((npsData) => {
          // Set value for all locations in stats
          if (!measureMap[npsData.AccountGroupID]) {
            return;
          }
          measureMap[npsData.AccountGroupID].nps_score.value = npsData.NPSScore;
          measureMap[npsData.AccountGroupID].nps_volume.value = npsData.NPSVolume;
          measureMap[npsData.AccountGroupID].promoters.value = npsData.Promoters;
          measureMap[npsData.AccountGroupID].passives.value = npsData.Passives;
          measureMap[npsData.AccountGroupID].detractors.value = npsData.Detractors;
        });
        if (previousNpsData) {
          previousNpsData.forEach((pNpsData) => {
            if (!measureMap[pNpsData.AccountGroupID]) {
              return;
            }
            measureMap[pNpsData.AccountGroupID][NPS_SCORE].deltaAbs =
              (measureMap[pNpsData.AccountGroupID][NPS_SCORE].value || 0) - (pNpsData.NPSScore || 0);
            measureMap[pNpsData.AccountGroupID][NPS_VOLUME].deltaAbs =
              (measureMap[pNpsData.AccountGroupID][NPS_VOLUME].value || 0) - (pNpsData.NPSVolume || 0);
            measureMap[pNpsData.AccountGroupID][PROMOTERS].deltaAbs =
              (measureMap[pNpsData.AccountGroupID][PROMOTERS].value || 0) - (pNpsData.Promoters || 0);
            measureMap[pNpsData.AccountGroupID][PASSIVES].deltaAbs =
              (measureMap[pNpsData.AccountGroupID][PASSIVES].value || 0) - (pNpsData.Passives || 0);
            measureMap[pNpsData.AccountGroupID][DETRACTORS].deltaAbs =
              (measureMap[pNpsData.AccountGroupID][DETRACTORS].value || 0) - (pNpsData.Detractors || 0);
          });
        }
        return measureMap;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const compareGroupSwitchMap: Record<string, Observable<BrandRow[]>> = {
      account_group_id: this.accountGroupService.filteredLocationsForPath$.pipe(
        map((locations) => {
          if (locations == null) {
            return null;
          }
          return Object.values(locations).map((ag: AccountGroup) => {
            return {
              accountGroup: ag,
              title: ag.companyName,
              subtitle: getLocation(ag),
            };
          });
        }),
      ),
      region: this.brandsService.childGroups$.pipe(
        map((regions) => {
          if (regions == null) {
            return null;
          }
          return regions.map((region) => {
            return {
              group: region,
              title: region.name,
            };
          });
        }),
      ),
    };
    const getExpectedGroupCopy$ = (groupBy: string) => {
      return compareGroupSwitchMap[groupBy].pipe(map((brandRows) => (brandRows ? [...brandRows] : brandRows)));
    };

    // Progressively loaded data for map/table. Location data, then measures/grades
    this.mapLocations$ = combineLatest([getExpectedGroupCopy$('account_group_id'), locationMeasureMap$]).pipe(
      filter((locationBrandRows) => !!locationBrandRows),
      map(([locationBrandRows, measures]) => {
        if (locationBrandRows == null) {
          return null;
        }
        return locationBrandRows.map((brandRow: BrandRow) => {
          brandRow.measureMap = (measures || {})[brandRow.accountGroup.accountGroupId] || createEmptyMetricMap();
          return brandRow;
        });
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }
}

function createEmptyMetricMap(): MeasureValueMap {
  return {
    nps_score: { value: null, deltaAbs: null },
    nps_volume: { value: 0, deltaAbs: null },
    promoters: { value: 0, deltaAbs: null },
    passives: { value: 0, deltaAbs: null },
    detractors: { value: 0, deltaAbs: null },
  };
}
