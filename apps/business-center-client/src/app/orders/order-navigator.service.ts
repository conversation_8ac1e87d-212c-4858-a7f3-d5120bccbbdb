import { map, shareReplay, startWith, take } from 'rxjs/operators';
import { combineLatest, Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { QueryParamsHandling } from '@angular/router';
import { OrderParamService } from './order-param.service';
import { AccountGroupService } from '../account-group';
import { OrderService } from './order.service';
import { ShoppingCartService } from '../shopping-cart/shopping-cart.service';

interface NavigationLink {
  url: string;
  navOptions?: NavigationExtras;
  windowNavigation?: boolean;
}

export enum OrderStep {
  Enter,
  CreateOrderFormPage,
  DynamicOrderFormPage,
  CreateConfirmationPage,
  ApproveOrderFormPage,
  ConfirmationPage,
  Submitted,
  Declined,
  TrialSubmitted,
}

export const orderSubmittedQueryParam = 'orderSubmitted';
export const trialOrderSubmittedQueryParam = 'trialOrderSubmitted';
export const orderDeclinedQueryParam = 'orderDeclined';
export const contactSalesQueryParam = 'orderContact';

@Injectable()
export class OrderNavigator {
  navigationMap$: Observable<Map<OrderStep, NavigationLink>>;
  private hasCustomOrderForm = false;

  constructor(
    private orderService: OrderService,
    private orderParamService: OrderParamService,
    private accountGroupService: AccountGroupService,
    private shoppingCartService: ShoppingCartService,
    private router: Router,
  ) {
    const orderId$ = this.orderParamService.orderId$.pipe(startWith(''));
    const nextUrl$ = this.orderParamService.nextUrl$.pipe(startWith(''));
    this.navigationMap$ = combineLatest([this.accountGroupService.currentAccountGroupId$, orderId$, nextUrl$]).pipe(
      map(([agid, orderId, nextUrl]) => {
        return new Map([
          [
            OrderStep.Enter,
            {
              url: nextUrl ? nextUrl : `/account/location/${agid}/store`,
              windowNavigation: !!nextUrl,
            },
          ],
          [
            OrderStep.CreateOrderFormPage,
            {
              url: `/account/location/${agid}/orders/create`,
              navOptions: {
                queryParamsHandling: 'preserve' as QueryParamsHandling,
              },
            },
          ],
          [
            OrderStep.DynamicOrderFormPage,
            {
              url: `/account/location/${agid}/orders/create/dynamic-order-form`,
              navOptions: {
                queryParamsHandling: 'preserve' as QueryParamsHandling,
              },
            },
          ],
          [
            OrderStep.CreateConfirmationPage,
            {
              url: `/account/location/${agid}/orders/create/confirmation`,
              navOptions: {
                queryParamsHandling: 'preserve' as QueryParamsHandling,
              },
            },
          ],
          [
            OrderStep.ApproveOrderFormPage,
            {
              url: `/account/location/${agid}/orders/${orderId}/approve`,
              navOptions: {
                queryParamsHandling: 'preserve' as QueryParamsHandling,
              },
            },
          ],
          [
            OrderStep.ConfirmationPage,
            {
              url: `/account/location/${agid}/orders/${orderId}/confirmation`,
              navOptions: {
                queryParamsHandling: 'preserve' as QueryParamsHandling,
              },
            },
          ],
          [
            OrderStep.Submitted,
            {
              url: `/account/location/${agid}/dashboard`,
              navOptions: {
                queryParams: {
                  nextUrl: nextUrl,
                  [orderSubmittedQueryParam]: true,
                },
              },
            },
          ],
          [
            OrderStep.TrialSubmitted,
            {
              url: `/account/location/${agid}/dashboard`,
              navOptions: {
                queryParams: {
                  nextUrl: nextUrl,
                  [trialOrderSubmittedQueryParam]: true,
                },
              },
            },
          ],
          [
            OrderStep.Declined,
            {
              url: `/account/location/${agid}/dashboard`,
              navOptions: {
                queryParams: {
                  nextUrl: nextUrl,
                  [orderDeclinedQueryParam]: true,
                },
              },
            },
          ],
        ]);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  static getNextNavigationStep(currentStep: OrderStep): OrderStep {
    switch (currentStep) {
      case OrderStep.CreateOrderFormPage:
        return OrderStep.CreateConfirmationPage;
      case OrderStep.DynamicOrderFormPage:
        return OrderStep.CreateConfirmationPage;
      case OrderStep.CreateConfirmationPage:
        return OrderStep.Submitted;
      case OrderStep.ApproveOrderFormPage:
        return OrderStep.ConfirmationPage;
      case OrderStep.ConfirmationPage:
        return OrderStep.Submitted;
    }
    return undefined;
  }

  private getPreviousNavigationStep(currentStep: OrderStep): OrderStep {
    switch (currentStep) {
      case OrderStep.CreateOrderFormPage:
        return OrderStep.Enter;
      case OrderStep.DynamicOrderFormPage:
        return OrderStep.Enter;
      case OrderStep.CreateConfirmationPage:
        if (this.hasCustomOrderForm) {
          return OrderStep.DynamicOrderFormPage;
        }
        return OrderStep.CreateOrderFormPage;
      case OrderStep.ApproveOrderFormPage:
        return OrderStep.Enter; // TODO: Go to Order List page when we build that?
      case OrderStep.ConfirmationPage:
        return OrderStep.ApproveOrderFormPage;
    }
    return undefined;
  }

  private getNavigateToStepObservable(step: OrderStep): Observable<void> {
    return combineLatest([
      this.orderParamService.isCartOrder$,
      this.navigationMap$,
      this.orderParamService.useOrderForm$,
      this.accountGroupService.currentAccountGroupId$,
    ]).pipe(
      take(1),
      map(([isCart, navigationMap, useOrderForm, _agid]) => {
        if (isCart && (step === OrderStep.Submitted || step === OrderStep.TrialSubmitted)) {
          this.shoppingCartService.clear();
        }

        if (step === OrderStep.CreateOrderFormPage && !useOrderForm) {
          step = OrderStep.Enter;
        }
        const navLink = navigationMap.get(step);
        if (navLink.windowNavigation) {
          window.location.assign(navLink.url);
        } else {
          this.router.navigate([navLink.url], navLink.navOptions);
        }
      }),
    );
  }

  navigateToStep(step: OrderStep): void {
    this.getNavigateToStepObservable(step).subscribe();
  }

  navigateToNextStep(currentStep: OrderStep): void {
    if (currentStep === OrderStep.DynamicOrderFormPage) {
      this.hasCustomOrderForm = true;
    }
    const nextStep = OrderNavigator.getNextNavigationStep(currentStep);
    this.getNavigateToStepObservable(nextStep).pipe(take(1)).subscribe();
  }

  navigateToPreviousStep(currentStep: OrderStep): void {
    const prevStep = this.getPreviousNavigationStep(currentStep);
    this.getNavigateToStepObservable(prevStep).pipe(take(1)).subscribe();
  }
}
