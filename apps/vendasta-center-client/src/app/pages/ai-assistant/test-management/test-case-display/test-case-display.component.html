<mat-card>
  <mat-card-header>
    <mat-card-title> Uploaded Test Cases</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <glxy-table-container
      class="simple-table"
      [dataSource]="dataSource"
      [columns]="columns"
      [pageSizeOptions]="[10, 20, 30]"
      [pageSize]="10"
      [border]="false"
      [fullWidth]="false"
    >
      <glxy-table-content-header
        showFilters="false"
        showFiltersButton="false"
        showSort="false"
      ></glxy-table-content-header>
      <table mat-table matSort (matSortChange)="sortData($event)">
        <tr mat-header-row *matHeaderRowDef="[]"></tr>

        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <glxy-table-selection />
          </th>
          <td mat-cell *matCellDef="let row">
            <glxy-table-selection [row]="row" />
          </td>
        </ng-container>

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
          <td mat-cell *matCellDef="let element">{{ element.name }}</td>
        </ng-container>

        <ng-container matColumnDef="prompt">
          <th mat-header-cell *matHeaderCellDef>Prompt</th>
          <td mat-cell *matCellDef="let element">
            @if (element.chatHistory?.length === 1) {
              {{ element.chatHistory[0].content }}
            } @else if (element.chatHistory?.length > 1) {
              Multiple
            }
          </td>
        </ng-container>

        <ng-container matColumnDef="expectation">
          <th mat-header-cell *matHeaderCellDef>Expectation</th>
          <td mat-cell *matCellDef="let element">{{ element.expectation }}</td>
        </ng-container>

        <ng-container matColumnDef="edit">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let element">
            <button (click)="editTestCase(element)" mat-icon-button>
              <mat-icon>edit</mat-icon>
            </button>
          </td>
        </ng-container>

        <ng-container matColumnDef="delete">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let element">
            <button (click)="deleteTestCase(element)" mat-icon-button>
              <mat-icon>delete</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-row *matRowDef="let row; columns: []"></tr>
      </table>
    </glxy-table-container>
  </mat-card-content>
  <mat-card-actions>
    <button mat-button color="primary" (click)="editTestCase(null)">+ Create Test Case</button>
  </mat-card-actions>
</mat-card>
