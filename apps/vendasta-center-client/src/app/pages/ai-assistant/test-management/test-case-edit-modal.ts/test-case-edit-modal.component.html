@if (data?.testCase?.id) {
  <h2 mat-dialog-title>Edit Test Case</h2>
} @else {
  <h2 mat-dialog-title>Create Test Case</h2>
}
<mat-dialog-content>
  <glxy-form-field required="true">
    <glxy-label>Test Name</glxy-label>
    <input placeholder="Name of the test" [formControl]="formGroup.get('name')" type="text" matInput />
    @if (formGroup.get('name').hasError('required')) {
      <glxy-error>Test name is required</glxy-error>
    }
  </glxy-form-field>

  <h3>Conversation</h3>
  @for (chat of chatHistory.controls; track chat; let index = $index) {
    <div class="prompt">
      <glxy-form-field class="role" required="true" bottomSpacing="small">
        <glxy-label>Role</glxy-label>
        <mat-select [formControl]="chat.get('role')">
          <mat-option [value]="chatRoles.CHAT_MESSAGE_ROLE_USER">User</mat-option>
          <mat-option [value]="chatRoles.CHAT_MESSAGE_ROLE_ASSISTANT">Assistant</mat-option>
        </mat-select>
      </glxy-form-field>
      <glxy-form-field required="true" class="content" bottomSpacing="small">
        <glxy-label>Content</glxy-label>
        <input placeholder="Chat message" [formControl]="chat.get('content')" type="text" matInput />
        @if (chat.get('content').hasError('required')) {
          @if (chat.get('role')?.value === chatRoles.CHAT_MESSAGE_ROLE_USER) {
            <glxy-error>Required: The user's message to the AI</glxy-error>
          } @else {
            <glxy-error>Required: The AI's response to the user</glxy-error>
          }
        }
      </glxy-form-field>
      @if (index > 0) {
        <button (click)="removeChatHistory(index)" mat-icon-button><mat-icon>close</mat-icon></button>
      }
    </div>
  }
  @if (chatHistory.length < 3) {
    <button class="add-button" color="primary" (click)="addChatHistory()" mat-button>+ Add chat</button>
  }
  <glxy-form-field>
    <glxy-label>Expectation</glxy-label>
    <textarea
      placeholder="Assistant's response should..."
      matInput
      [formControl]="formGroup.get('expectation')"
    ></textarea>
    @if (formGroup.get('expectation').hasError('required')) {
      <glxy-error>Required: Description of what the AI's response should include</glxy-error>
    }
  </glxy-form-field>
</mat-dialog-content>

<mat-dialog-actions>
  <button mat-stroked-button (click)="close()">Close</button>
  <button mat-raised-button color="primary" (click)="save()">Save</button>
</mat-dialog-actions>
