import { Component, inject, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogActions,
  MatDialogContent,
  MatDialogRef,
  MatDialogTitle,
} from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { ChatMessageRole, TestCase } from '@vendasta/ai-assistants';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ChatMessage } from '@vendasta/ai-assistants/lib/_internal/objects/answer';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-test-case-edit-modal',
  imports: [
    CommonModule,
    GalaxyFormFieldModule,
    GalaxyUploaderModule,
    MatButtonModule,
    MatIconModule,
    MatDialogActions,
    MatDialogContent,
    MatDialogTitle,
    ReactiveFormsModule,
    MatSelectModule,
    MatInputModule,
  ],
  standalone: true,
  templateUrl: './test-case-edit-modal.component.html',
  styleUrl: './test-case-edit-modal.component.scss',
})
export class TestCaseEditModalComponent implements OnInit {
  protected data = inject(MAT_DIALOG_DATA);
  private readonly dialogRef = inject(MatDialogRef<TestCaseEditModalComponent, TestCase>);
  formGroup: FormGroup;
  chatHistory: FormArray;

  chatRoles = ChatMessageRole;

  ngOnInit() {
    const testCase: TestCase = this?.data?.testCase;
    this.formGroup = new FormGroup({
      id: new FormControl(testCase.id),
      name: new FormControl(testCase.name, [Validators.required]),
      expectation: new FormControl(testCase.expectation, [Validators.required]),
    });
    this.chatHistory = new FormArray(
      testCase.chatHistory.map((message: ChatMessage) => {
        return new FormGroup({
          role: new FormControl(message.role.valueOf(), [Validators.required]),
          content: new FormControl(message.content, [Validators.required]),
        });
      }),
    );
    return;
  }

  addChatHistory(): void {
    this.chatHistory.push(
      new FormGroup({
        role: new FormControl(ChatMessageRole.CHAT_MESSAGE_ROLE_USER.valueOf(), [Validators.required]),
        content: new FormControl('', [Validators.required]),
      }),
    );
  }

  removeChatHistory(index: number): void {
    this.chatHistory.removeAt(index);
  }

  close(): void {
    this.dialogRef.close();
  }

  save(): void {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }
    if (this.chatHistory.invalid) {
      this.chatHistory.markAllAsTouched();
      return;
    }
    const testCase: TestCase = new TestCase({
      id: this.formGroup.get('id').value,
      name: this.formGroup.get('name').value,
      chatHistory: this.chatHistory.value.map((message: ChatMessage) => {
        return {
          role: message.role,
          content: message.content,
        };
      }),
      expectation: this.formGroup.get('expectation').value,
    });
    this.dialogRef.close(testCase);
  }
}
