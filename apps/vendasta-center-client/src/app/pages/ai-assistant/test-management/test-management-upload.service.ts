import { Injectable } from '@angular/core';
import { FileInfo, GalaxyUploaderService, UploadResponse } from '@vendasta/galaxy/uploader';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UploadService extends GalaxyUploaderService {
  // No-op uploading the file since we parse the rows ourselves in the modal
  buildRequest(_: FileInfo): Observable<UploadResponse> {
    return of({
      data: {
        url: '',
      },
    });
  }
}
