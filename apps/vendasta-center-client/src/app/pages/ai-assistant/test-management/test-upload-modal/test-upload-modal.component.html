<h2 mat-dialog-title>Upload Test Cases</h2>

<mat-dialog-content>
  <glxy-form-field>
    <glxy-uploader #glxyUploader [maxFiles]="1" [accept]="'.csv'" (fileUploaded)="onFileSelected($event)" />
    <glxy-uploader-list [files]="(glxyUploader.filesChanged | async) || []" />

    @if (testCases.length > 0) {
      <h3>{{ testCases.length }} test cases found</h3>
    }
    @if (errors.length > 0) {
      <h3>{{ errors.length }} errors found</h3>
    }
    @for (error of errors; track error) {
      <glxy-error>{{ error.message }}</glxy-error>
    }
  </glxy-form-field>
  <glxy-form-field>
    <mat-checkbox [(ngModel)]="removeExisting">Replace existing test cases</mat-checkbox>
  </glxy-form-field>
</mat-dialog-content>

<mat-dialog-actions>
  <button mat-stroked-button (click)="close()">Close</button>
  <button mat-raised-button color="primary" (click)="save()">Save</button>
</mat-dialog-actions>
