import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';
import { MatCardModule } from '@angular/material/card';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { FileInfo, GALAXY_UPLOADER_SERVICE_TOKEN, GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { UploadService } from '../test-management-upload.service';
import { Papa } from 'ngx-papaparse';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { ChatMessage, ChatMessageRole, TestCase } from '@vendasta/ai-assistants';
import { MatCheckboxModule } from '@angular/material/checkbox';

export interface RowError {
  rowIndex: number;
  message: string;
}

export interface TestUploadModalResult {
  cases: TestCase[];
  removeExisting: boolean;
}

@Component({
  selector: 'app-test-upload-modal',
  imports: [
    MatButtonModule,
    MatDialogModule,
    CommonModule,
    GalaxyPageModule,
    GalaxyStickyFooterModule,
    MatCardModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    GalaxyUploaderModule,
    FormsModule,
    MatCheckboxModule,
  ],
  standalone: true,
  providers: [UploadService, { provide: GALAXY_UPLOADER_SERVICE_TOKEN, useClass: UploadService }],
  templateUrl: './test-upload-modal.component.html',
  styleUrl: './test-upload-modal.component.scss',
})
export class TestUploadModalComponent {
  testCases: TestCase[] = [];
  errors: RowError[] = [];
  csvParser: Papa = inject(Papa);
  private readonly dialogRef = inject(MatDialogRef<TestUploadModalComponent, TestUploadModalResult>);
  removeExisting = false;

  async onFileSelected(event: FileInfo) {
    if (!event?.file) return;

    const file = await event?.file?.text();
    const results = this.csvParser.parse(file, {
      header: true,
      skipEmptyLines: true,
      error: (error) => {
        console.error('CSV parsing error:', error);
      },
    });
    const data = results?.data as Record<string, string>[];

    const errors: RowError[] = [];
    const testCases: TestCase[] = data
      .map((row, index) => {
        const chatHistory: ChatMessage[] = [];

        let hasError = false;
        for (let i = 1; i <= 3; i++) {
          const role = row[`role_${i}`]?.trim();
          const content = row[`content_${i}`]?.trim();
          if (!role && !content) {
            continue;
          }

          if (role !== 'user' && role !== 'assistant') {
            errors.push({
              rowIndex: index,
              message: `Invalid role "${role}" for chat history entry row ${index + 1}. Expected "user" or "assistant".`,
            });
            hasError = true;
            continue;
          }

          if (role && content) {
            chatHistory.push(new ChatMessage({ role: stringToRole(role), content: content }));
          } else {
            hasError = true;
            errors.push({
              rowIndex: index,
              message: `Row ${index + 1} has invalid chat history`,
            });
          }
        }
        if (hasError) {
          return;
        }

        if (chatHistory.length === 0) {
          errors.push({
            rowIndex: index,
            message: `No chat history found for row ${index + 1}.`,
          });
          return;
        }

        return new TestCase({
          name: row['name']?.trim() || `Unnamed-${index}`,
          chatHistory: chatHistory,
          expectation: row['expected']?.trim() || '',
        });
      })
      .filter((testCase) => !!testCase);

    this.testCases = testCases;
    this.errors = errors;
    return {
      TestCases: testCases,
      Errors: errors,
    };
  }

  close() {
    this.dialogRef.close();
  }

  save() {
    this.dialogRef.close({ cases: this.testCases, removeExisting: this.removeExisting });
  }
}

function stringToRole(role: string): ChatMessageRole {
  switch (role) {
    case 'user':
      return ChatMessageRole.CHAT_MESSAGE_ROLE_USER;
    case 'assistant':
      return ChatMessageRole.CHAT_MESSAGE_ROLE_ASSISTANT;
    default:
      return ChatMessageRole.CHAT_MESSAGE_ROLE_INVALID;
  }
}
