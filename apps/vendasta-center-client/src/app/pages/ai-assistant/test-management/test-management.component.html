<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>AI Assistant Testing</glxy-page-title>
  </glxy-page-toolbar>

  <glxy-page-wrapper widthPreset="default">
    <div class="row row-gutters">
      <div class="col col-xs-12">
        <div class="content">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Support AI Assistant Testing</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="actions">
                <button mat-raised-button color="primary" (click)="openUploadDialog()">Upload Test Cases</button>
                <button mat-button (click)="downloadCSVTemplate()">
                  <mat-icon>download</mat-icon>
                  Download Template
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
    <div class="row row-gutters">
      <div class="col col-xs-12">
        <app-test-case-display #table></app-test-case-display>
      </div>
    </div>

    <div class="row row-gutters">
      <div class="col col-xs-12">
        <div class="actions">
          <button mat-raised-button color="primary" (click)="runTests()">Run Tests</button>
          @if (testRunLoading) {
            <glxy-loading-spinner fullWidth="false" size="small"></glxy-loading-spinner>
          } @else if (testResultsLink) {
            <a [href]="testResultsLink" target="_blank">View Test Run</a>
          }
        </div>
      </div>
    </div>

    <div class="row row-gutters">
      <div class="col col-xs-12">
        <glxy-alert type="tip">
          Must be a member of developers&#64;vendasta.com or support&#64;vendasta.com to view test results
        </glxy-alert>
      </div>
    </div>

    <div class="row row-gutters">
      <div class="col col-xs-12">
        <div class="actions">
          <a
            href="https://console.cloud.google.com/cloud-build/builds?inv=1&invt=AbycTA&organizationId=39943578564&project=vendasta-support-bot"
            target="_blank"
            >View Run History</a
          >
        </div>
      </div>
    </div>
  </glxy-page-wrapper>
</glxy-page>
