import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { KnowledgeSourceManagementComponent } from '@galaxy/ai-knowledge';
import { AssistantSettingsComponent } from './assistant-settings/assistant-settings.component';
import { PromptManagementComponent } from './prompt-management/prompt-management.component';
import { PromptModuleManagementComponent } from './prompt-module-management/prompt-module-management.component';
import { FunctionManagementComponent } from './function-management/function-management.component';
import { GoalManagementComponent } from './goal-management/goal-management.component';
import { FeedbackComponent } from './feedback/feedback.component';
import { VoiceConfigurationComponent } from './voice-configuration/voice-configuration.component';
import { PendingChangesGuard } from '../../core/pending-changes.guard';
import { TestManagementComponent } from './test-management/test-management.component';

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: 'support',
        children: [
          {
            path: 'settings',
            pathMatch: 'full',
            component: AssistantSettingsComponent,
          },
          {
            path: 'knowledge-base',
            pathMatch: 'full',
            component: KnowledgeSourceManagementComponent,
          },
          {
            path: 'testing',
            pathMatch: 'full',
            component: TestManagementComponent,
          },
        ],
      },
      {
        path: 'prompt-management',
        pathMatch: 'full',
        component: PromptManagementComponent,
      },
      {
        path: 'prompt-module-management',
        pathMatch: 'full',
        component: PromptModuleManagementComponent,
      },
      {
        path: 'function-management',
        pathMatch: 'full',
        component: FunctionManagementComponent,
      },
      {
        path: 'goal-management',
        pathMatch: 'full',
        component: GoalManagementComponent,
      },
      {
        path: 'voice-configuration',
        pathMatch: 'full',
        component: VoiceConfigurationComponent,
        canDeactivate: [PendingChangesGuard],
      },
      {
        path: 'feedback',
        pathMatch: 'full',
        component: FeedbackComponent,
      },
    ]),
  ],
  exports: [RouterModule],
})
export class AiAssistantRoutingModule {}
