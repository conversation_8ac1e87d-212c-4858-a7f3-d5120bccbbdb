<!-- Atlas Navbar -->
<atlas-navbar [hideNotifications]="false" [hideCenters]="false" [dropdownItems]="dropdownItems">
  <atlas-item class="hide-on-md-up">
    <button mat-icon-button (click)="nav.toggle()" class="toggle-menu">
      <mat-icon class="nav-toggle">menu</mat-icon>
    </button>
  </atlas-item>
  <atlas-title interactable>Vendasta Center</atlas-title>
  <div class="spacer" style="margin-right: auto"></div>
</atlas-navbar>

<!-- Main page content -->
<glxy-nav #nav [fixedTopGap]="40" appName="vcc">
  <glxy-nav-panel>
    <glxy-nav-header>
      <div class="nav-header">
        <img
          src="https://vstatic-prod.apigateway.co/vendasta-center-client/assets/vendasta-logo.png"
          alt="logo"
          class="top-logo"
        />
        <span>Admin Center</span>
      </div>
    </glxy-nav-header>

    <glxy-nav-item route="/" icon="dashboard" [activeExactRoute]="true"> Dashboard </glxy-nav-item>

    <glxy-nav-item route="/feature-access/list-features" icon="lock"> Feature Flags </glxy-nav-item>

    <glxy-nav-item href="http://weblate.apigateway.co" icon="translate">
      Weblate
      <glxy-nav-item-accessory>
        <mat-icon>open_in_new</mat-icon>
      </glxy-nav-item-accessory>
    </glxy-nav-item>

    <glxy-nav-item route="/event-broker-events" icon="publish"> Event broker events </glxy-nav-item>

    <glxy-nav-item route="/notifications" icon="notifications"> Notifications </glxy-nav-item>

    <glxy-nav-item route="/loop" icon="all_inclusive"> The loop </glxy-nav-item>

    <glxy-nav-item route="/domain-management" icon="web"> App host domains </glxy-nav-item>

    <glxy-nav-item route="/api-docs" icon="api-docs"> API Documentation </glxy-nav-item>

    <glxy-nav-item-list icon="email">
      Email
      <glxy-nav-item-list-items>
        <glxy-nav-item route="/email/admin" icon="play_pause"> Master Control </glxy-nav-item>

        <glxy-nav-item route="/email" icon="stacked_email" [activeExactRoute]="true"> History </glxy-nav-item>

        <glxy-nav-item route="/email/users" icon="inbox"> Recipients </glxy-nav-item>

        <glxy-nav-item route="/email/senders" icon="send"> Senders </glxy-nav-item>

        <glxy-nav-item route="/email/sendgrid" icon="api"> Sendgrid </glxy-nav-item>
      </glxy-nav-item-list-items>
    </glxy-nav-item-list>

    <glxy-nav-item route="/phone-management" icon="phone"> Phone </glxy-nav-item>

    <glxy-nav-item-list icon="galaxy-ai-icon" [isSvg]="true">
      AI Assistants
      <glxy-nav-item-list-items>
        <glxy-nav-item-list icon="support_agent">
          Support AI
          <glxy-nav-item-list-items>
            <glxy-nav-item route="/ai-assistant/support/settings" icon="settings" class="nested-nav-item">
              Settings
            </glxy-nav-item>
            <glxy-nav-item route="/ai-assistant/support/knowledge-base" icon="database" class="nested-nav-item">
              Knowledge base
            </glxy-nav-item>
            <glxy-nav-item route="/ai-assistant/support/testing" icon="checklist" class="nested-nav-item">
              Testing
            </glxy-nav-item>
          </glxy-nav-item-list-items>
        </glxy-nav-item-list>

        <glxy-nav-item href="https://docs.vendasta-internal.com/ai-assistants/introduction/" icon="menu_book">
          Documentation
          <glxy-nav-item-accessory>
            <mat-icon>open_in_new</mat-icon>
          </glxy-nav-item-accessory>
        </glxy-nav-item>

        <glxy-nav-item route="/ai-assistant/prompt-management" icon="terminal"> Prompts (DEPRECATED) </glxy-nav-item>

        <glxy-nav-item route="/ai-assistant/prompt-module-management" icon="terminal"> Prompt modules </glxy-nav-item>

        <glxy-nav-item route="/ai-assistant/function-management" icon="psychology"> Functions </glxy-nav-item>

        <glxy-nav-item route="/ai-assistant/goal-management" icon="target"> Goals </glxy-nav-item>

        <glxy-nav-item route="/ai-assistant/voice-configuration" icon="phone"> Voice config</glxy-nav-item>

        <glxy-nav-item route="/ai-assistant/feedback" icon="feedback"> User Feedback</glxy-nav-item>
      </glxy-nav-item-list-items>
    </glxy-nav-item-list>

    <glxy-nav-item-list icon="passkey">
      Accounts and access
      <glxy-nav-item-list-items>
        <glxy-nav-item route="/service-accounts" icon="supervised_user_circle">
          Vendasta service accounts
        </glxy-nav-item>

        <glxy-nav-item href="https://iam-{{ getEnv() }}.apigateway.co/admin/users" icon="person_search">
          IAM users
          <glxy-nav-item-accessory>
            <mat-icon>open_in_new</mat-icon>
          </glxy-nav-item-accessory>
        </glxy-nav-item>

        <glxy-nav-item
          href="https://console.cloud.google.com/iam-admin/serviceaccounts?project=repcore-prod"
          icon="admin_panel_settings"
        >
          GCP service accounts
          <glxy-nav-item-accessory>
            <mat-icon>open_in_new</mat-icon>
          </glxy-nav-item-accessory>
        </glxy-nav-item>
      </glxy-nav-item-list-items>
    </glxy-nav-item-list>

    <glxy-nav-item-list icon="login">
      SSO
      <glxy-nav-item-list-items>
        <glxy-nav-item route="/integrations" icon="call_merge"> Service providers </glxy-nav-item>

        <glxy-nav-item route="/scopes" icon="pageview"> OAuth2 scopes </glxy-nav-item>

        <glxy-nav-item route="/playground" icon="policy"> OAuth2 playground </glxy-nav-item>
      </glxy-nav-item-list-items>
    </glxy-nav-item-list>

    <glxy-nav-item-list icon="description">
      Listings
      <glxy-nav-item-list-items>
        <glxy-nav-item route="/google-business-information"> Google </glxy-nav-item>

        <glxy-nav-item route="/facebook-page-information"> Facebook </glxy-nav-item>

        <glxy-nav-item route="/bing-information"> Bing </glxy-nav-item>

        <glxy-nav-item route="/apple-information"> Apple </glxy-nav-item>

        <glxy-nav-item route="/yext-partner-import" icon="download"> Yext partner import </glxy-nav-item>

        <glxy-nav-item route="/business-categories" icon="category_search"> Business categories </glxy-nav-item>

        <glxy-nav-item route="/business-category-mapping" icon="rule_settings">
          Business category mapping
        </glxy-nav-item>
      </glxy-nav-item-list-items>
    </glxy-nav-item-list>

    <glxy-nav-item-list icon="offline_bolt">
      Automations
      <glxy-nav-item-list-items>
        <glxy-nav-item route="/automations/task-definitions/list" icon="step"> Task definitions </glxy-nav-item>

        <glxy-nav-item route="/automations/event-trigger-definitions/list" icon="start">
          Event trigger definitions
        </glxy-nav-item>
      </glxy-nav-item-list-items>
    </glxy-nav-item-list>
  </glxy-nav-panel>

  <!-- Page Content -->
  <router-outlet />
</glxy-nav>
